import 'package:bitacora/domain/common/payment_status.dart';
import 'package:bitacora/domain/common/query/search_providers_repository_query.dart';
import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/presentation/entry_form/options/entry_form_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/money/entry_form_money_option.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/util/form/form_has_changes.dart';
import 'package:flutter/material.dart';

class EntryFormMoneyOptionController extends EntryFormOptionController
    with FormHasChanges {
  final ValueNotifier<PaymentStatus> paymentStatus =
      ValueNotifier(PaymentStatus.na);
  final ValueNotifier<bool> priceIsUnit = ValueNotifier(false);

  final TextEditingController income = TextEditingController();
  final TextEditingController expense = TextEditingController();
  final TextEditingController provider = TextEditingController();

  final TextEditingController quantity;

  EntryFormMoneyOptionController(
    super.liveEntry,
    this.quantity,
  ) {
    paymentStatus.addListener(_checkHasData);
    income.addListener(_checkHasData);
    expense.addListener(_checkHasData);
    provider.addListener(_checkHasData);

    monitorChanges();
  }

  @override
  Map<ValueNotifier, ValueObject?> get fields => {
        expense: liveEntry.value?.worklog?.costPrice,
        income: liveEntry.value?.worklog?.salePrice,
        provider: liveEntry.value?.worklog?.provider,
        if (liveEntry.value?.id != null)
          paymentStatus: liveEntry.value?.worklog?.paymentStatus,
      };

  @override
  void dispose() {
    paymentStatus.dispose();
    priceIsUnit.dispose();

    income.dispose();
    expense.dispose();
    provider.dispose();
    super.dispose();
  }

  void _checkHasData() {
    hasData.value = paymentStatus.value != PaymentStatus.na ||
        income.text.isNotEmpty ||
        expense.text.isNotEmpty ||
        provider.text.isNotEmpty;
  }

  @override
  MaterialColor get color => bitacoraGreen;

  @override
  IconData get icon => Icons.attach_money;

  @override
  Widget form(BuildContext context) => EntryFormMoneyOption(
      controller: this,
      suggestionsQuery: const SearchProvidersRepositoryQuery());
}
