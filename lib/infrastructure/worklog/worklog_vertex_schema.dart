import 'package:firebase_vertexai/firebase_vertexai.dart';

final worklogVertexSchema = Schema.object(properties: {
  'title': Schema.string(nullable: false),
  'quantity': Schema.number(nullable: true),
  'sublocation': Schema.string(nullable: false),
  'worklog_type': Schema.integer(nullable: false),
  'project_id': Schema.integer(nullable: true),
  'project_name': Schema.string(nullable: false),
});
