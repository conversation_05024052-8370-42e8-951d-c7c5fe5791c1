import 'dart:async';
import 'dart:convert';

import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:dio/dio.dart';
import 'package:flutter/cupertino.dart';
import 'package:shared_preferences/shared_preferences.dart';

class AppSupportInjector {
  factory AppSupportInjector() => inject(() => const AppSupportInjector._());

  Future<AppSupport> get() => inject(() => AppSupport._getInstance());

  const AppSupportInjector._();
}

class AppSupport {
  late String whatsAppContactNumber;
  static Completer<AppSupport>? _completer;

  AppSupport._(Map<String, dynamic> data)
      : whatsAppContactNumber = data['whatsapp'];

  @visibleForTesting
  factory AppSupport.fromMap(Map<String, dynamic> data) {
    return AppSupport._(data);
  }

  static Future<AppSupport> _getInstance() async {
    if (_completer == null) {
      final completer = Completer<AppSupport>();
      _completer = completer;
      try {
        final Map<String, dynamic> data = await _fetchFromServer() ??
            await _fetchFromSharedPreferences() ??
            _fromAppConfig();

        _saveToSharedPrefs(data);

        completer.complete(AppSupport._(data));
      } catch (e) {
        completer.completeError(e);
        final Future<AppSupport> appSupportFuture = completer.future;
        _completer = null;
        return appSupportFuture;
      }
    }

    return _completer!.future;
  }

  static Future<Map<String, dynamic>?> _fetchFromServer() async {
    logger.i('app-support: from server');
    final dio = Dio(BaseOptions(baseUrl: AppConfig().apiBaseUrl));

    try {
      final response = await dio.get('support');
      return response.data;
    } catch (_) {
      return null;
    }
  }

  static Future<Map<String, dynamic>?> _fetchFromSharedPreferences() async {
    logger.i('app-support: from prefs');
    const key = SharedPreferencesKeys.supportData;
    final prefs = await SharedPreferences.getInstance();

    final prefString = prefs.getString(key);
    if (prefString == null) {
      return null;
    }

    final map = jsonDecode(prefs.getString(key)!);
    return map;
  }

  static Map<String, dynamic> _fromAppConfig() {
    logger.i('app-support: from app config');
    return <String, dynamic>{
      'whatsapp': AppConfig().whatsAppContactNumber,
    };
  }

  static void _saveToSharedPrefs(Map<String, dynamic> data) async {
    logger.i('app-support: save to prefs');
    final prefs = await SharedPreferences.getInstance();

    await prefs.setString(SharedPreferencesKeys.supportData, jsonEncode(data));
  }
}
