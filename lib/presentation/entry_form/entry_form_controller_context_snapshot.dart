import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/template/template_cache.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';

class EntryFormControllerContextSnapshot extends ContextSnapshot {
  EntryFormControllerContextSnapshot(super.context);

  @override
  List<ValueShot> get valueShots => [
        ValueShot.provider<Repository>(),
        ValueShot.provider<ActiveLogDay>(),
        ValueShot.provider<ActiveProject>(),
        ValueShot.provider<AuthRepository>(),
        ValueShot.provider<ActiveOrganization>(),
        ValueShot.provider<ApiTranslator>(),
        ValueShot.provider<TemplateCache>(),
        ValueShot.navigator(),
      ];
}
