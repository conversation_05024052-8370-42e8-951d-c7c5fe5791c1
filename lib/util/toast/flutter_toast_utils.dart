import 'package:bitacora/util/inject/inject.dart';
import 'package:fluttertoast/fluttertoast.dart';

export 'package:fluttertoast/fluttertoast.dart';

class FluttertoastUtils {
  factory FluttertoastUtils() => inject(() => const FluttertoastUtils._());

  const FluttertoastUtils._();

  void showToast({required String msg, Toast? toastLength}) {
    Fluttertoast.showToast(msg: msg, toastLength: toastLength);
  }
}
