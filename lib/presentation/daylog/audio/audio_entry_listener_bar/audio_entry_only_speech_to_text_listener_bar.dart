import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_bar/audio_entry_listener_bar_swipe_to_cancel.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

const _kAnimationCurve = Curves.easeInOut;
const _kAnimationDuration = Duration(milliseconds: 100);
const _kMaxHeightScreenPercent = 0.6;

class AudioEntryOnlySpeechToTextBar extends StatelessWidget {
  final AudioEntryListenerController controller;
  final ScrollController _scrollController = ScrollController();

  AudioEntryOnlySpeechToTextBar({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Builder(builder: (context) {
      return ValueListenableBuilder<String>(
        valueListenable: controller.recognizedWords,
        builder: (context, recognizedWords, _) {
          SchedulerBinding.instance.addPostFrameCallback((_) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: _kAnimationDuration,
              curve: _kAnimationCurve,
            );
          });

          return Container(
            padding: const EdgeInsets.only(left: 16, right: 16.0, bottom: 16),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(24.0)),
              color: Theme.of(context).colorScheme.onSurface,
            ),
            child: Column(
              children: [
                AnimatedSize(
                  curve: _kAnimationCurve,
                  duration: _kAnimationDuration,
                  child: ConstrainedBox(
                    constraints: BoxConstraints(
                      maxHeight: MediaQuery.sizeOf(context).height *
                          _kMaxHeightScreenPercent,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        Flexible(
                          child: SingleChildScrollView(
                            controller: _scrollController,
                            child: Padding(
                              padding: const EdgeInsets.only(top: 16.0),
                              child: Row(
                                mainAxisAlignment:
                                    controller.recognizedWords.value.isEmpty
                                        ? MainAxisAlignment.center
                                        : MainAxisAlignment.start,
                                children: [
                                  Flexible(
                                    child: Text(
                                      controller.recognizedWords.value.isEmpty
                                          ? '•••'
                                          : controller.recognizedWords.value,
                                      style: Theme.of(context)
                                          .textTheme
                                          .headlineSmall!
                                          .copyWith(
                                            color:
                                                Theme.of(context).canvasColor,
                                          ),
                                      textAlign: TextAlign.left,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 8.0),
                ValueListenableBuilder<bool>(
                  valueListenable: controller.isAnchored,
                  builder: (_, isAnchored, __) => isAnchored
                      ? const SizedBox()
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            AudioEntryListenerBarSwipeToCancel(
                                controller: controller),
                          ],
                        ),
                ),
              ],
            ),
          );
        },
      );
    });
  }
}
