import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';
import 'package:flutter/material.dart';

class EntryFormPageNavigatorProps {
  final Entry? entry;
  final EntryFormPageContextSnapshot contextSnapshot;

  EntryFormPageNavigatorProps(BuildContext context, [this.entry])
      : contextSnapshot = EntryFormPageContextSnapshot(context);

  EntryFormPageNavigatorProps.fromContextSnapshot(this.contextSnapshot,
      [this.entry]);

  EntryFormPageNavigatorProps copyWith(Entry entry) =>
      EntryFormPageNavigatorProps.fromContextSnapshot(
        contextSnapshot,
        entry,
      );
}

class EntryFormPageContextSnapshot extends ContextSnapshot {
  EntryFormPageContextSnapshot(super.context);

  EntryFormPageContextSnapshot.fromContextSnapshot(super.snapshot)
      : super.fromSnapshot();

  @override
  List<ValueShot> get valueShots => [
        ValueShot.navigator(),
        ValueShot.provider<Repository>(),
        ValueShot.provider<ApiHelper>(),
        ValueShot.provider<ApiTranslator>(),
      ];
}
