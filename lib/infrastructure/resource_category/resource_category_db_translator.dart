import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/resource_category/resource_category.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_db_contract.dart';

class ResourceCategoryDbTranslator implements DbTranslator<ResourceCategory> {
  const ResourceCategoryDbTranslator();

  @override
  Set<Field> get nestedModelFields => resourceCategoryNestedModelFields;

  @override
  Future<ResourceCategory> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return ResourceCategory(
      id: fields[ResourceCategoryField.id]?.value(map),
      remoteId: fields[ResourceCategoryField.remoteId]?.value(map),
      name: fields[ResourceCategoryField.name]?.value(map),
      order: fields[ResourceCategoryField.order]?.value(map),
      parent: await fields[ResourceCategoryField.parent]?.nested(context, map),
      organization: await fields[ResourceCategoryField.organization]
          ?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, ResourceCategory model) async {
    final map = <String, dynamic>{};
    const contract = ResourceCategoryDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.name, model.name);
    addField(map, contract.order, model.order);
    await saveNestedModel<ResourceCategory>(context, map, contract.parentId,
        context.db.resourceCategory, model.parent);
    await saveNestedModel<Organization>(context, map, contract.organizationId,
        context.db.organization, model.organization);
    return map;
  }
}
