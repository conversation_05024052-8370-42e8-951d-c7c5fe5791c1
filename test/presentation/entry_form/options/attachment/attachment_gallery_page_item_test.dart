import 'dart:async';

import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/attachment_gallery_page_item.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/image/attachment_image_photo_view.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:file/file.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:photo_view/photo_view.dart';

import '../../../../domain/common/mocks.dart';
import '../../../../mocks.dart';
import '../../../../mocktail_fallback_values.dart';
import '../../../../test_util.dart';
import '../../../../util/attachment/mocks.dart';
import '../../../../util/file_system/mocks.dart';
import 'mocks.dart';

void main() {
  group('$AttachmentGalleryPageItem tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
      PathProviderPlatform.instance = MockPathProvider();
    });

    testWidgets('LiveAttachment null value', (tester) async {
      await tester.pumpWidget(testAttachmentGalleryPageItem());

      expect(find.byType(SizedBox), findsOneWidget);
      expect(find.byType(PhotoView), findsNothing);
      expect(find.byIcon(Icons.cloud_outlined), findsNothing);
      expect(find.byIcon(Icons.cloud_off), findsNothing);
      expect(find.byIcon(Icons.file_present), findsNothing);
    });

    testWidgets('Undownloaded Item is uploaded', (tester) async {
      final attachment = Attachment(
        name: AttachmentName('test.txt'),
        isDownloaded: AttachmentIsDownloaded(false),
        transferState:
            AttachmentTransferStateValueObject(AttachmentTransferState.na),
        isUploaded: AttachmentIsUploaded(true),
      );

      await tester
          .pumpWidget(testAttachmentGalleryPageItem(attachment: attachment));

      expect(find.byIcon(Icons.cloud_outlined), findsOneWidget);
      expect(find.text(attachment.name!.value), findsOneWidget);
      expect(find.byType(PlatformCircularProgressIndicator), findsNothing);
      expect(find.text('Download'), findsOneWidget);
    });

    testWidgets('Undownloaded Item in progress', (tester) async {
      final attachment = Attachment(
        name: AttachmentName('test.txt'),
        isDownloaded: AttachmentIsDownloaded(false),
        transferState: AttachmentTransferStateValueObject(
            AttachmentTransferState.inProgress),
        isUploaded: AttachmentIsUploaded(true),
      );

      await tester
          .pumpWidget(testAttachmentGalleryPageItem(attachment: attachment));

      expect(find.byIcon(Icons.cloud_outlined), findsOneWidget);
      expect(find.text(attachment.name!.value), findsOneWidget);
      expect(find.byType(PlatformCircularProgressIndicator), findsOneWidget);
      expect(find.byType(TextButton), findsNothing);
    });

    testWidgets('Undownloaded Item no uploaded', (tester) async {
      final attachment = Attachment(
        name: AttachmentName('test.jpg'),
        isDownloaded: AttachmentIsDownloaded(false),
        transferState:
            AttachmentTransferStateValueObject(AttachmentTransferState.na),
        isUploaded: AttachmentIsUploaded(false),
      );

      await tester
          .pumpWidget(testAttachmentGalleryPageItem(attachment: attachment));

      expect(find.byIcon(Icons.cloud_off), findsOneWidget);
      expect(find.text(attachment.name!.value), findsOneWidget);
      expect(find.byType(PlatformCircularProgressIndicator), findsNothing);
      expect(find.text('Download'), findsNothing);
    });

    testWidgets('Downloaded Image', (tester) async {
      await tester.runAsync(() async {
        final fileSystem = await mockFileSystem();
        final image = await createMockImage(fileSystem, 'ic_bitacora.png');
        final attachment = Attachment(
          name: AttachmentName(image.basename),
          isDownloaded: AttachmentIsDownloaded(true),
          path: AttachmentPath(image.path),
        );

        final attachmentStorageUtils = _mockAttachmentStorageUtils(
            futurePath: Future.value('ic_bitacora.png'));

        await withInjected2<FileSystem, AttachmentUtils>(
            fileSystem, attachmentStorageUtils, () async {
          await tester.pumpWidget(
              testAttachmentGalleryPageItem(attachment: attachment));
          await tester.pump();
        });
      });
      expect(find.byType(AttachmentImagePhotoView), findsOneWidget);
    });

    testWidgets('Downloaded a non-image file', (tester) async {
      final attachment = Attachment(
        name: AttachmentName('testament.txt'),
        isDownloaded: AttachmentIsDownloaded(true),
        path: const AttachmentPath('texts/testament.txt'),
      );
      final attachmentUtils = _mockAttachmentStorageUtils(
          futurePath: Future.value('/images/testament.txt'));

      await withInjected<AttachmentUtils>(
        attachmentUtils,
        () => tester.pumpWidget(
          testAttachmentGalleryPageItem(attachment: attachment),
        ),
      );

      await tester.pump();

      expect(find.byType(PhotoView), findsNothing);
      expect(find.byIcon(Icons.file_present), findsOneWidget);
      expect(find.text(attachment.name!.displayValue), findsOneWidget);
      expect(find.byType(SizedBox), findsNWidgets(2));
      expect(find.text('Open'), findsOneWidget);
    });

    testWidgets('Auto Download image', (tester) async {
      var attachment = Attachment(
        name: AttachmentName('image.jpg'),
        isDownloaded: AttachmentIsDownloaded(false),
        isUploaded: AttachmentIsUploaded(true),
        path: const AttachmentPath('images/image.jpg'),
        transferState:
            AttachmentTransferStateValueObject(AttachmentTransferState.na),
      );

      mockOnDownloadAttachmentCallback(_, __) {
        attachment = Attachment(
          name: AttachmentName('image.jpg'),
          isDownloaded: AttachmentIsDownloaded(false),
          isUploaded: AttachmentIsUploaded(true),
          path: const AttachmentPath('images/image.jpg'),
          transferState: AttachmentTransferStateValueObject(
              AttachmentTransferState.inProgress),
        );
      }

      final attachmentStorageUtils = _mockAttachmentStorageUtils(
          futurePath: Future.value('/images/image.jpg'));

      await withInjected<AttachmentUtils>(
        attachmentStorageUtils,
        () => tester.pumpWidget(
          testAttachmentGalleryPageItem(
            attachment: attachment,
            onDownloadAttachment: mockOnDownloadAttachmentCallback,
          ),
        ),
      );

      await tester.pumpAndSettle();

      expect(
          attachment.transferState?.value, AttachmentTransferState.inProgress);
    });

    testWidgets('Auto Download image run callback only one time',
        (tester) async {
      var onDownloadTimes = 0;
      var attachment = Attachment(
        name: AttachmentName('image.jpg'),
        isDownloaded: AttachmentIsDownloaded(false),
        isUploaded: AttachmentIsUploaded(true),
        path: const AttachmentPath('images/image.jpg'),
        transferState:
            AttachmentTransferStateValueObject(AttachmentTransferState.na),
      );
      final mutations = StreamController<Mutation<Attachment>>.broadcast();
      final liveAttachment = LiveModel<Attachment>(
        attachment,
        mutations.stream,
        () => Future.value(attachment),
      );
      mockOnDownloadAttachmentCallback(_, __) {
        attachment = Attachment(
          name: AttachmentName('image.jpg'),
          isDownloaded: AttachmentIsDownloaded(false),
          isUploaded: AttachmentIsUploaded(true),
          path: const AttachmentPath('images/image.jpg'),
          transferState:
              AttachmentTransferStateValueObject(AttachmentTransferState.na),
        );
        onDownloadTimes++;
      }

      final attachmentStorageUtils = _mockAttachmentStorageUtils(
          futurePath: Future.value('/images/image.jpg'));
      await withInjected<AttachmentUtils>(
        attachmentStorageUtils,
        () => tester.pumpWidget(
          testAttachmentGalleryPageItem(
            attachment: attachment,
            onDownloadAttachment: mockOnDownloadAttachmentCallback,
            liveAttachment: liveAttachment,
          ),
        ),
      );
      await tester.pumpAndSettle();

      mutations.sink.add(Mutation(type: MutationType.unknown));
      await tester.pumpAndSettle();

      expect(onDownloadTimes, 1);
    });
  });
}

Widget testAttachmentGalleryPageItem({
  Attachment? attachment,
  DownloadAttachmentCallback? onDownloadAttachment,
  LiveModel<Attachment>? liveAttachment,
}) {
  liveAttachment ??= mockLiveModel<Attachment>(attachment);
  final controller =
      mockAttachmentGalleryPageItemController(liveAttachment: liveAttachment);
  return MaterialApp(
    home: Localizations(
      locale: const Locale('en'),
      delegates: AppLocalizations.localizationsDelegates,
      child: Scaffold(
        body: AttachmentGalleryPageItem(
          controller: controller,
          onDownloadAttachment: onDownloadAttachment ?? (_, __) {},
          isShowingDoodleEditor: ValueNotifier(false),
        ),
      ),
    ),
  );
}

AttachmentUtils _mockAttachmentStorageUtils({Future<String?>? futurePath}) {
  final mock = MockAttachmentUtils();
  when(() => mock.getAbsolutePath(any()))
      .thenAnswer((_) => futurePath ?? Future.value('/absolute/path'));
  return mock;
}
