name: open_filex
description: A plug-in that can call native APP to open files with string result in flutter, support iOS(UTI) / android(intent) / PC(ffi) / web(dart:html)
version: 4.6.0
homepage: https://github.com/javaherisaber/open_file

dependencies:
  flutter:
    sdk: flutter
  ffi: ^2.0.1

dev_dependencies:
  flutter_lints: ^2.0.1

environment:
  sdk: '>=2.17.0 <4.0.0'
  flutter: ">=3.0.0"

# The following section is specific to Flutter.
flutter:
  plugin:
    platforms:
      android:
        package: com.crazecoder.openfile
        pluginClass: OpenFilePlugin
      ios:
        pluginClass: OpenFilePlugin
