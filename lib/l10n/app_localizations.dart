import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_en.dart';
import 'app_localizations_es.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale) : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations? of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations);
  }

  static const LocalizationsDelegate<AppLocalizations> delegate = _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates = <LocalizationsDelegate<dynamic>>[
    delegate,
    GlobalMaterialLocalizations.delegate,
    GlobalCupertinoLocalizations.delegate,
    GlobalWidgetsLocalizations.delegate,
  ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('en'),
    Locale('es')
  ];

  /// The application motto
  ///
  /// In en, this message translates to:
  /// **'Your business\'s diary'**
  String get appMotto;

  /// No description provided for @yes.
  ///
  /// In en, this message translates to:
  /// **'Yes'**
  String get yes;

  /// No description provided for @no.
  ///
  /// In en, this message translates to:
  /// **'No'**
  String get no;

  /// No description provided for @monday.
  ///
  /// In en, this message translates to:
  /// **'Monday'**
  String get monday;

  /// No description provided for @tuesday.
  ///
  /// In en, this message translates to:
  /// **'Tuesday'**
  String get tuesday;

  /// No description provided for @wednesday.
  ///
  /// In en, this message translates to:
  /// **'Wednesday'**
  String get wednesday;

  /// No description provided for @thursday.
  ///
  /// In en, this message translates to:
  /// **'Thursday'**
  String get thursday;

  /// No description provided for @friday.
  ///
  /// In en, this message translates to:
  /// **'Friday'**
  String get friday;

  /// No description provided for @saturday.
  ///
  /// In en, this message translates to:
  /// **'Saturday'**
  String get saturday;

  /// No description provided for @sunday.
  ///
  /// In en, this message translates to:
  /// **'Sunday'**
  String get sunday;

  /// No description provided for @days.
  ///
  /// In en, this message translates to:
  /// **'days'**
  String get days;

  /// No description provided for @lastWeek.
  ///
  /// In en, this message translates to:
  /// **'Last week'**
  String get lastWeek;

  /// No description provided for @yesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get yesterday;

  /// No description provided for @anHourAgo.
  ///
  /// In en, this message translates to:
  /// **'An hour ago'**
  String get anHourAgo;

  /// No description provided for @aMinuteAgo.
  ///
  /// In en, this message translates to:
  /// **'A minute ago'**
  String get aMinuteAgo;

  /// No description provided for @justNow.
  ///
  /// In en, this message translates to:
  /// **'Just now'**
  String get justNow;

  /// No description provided for @anHourLeft.
  ///
  /// In en, this message translates to:
  /// **'An 1h left'**
  String get anHourLeft;

  /// No description provided for @aMinuteLeft.
  ///
  /// In en, this message translates to:
  /// **'A 1m ago'**
  String get aMinuteLeft;

  /// No description provided for @email.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get email;

  /// No description provided for @emails.
  ///
  /// In en, this message translates to:
  /// **'Emails'**
  String get emails;

  /// No description provided for @password.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get password;

  /// No description provided for @login.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get login;

  /// No description provided for @logout.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logout;

  /// No description provided for @signup.
  ///
  /// In en, this message translates to:
  /// **'Signup'**
  String get signup;

  /// No description provided for @reset.
  ///
  /// In en, this message translates to:
  /// **'Reset'**
  String get reset;

  /// No description provided for @back.
  ///
  /// In en, this message translates to:
  /// **'Back'**
  String get back;

  /// No description provided for @cancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get cancel;

  /// No description provided for @grant.
  ///
  /// In en, this message translates to:
  /// **'Grant'**
  String get grant;

  /// No description provided for @edit.
  ///
  /// In en, this message translates to:
  /// **'Edit'**
  String get edit;

  /// No description provided for @finish.
  ///
  /// In en, this message translates to:
  /// **'Finish'**
  String get finish;

  /// No description provided for @update.
  ///
  /// In en, this message translates to:
  /// **'Update'**
  String get update;

  /// No description provided for @save.
  ///
  /// In en, this message translates to:
  /// **'Save'**
  String get save;

  /// No description provided for @delete.
  ///
  /// In en, this message translates to:
  /// **'Delete'**
  String get delete;

  /// No description provided for @type.
  ///
  /// In en, this message translates to:
  /// **'Type'**
  String get type;

  /// No description provided for @date.
  ///
  /// In en, this message translates to:
  /// **'Date'**
  String get date;

  /// No description provided for @birthday.
  ///
  /// In en, this message translates to:
  /// **'Birthday'**
  String get birthday;

  /// No description provided for @status.
  ///
  /// In en, this message translates to:
  /// **'Status'**
  String get status;

  /// No description provided for @progress.
  ///
  /// In en, this message translates to:
  /// **'Progress'**
  String get progress;

  /// No description provided for @completed.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get completed;

  /// No description provided for @failed.
  ///
  /// In en, this message translates to:
  /// **'Failed'**
  String get failed;

  /// No description provided for @download.
  ///
  /// In en, this message translates to:
  /// **'Download'**
  String get download;

  /// No description provided for @open.
  ///
  /// In en, this message translates to:
  /// **'Open'**
  String get open;

  /// No description provided for @openWith.
  ///
  /// In en, this message translates to:
  /// **'Open with'**
  String get openWith;

  /// No description provided for @tag.
  ///
  /// In en, this message translates to:
  /// **'Tag'**
  String get tag;

  /// No description provided for @tags.
  ///
  /// In en, this message translates to:
  /// **'Tags'**
  String get tags;

  /// No description provided for @sync.
  ///
  /// In en, this message translates to:
  /// **'Sync'**
  String get sync;

  /// No description provided for @syncing.
  ///
  /// In en, this message translates to:
  /// **'Syncing'**
  String get syncing;

  /// No description provided for @welcome.
  ///
  /// In en, this message translates to:
  /// **'Welcome'**
  String get welcome;

  /// No description provided for @address.
  ///
  /// In en, this message translates to:
  /// **'Address'**
  String get address;

  /// No description provided for @addresses.
  ///
  /// In en, this message translates to:
  /// **'Addresses'**
  String get addresses;

  /// No description provided for @latitude.
  ///
  /// In en, this message translates to:
  /// **'Latitude'**
  String get latitude;

  /// No description provided for @longitude.
  ///
  /// In en, this message translates to:
  /// **'Longitude'**
  String get longitude;

  /// No description provided for @send.
  ///
  /// In en, this message translates to:
  /// **'Send'**
  String get send;

  /// No description provided for @sending.
  ///
  /// In en, this message translates to:
  /// **'Sending'**
  String get sending;

  /// No description provided for @sent.
  ///
  /// In en, this message translates to:
  /// **'Sent'**
  String get sent;

  /// No description provided for @share.
  ///
  /// In en, this message translates to:
  /// **'Share'**
  String get share;

  /// No description provided for @sharing.
  ///
  /// In en, this message translates to:
  /// **'Sharing'**
  String get sharing;

  /// No description provided for @staff.
  ///
  /// In en, this message translates to:
  /// **'Staff'**
  String get staff;

  /// No description provided for @simple.
  ///
  /// In en, this message translates to:
  /// **'Simple'**
  String get simple;

  /// No description provided for @switch_.
  ///
  /// In en, this message translates to:
  /// **'Switch'**
  String get switch_;

  /// No description provided for @invite.
  ///
  /// In en, this message translates to:
  /// **'Invite'**
  String get invite;

  /// No description provided for @inProgress.
  ///
  /// In en, this message translates to:
  /// **'In Progress...'**
  String get inProgress;

  /// No description provided for @resend.
  ///
  /// In en, this message translates to:
  /// **'Resend'**
  String get resend;

  /// No description provided for @deactivate.
  ///
  /// In en, this message translates to:
  /// **'Deactivate'**
  String get deactivate;

  /// No description provided for @audioRecording.
  ///
  /// In en, this message translates to:
  /// **'Audio Recording'**
  String get audioRecording;

  /// No description provided for @voiceRecognition.
  ///
  /// In en, this message translates to:
  /// **'Voice Recognition'**
  String get voiceRecognition;

  /// No description provided for @speechNote.
  ///
  /// In en, this message translates to:
  /// **'Speech Note'**
  String get speechNote;

  /// No description provided for @trackerEntry.
  ///
  /// In en, this message translates to:
  /// **'Tracked Entry'**
  String get trackerEntry;

  /// No description provided for @other.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get other;

  /// No description provided for @otherSingular.
  ///
  /// In en, this message translates to:
  /// **'Other'**
  String get otherSingular;

  /// No description provided for @feed.
  ///
  /// In en, this message translates to:
  /// **'Feed'**
  String get feed;

  /// No description provided for @posts.
  ///
  /// In en, this message translates to:
  /// **'Posts'**
  String get posts;

  /// No description provided for @resources.
  ///
  /// In en, this message translates to:
  /// **'Resources'**
  String get resources;

  /// No description provided for @readMore.
  ///
  /// In en, this message translates to:
  /// **'Read more'**
  String get readMore;

  /// No description provided for @report.
  ///
  /// In en, this message translates to:
  /// **'Report'**
  String get report;

  /// No description provided for @reports.
  ///
  /// In en, this message translates to:
  /// **'Reports'**
  String get reports;

  /// No description provided for @spreadsheet.
  ///
  /// In en, this message translates to:
  /// **'Spreadsheet'**
  String get spreadsheet;

  /// No description provided for @creating.
  ///
  /// In en, this message translates to:
  /// **'Creating'**
  String get creating;

  /// No description provided for @processing.
  ///
  /// In en, this message translates to:
  /// **'Processing'**
  String get processing;

  /// No description provided for @opening.
  ///
  /// In en, this message translates to:
  /// **'Opening'**
  String get opening;

  /// No description provided for @today.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get today;

  /// No description provided for @elapsed.
  ///
  /// In en, this message translates to:
  /// **'Elapsed'**
  String get elapsed;

  /// No description provided for @start.
  ///
  /// In en, this message translates to:
  /// **'Start'**
  String get start;

  /// No description provided for @stop.
  ///
  /// In en, this message translates to:
  /// **'Stop'**
  String get stop;

  /// No description provided for @notifications.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notifications;

  /// No description provided for @title.
  ///
  /// In en, this message translates to:
  /// **'Title'**
  String get title;

  /// No description provided for @subtitle.
  ///
  /// In en, this message translates to:
  /// **'Subtitle'**
  String get subtitle;

  /// No description provided for @text.
  ///
  /// In en, this message translates to:
  /// **'Text'**
  String get text;

  /// No description provided for @view.
  ///
  /// In en, this message translates to:
  /// **'View'**
  String get view;

  /// No description provided for @all.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get all;

  /// No description provided for @allWithTemplates.
  ///
  /// In en, this message translates to:
  /// **'All (with Templates)'**
  String get allWithTemplates;

  /// No description provided for @clear.
  ///
  /// In en, this message translates to:
  /// **'Clear'**
  String get clear;

  /// No description provided for @clearAll.
  ///
  /// In en, this message translates to:
  /// **'Clear All'**
  String get clearAll;

  /// No description provided for @assignee.
  ///
  /// In en, this message translates to:
  /// **'Assignee'**
  String get assignee;

  /// No description provided for @signee.
  ///
  /// In en, this message translates to:
  /// **'Signee'**
  String get signee;

  /// No description provided for @creator.
  ///
  /// In en, this message translates to:
  /// **'Creator'**
  String get creator;

  /// No description provided for @client.
  ///
  /// In en, this message translates to:
  /// **'Client'**
  String get client;

  /// No description provided for @include.
  ///
  /// In en, this message translates to:
  /// **'Include'**
  String get include;

  /// No description provided for @customize.
  ///
  /// In en, this message translates to:
  /// **'Customize'**
  String get customize;

  /// No description provided for @template.
  ///
  /// In en, this message translates to:
  /// **'Template'**
  String get template;

  /// No description provided for @retry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get retry;

  /// No description provided for @retrieve.
  ///
  /// In en, this message translates to:
  /// **'Retrieve'**
  String get retrieve;

  /// No description provided for @project.
  ///
  /// In en, this message translates to:
  /// **'Project'**
  String get project;

  /// No description provided for @company.
  ///
  /// In en, this message translates to:
  /// **'Company'**
  String get company;

  /// No description provided for @area.
  ///
  /// In en, this message translates to:
  /// **'Area'**
  String get area;

  /// No description provided for @approved.
  ///
  /// In en, this message translates to:
  /// **'Approved'**
  String get approved;

  /// No description provided for @rejected.
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get rejected;

  /// No description provided for @received.
  ///
  /// In en, this message translates to:
  /// **'Received'**
  String get received;

  /// No description provided for @new_.
  ///
  /// In en, this message translates to:
  /// **'New'**
  String get new_;

  /// No description provided for @create.
  ///
  /// In en, this message translates to:
  /// **'Create'**
  String get create;

  /// No description provided for @missing.
  ///
  /// In en, this message translates to:
  /// **'Missing'**
  String get missing;

  /// No description provided for @empty.
  ///
  /// In en, this message translates to:
  /// **'Empty'**
  String get empty;

  /// No description provided for @tryAgain.
  ///
  /// In en, this message translates to:
  /// **'Try Again'**
  String get tryAgain;

  /// No description provided for @longPress.
  ///
  /// In en, this message translates to:
  /// **'Long Press'**
  String get longPress;

  /// No description provided for @discard.
  ///
  /// In en, this message translates to:
  /// **'Discard'**
  String get discard;

  /// No description provided for @entries.
  ///
  /// In en, this message translates to:
  /// **'Entries'**
  String get entries;

  /// No description provided for @home.
  ///
  /// In en, this message translates to:
  /// **'Home'**
  String get home;

  /// No description provided for @office.
  ///
  /// In en, this message translates to:
  /// **'Office'**
  String get office;

  /// No description provided for @work.
  ///
  /// In en, this message translates to:
  /// **'Work'**
  String get work;

  /// No description provided for @mobile.
  ///
  /// In en, this message translates to:
  /// **'Mobile'**
  String get mobile;

  /// No description provided for @main.
  ///
  /// In en, this message translates to:
  /// **'Main'**
  String get main;

  /// No description provided for @oneHour.
  ///
  /// In en, this message translates to:
  /// **'1 hour'**
  String get oneHour;

  /// To indicate number of hours
  ///
  /// In en, this message translates to:
  /// **'{n} hours'**
  String nHours(int n);

  /// No description provided for @group.
  ///
  /// In en, this message translates to:
  /// **'Group'**
  String get group;

  /// No description provided for @ungroup.
  ///
  /// In en, this message translates to:
  /// **'Ungroup'**
  String get ungroup;

  /// To indicate the report creation date in days.
  ///
  /// In en, this message translates to:
  /// **'{n} days ago'**
  String daysAgo(int n);

  /// To indicate the report creation date in hours.
  ///
  /// In en, this message translates to:
  /// **'{n} hours ago'**
  String hoursAgo(int n);

  /// To indicate the report creation date in minutes.
  ///
  /// In en, this message translates to:
  /// **'{n} minutes ago'**
  String minutesAgo(int n);

  /// To indicate the report creation date in seconds.
  ///
  /// In en, this message translates to:
  /// **'{n} seconds ago'**
  String secondsAgo(int n);

  /// To indicate the user location tracking left time in hours.
  ///
  /// In en, this message translates to:
  /// **'{n}h left'**
  String hoursLeft(int n);

  /// To indicate user location tracking left time in minutes.
  ///
  /// In en, this message translates to:
  /// **'{n}m left'**
  String minutesLeft(int n);

  /// To indicate user location tracking left time in  seconds.
  ///
  /// In en, this message translates to:
  /// **'{n}s left'**
  String secondsLeft(int n);

  /// To indicate user location tracking will stop soon
  ///
  /// In en, this message translates to:
  /// **'Will stop soon'**
  String get willStopSoon;

  /// Location Tracking consent dialog title
  ///
  /// In en, this message translates to:
  /// **'Permission Required'**
  String get permissionRequired;

  /// Location Tracking consent dialog text
  ///
  /// In en, this message translates to:
  /// **'Bitacora.io collects location data to enable route tracking initiated by the user even when app is closed or not in use.'**
  String get trackingConsent;

  /// Title of a group's detail page
  ///
  /// In en, this message translates to:
  /// **'{n} related entries'**
  String relatedEntries(int n);

  /// Title for the report form page when creating a new report.
  ///
  /// In en, this message translates to:
  /// **'New Report'**
  String get newReport;

  /// Include section caption in the report creation form page. Indicates which entry fields will be included in report.
  ///
  /// In en, this message translates to:
  /// **'Select which entry fields to include in your report.'**
  String get reportIncludeCaption;

  /// Customize section caption in the report creation form page.
  ///
  /// In en, this message translates to:
  /// **'Add a header to your report.'**
  String get reportCustomizeCaption;

  /// Toast message when user wants to open a report file that does not exist.
  ///
  /// In en, this message translates to:
  /// **'The report file could not be found.'**
  String get reportFileCouldNotBeFound;

  /// No app to open external file toast message
  ///
  /// In en, this message translates to:
  /// **'No app found to open this file.'**
  String get noAppToOpen;

  /// noun: sorting or blocking access to certain data
  ///
  /// In en, this message translates to:
  /// **'Filters'**
  String get filters;

  /// Daylog filter panel title
  ///
  /// In en, this message translates to:
  /// **'Add Filters'**
  String get addFilters;

  /// No filters indicator of daylog appbar
  ///
  /// In en, this message translates to:
  /// **'No active filters'**
  String get noActiveFilters;

  /// Filters section caption in the report creation form page.
  ///
  /// In en, this message translates to:
  /// **'Apply filters to view selected entries.'**
  String get reportFiltersCaption;

  /// Used to label an external user (outside of organization). Usually email is specified. i.e. -External- [<EMAIL>]
  ///
  /// In en, this message translates to:
  /// **'-External-'**
  String get externalSignee;

  /// label for different signature types: i.e. approved, rejected, received
  ///
  /// In en, this message translates to:
  /// **'Signature Type'**
  String get signatureType;

  /// noun: act of searching
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get search;

  /// label describing what to type in search fields
  ///
  /// In en, this message translates to:
  /// **'Input exact word or phrase for search...'**
  String get searchInput;

  /// shows when there are no results in a search
  ///
  /// In en, this message translates to:
  /// **'No results'**
  String get noResults;

  /// noun: the way in which something is arranged or set out.
  ///
  /// In en, this message translates to:
  /// **'Format'**
  String get format;

  /// noun: as in time period.
  ///
  /// In en, this message translates to:
  /// **'Period'**
  String get period;

  /// Number of days. Shouldn't be used for a single day.
  ///
  /// In en, this message translates to:
  /// **'{n} days'**
  String nDays(int n);

  /// Distance traveled text of location tracking entry.
  ///
  /// In en, this message translates to:
  /// **'Distance traveled: {n} {unit}'**
  String distanceTraveled(String n, String unit);

  /// Average speed text of location tracking entry.
  ///
  /// In en, this message translates to:
  /// **'Average speed: {n} {unit}'**
  String averageSpeed(double n, String unit);

  /// GPS (latitude, longitude) location
  ///
  /// In en, this message translates to:
  /// **'Location'**
  String get location;

  /// Typically shows in a toast because user has no connection.
  ///
  /// In en, this message translates to:
  /// **'There was an issue with the network.'**
  String get networkError;

  /// Asks if password was forgotten.
  ///
  /// In en, this message translates to:
  /// **'Forgot password?'**
  String get forgotPassword;

  /// Action to reset the user's password.
  ///
  /// In en, this message translates to:
  /// **'Reset password'**
  String get resetPassword;

  /// Asks the user if they don't have an account.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccount;

  /// Used for the user's name.
  ///
  /// In en, this message translates to:
  /// **'Name'**
  String get name;

  /// Used for the user's phone number.
  ///
  /// In en, this message translates to:
  /// **'Phone number'**
  String get phoneNumber;

  /// Used for the user's phone number section.
  ///
  /// In en, this message translates to:
  /// **'Phone numbers'**
  String get phoneNumbers;

  /// No description provided for @organizations.
  ///
  /// In en, this message translates to:
  /// **'Organizations'**
  String get organizations;

  /// Used for badge labeling org as Pro.
  ///
  /// In en, this message translates to:
  /// **'Pro Plan'**
  String get proPlan;

  /// Used for labeling failed user invitation.
  ///
  /// In en, this message translates to:
  /// **'⚠️ Error sending invite'**
  String get failedUserInvite;

  /// Toast text to notify that it was copied to the clipboard (keep short).
  ///
  /// In en, this message translates to:
  /// **'Copied {text}.'**
  String copyClipboard(String text);

  /// The current/active/selected) organization.
  ///
  /// In en, this message translates to:
  /// **'Active Organization'**
  String get activeOrganization;

  /// Menu option for exporting a pdf or excel report.
  ///
  /// In en, this message translates to:
  /// **'PDF or Excel'**
  String get pdfOrExcel;

  /// Menu option subtitle for exporting a pdf report.
  ///
  /// In en, this message translates to:
  /// **'Create & Share PDF'**
  String get createAndShareReport;

  /// Title question for the report screen where the user will be directed on how to export their data and create reports.
  ///
  /// In en, this message translates to:
  /// **'Looking to create reports or export your data?'**
  String get reportScreenTitle;

  /// Message prompting the user to visit the website or contact for help regarding exporting information or creating reports.
  ///
  /// In en, this message translates to:
  /// **'Visit the Bitacora.io web panel to manage your information.'**
  String get reportScreenMessage;

  /// Describes a regular report template (intended for daily use)
  ///
  /// In en, this message translates to:
  /// **'Regular / Day'**
  String get reportTemplateBaseDescription;

  /// Describes a compact report template (intended for hourly use)
  ///
  /// In en, this message translates to:
  /// **'Compact / Hr'**
  String get reportTemplateCompactDescription;

  /// Describes a minimal compact report template
  ///
  /// In en, this message translates to:
  /// **'Compact Minimal'**
  String get reportTemplateCompactMinimalDescription;

  /// Describes a two-columned compact report template
  ///
  /// In en, this message translates to:
  /// **'Compact / 2 Columns'**
  String get reportTemplateCompactTwoColumnsDescription;

  /// Top Snackbar text used when report creation fails.
  ///
  /// In en, this message translates to:
  /// **'Failed to create report.'**
  String get reportFailedToCreate;

  /// Menu subtitle for sync action. Informs the last sync update time.
  ///
  /// In en, this message translates to:
  /// **'Updated {updatedAt}'**
  String syncDescription(String updatedAt);

  /// Menu subtitle for sync action. Informs when session is invalid.
  ///
  /// In en, this message translates to:
  /// **'Login Required'**
  String get loginRequired;

  /// Menu subtitle for sync action. Informs when app version is invalid.
  ///
  /// In en, this message translates to:
  /// **'Update Required'**
  String get updateRequired;

  /// Menu subtitle to report that synchronization is incomplete.
  ///
  /// In en, this message translates to:
  /// **'There are logs that have not yet synced. Make sure you have good internet and tap Sync.'**
  String get incompleteSync;

  /// Report creation warning title message when app has pending uploads.
  ///
  /// In en, this message translates to:
  /// **'Sync in Progress.'**
  String get syncInProgress;

  /// Report creation warning body message when app has pending uploads.
  ///
  /// In en, this message translates to:
  /// **'There still appear to be records to be synced in your organization, your report may be incomplete.'**
  String get syncReport;

  /// Shows when the user needs to re enter their password.
  ///
  /// In en, this message translates to:
  /// **'The session has expired.\nEnter your password to sync your data.'**
  String get sessionExpired;

  /// Shows when the user needs to update the app.
  ///
  /// In en, this message translates to:
  /// **'This app version is no longer supported.\nUpdate to sync your data.'**
  String get apiExpired;

  /// Recover Session leave for later button text
  ///
  /// In en, this message translates to:
  /// **'Leave for later'**
  String get leaveForLater;

  /// Recover Session leave for later button warning text
  ///
  /// In en, this message translates to:
  /// **'You won\'t be able to sync data'**
  String get youWontBeAbleToSyncData;

  /// Used when an user recover session with wrong credentials.
  ///
  /// In en, this message translates to:
  /// **'The credentials don\'t match with the current user.'**
  String get credentialNotMatchWithCurrentUser;

  /// Settings configurable by and applicable to the user.
  ///
  /// In en, this message translates to:
  /// **'User settings'**
  String get userSettings;

  /// Determines that an option (i.e location) will be included on entry capture.
  ///
  /// In en, this message translates to:
  /// **'Include on entry capture'**
  String get includeOnEntryCapture;

  /// No description provided for @locationTrackingActive.
  ///
  /// In en, this message translates to:
  /// **'Location tracking active'**
  String get locationTrackingActive;

  /// Toast text when entry location tracking was tapped from another org.
  ///
  /// In en, this message translates to:
  /// **'The tracking entry belongs to {organizationName}.'**
  String locationTrackingFromAnotherOrg(String organizationName);

  /// App drawer item title to allow view your activity
  ///
  /// In en, this message translates to:
  /// **'My Activiy'**
  String get myActiviy;

  /// App drawer item subtitle to allow view your activity
  ///
  /// In en, this message translates to:
  /// **'View the number of entries created this week and this month'**
  String get myActiviyDescription;

  /// Week chart title
  ///
  /// In en, this message translates to:
  /// **'This Week'**
  String get weekChartTitle;

  /// Week chart subtitle
  ///
  /// In en, this message translates to:
  /// **'Compared to Last Week'**
  String get weekChartSubtitle;

  /// Month chart title
  ///
  /// In en, this message translates to:
  /// **'This Month'**
  String get monthChartTitle;

  /// Month chart subtitle
  ///
  /// In en, this message translates to:
  /// **'entries compared to {count} Last Month'**
  String monthChartSubtitle(int count);

  /// App drawer item title to allow user tracking location.
  ///
  /// In en, this message translates to:
  /// **'Track My Location'**
  String get allowTrackingUser;

  /// App drawer item subtitle to allow start user tracking location.
  ///
  /// In en, this message translates to:
  /// **'For this organization'**
  String get forThisOrganization;

  /// App drawer item subtitle to allow stop user tracking location.
  ///
  /// In en, this message translates to:
  /// **'For {organizationName}'**
  String forOrganization(String organizationName);

  /// No description provided for @backgroundLocationTracking.
  ///
  /// In en, this message translates to:
  /// **'Background location tracking active. Tap to open.'**
  String get backgroundLocationTracking;

  /// App related permissions granted through operating system
  ///
  /// In en, this message translates to:
  /// **'System permissions'**
  String get systemPermissions;

  /// Signup phone input caption
  ///
  /// In en, this message translates to:
  /// **'For improved support & upcoming WhatsApp integration.'**
  String get phoneInputCaption;

  /// Menu title for go to privacy policy.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get privacyPolicy;

  /// Menu title for go to youtube video tutorials.
  ///
  /// In en, this message translates to:
  /// **'Video Tutorials'**
  String get videoTutorials;

  /// Text button option to contact WhatsApp support.
  ///
  /// In en, this message translates to:
  /// **'Contact Support'**
  String get contactSupport;

  /// Menu option for send support WhatsApp message.
  ///
  /// In en, this message translates to:
  /// **'WhatsApp Support'**
  String get sendUsWhatsApp;

  /// Menu option for send email contact.
  ///
  /// In en, this message translates to:
  /// **'Email Support'**
  String get emailSupport;

  /// Initial message for init support contact
  ///
  /// In en, this message translates to:
  /// **'Hi, I\'m a Bitacora.io user {email}, I have a question or comment...'**
  String supportMessage(String email);

  /// Initial message WhatsAap support contact in login page
  ///
  /// In en, this message translates to:
  /// **'Hi, I have a question or comment...'**
  String get loginSupportMessage;

  /// A user setting category for changing the app's color scheme.
  ///
  /// In en, this message translates to:
  /// **'Theme'**
  String get theme;

  /// Referring to the current underlying system (or operating system)
  ///
  /// In en, this message translates to:
  /// **'System'**
  String get system;

  /// Referring to the light theme or color scheme (light mode)
  ///
  /// In en, this message translates to:
  /// **'Light'**
  String get light;

  /// Referring to the dark theme or color scheme (dark mode)
  ///
  /// In en, this message translates to:
  /// **'Dark'**
  String get dark;

  /// Option to show data related for all projects/categories/locations
  ///
  /// In en, this message translates to:
  /// **'All: Projects / Categories / Locations'**
  String get allProjects;

  /// Generic string for a menu (list of options).
  ///
  /// In en, this message translates to:
  /// **'Menu'**
  String get menu;

  /// A label shown when there are no scheduled tasks for the user.
  ///
  /// In en, this message translates to:
  /// **'No scheduled tasks.'**
  String get noScheduledTasks;

  /// The basic type of log, related to any kind of activity.
  ///
  /// In en, this message translates to:
  /// **'Log'**
  String get log;

  /// A type of log, related to inventory activity.
  ///
  /// In en, this message translates to:
  /// **'Inventory'**
  String get inventory;

  /// A type of log, related to personnel activity.
  ///
  /// In en, this message translates to:
  /// **'Personnel'**
  String get personnel;

  /// Describes the movement of inventory.
  ///
  /// In en, this message translates to:
  /// **'Movement'**
  String get movement;

  /// Inventory movement going out.
  ///
  /// In en, this message translates to:
  /// **'Outgoing'**
  String get outgoing;

  /// Inventory movement coming in.
  ///
  /// In en, this message translates to:
  /// **'Incoming'**
  String get incoming;

  /// Used when an email text field has invalid contents.
  ///
  /// In en, this message translates to:
  /// **'Invalid email'**
  String get invalidEmail;

  /// Used when invite new user with duplicated email to org
  ///
  /// In en, this message translates to:
  /// **'Email already used in this organization'**
  String get alreadyUsedEmail;

  /// Used when an phone number text field has invalid contents.
  ///
  /// In en, this message translates to:
  /// **'Invalid phone number'**
  String get invalidPhoneNumber;

  /// Used when a name text field has empty contents.
  ///
  /// In en, this message translates to:
  /// **'Empty name'**
  String get emptyName;

  /// Used when a password text field has empty contents.
  ///
  /// In en, this message translates to:
  /// **'Empty password'**
  String get emptyPassword;

  /// A generic message for unexpected errors.
  ///
  /// In en, this message translates to:
  /// **'Something went wrong.'**
  String get somethingWentWrong;

  /// Generic try again later message.
  ///
  /// In en, this message translates to:
  /// **'Try again later.'**
  String get tryAgainLater;

  /// Shown below the missing required field when trying to save.
  ///
  /// In en, this message translates to:
  /// **'This field is required.'**
  String get requiredField;

  /// The quantity field label for a log.
  ///
  /// In en, this message translates to:
  /// **'# / Qtty.'**
  String get formQuantity;

  /// The activity/title field label for a log entry.
  ///
  /// In en, this message translates to:
  /// **'Activity / Entry Title'**
  String get formTitle;

  /// The project field label for a log.
  ///
  /// In en, this message translates to:
  /// **'Proj., Category or Location'**
  String get formProject;

  /// The sub-project/category field label for a log.
  ///
  /// In en, this message translates to:
  /// **'Sub-proj. cat. loc'**
  String get formSublocation;

  /// The comments/description field label for a log.
  ///
  /// In en, this message translates to:
  /// **'Comments / Description'**
  String get formComments;

  /// The comments/description field label for a progress log.
  ///
  /// In en, this message translates to:
  /// **'Progress Comments / Description'**
  String get progressComments;

  /// When the user denied geolocation permissions forever.
  ///
  /// In en, this message translates to:
  /// **'Location permissions are denied.'**
  String get locationDenied;

  /// When the user denied photo gallery access.
  ///
  /// In en, this message translates to:
  /// **'Photo gallery access is denied.'**
  String get photoDenied;

  /// When the user denied video gallery access.
  ///
  /// In en, this message translates to:
  /// **'Video gallery access is denied.'**
  String get videoDenied;

  /// When the user denied file access.
  ///
  /// In en, this message translates to:
  /// **'Storage access is denied.'**
  String get fileDenied;

  /// When the user denied camera access.
  ///
  /// In en, this message translates to:
  /// **'Camera access is denied.'**
  String get cameraDenied;

  /// When the user denied audio recording permissions forever.
  ///
  /// In en, this message translates to:
  /// **'Audio recording permissions are denied.'**
  String get recordingDenied;

  /// Toast text for speech to text error. Keep short.
  ///
  /// In en, this message translates to:
  /// **'⚠️ Error due to speech recognition.'**
  String get voiceRecognitionError;

  /// Go to the application settings on the system.
  ///
  /// In en, this message translates to:
  /// **'Go to Settings'**
  String get goToAppSystemSettings;

  /// Shows when a user has input a new field (project, title, etc.)
  ///
  /// In en, this message translates to:
  /// **'New entries'**
  String get newEntries;

  /// Labels an entry. The service/work was offered. i.e. someone hired you
  ///
  /// In en, this message translates to:
  /// **'Offered'**
  String get offered;

  /// The item name field label for an inventory log.
  ///
  /// In en, this message translates to:
  /// **'Item Name'**
  String get itemName;

  /// The label for the source of the inventory log. Keep short.
  ///
  /// In en, this message translates to:
  /// **'From:'**
  String get from_;

  /// The label for the destination of the inventory log. Keep short.
  ///
  /// In en, this message translates to:
  /// **'To:'**
  String get to_;

  /// The label for the recipient of the inventory log.
  ///
  /// In en, this message translates to:
  /// **'Recipient'**
  String get recipient;

  /// The label for the provider of the inventory log.
  ///
  /// In en, this message translates to:
  /// **'Provider'**
  String get provider;

  /// The label used when specifying the price per unit.
  ///
  /// In en, this message translates to:
  /// **'UNIT'**
  String get unit_;

  /// The label used when specifying the total price.
  ///
  /// In en, this message translates to:
  /// **'TOT'**
  String get total_;

  /// The label used for the cost price of the inventory log.
  ///
  /// In en, this message translates to:
  /// **'Expense'**
  String get expense;

  /// The label used for the sale price of the inventory log.
  ///
  /// In en, this message translates to:
  /// **'Income'**
  String get income;

  /// The label used for the reason of the inventory log.
  ///
  /// In en, this message translates to:
  /// **'Reason'**
  String get reason;

  /// Used to mark a payment status as not available.
  ///
  /// In en, this message translates to:
  /// **'N/A'**
  String get na;

  /// Used to mark a payment status as paid.
  ///
  /// In en, this message translates to:
  /// **'PAID'**
  String get paid;

  /// Used to mark a payment status as not paid.
  ///
  /// In en, this message translates to:
  /// **'NOT PAID'**
  String get unpaid;

  /// Used to select the number of hours for a personnel log
  ///
  /// In en, this message translates to:
  /// **'# OF HOURS'**
  String get numHours_;

  /// Used to select the clock-in clock-out times for a personnel log. Must be very short.
  ///
  /// In en, this message translates to:
  /// **'Time'**
  String get time_;

  /// Used as the label for inputting the number of hours for a personnel log
  ///
  /// In en, this message translates to:
  /// **'Hours'**
  String get hours;

  /// Used as the label for the clock-in time for a personnel log.
  ///
  /// In en, this message translates to:
  /// **'Entrance'**
  String get entrance;

  /// Used as the label for the clock-out time for a personnel log.
  ///
  /// In en, this message translates to:
  /// **'Exit'**
  String get exit;

  /// Title for schedule option in entry form.
  ///
  /// In en, this message translates to:
  /// **'Schedule Entry'**
  String get scheduleEntry;

  /// Used for the start date of a range of dates.
  ///
  /// In en, this message translates to:
  /// **'Start Date'**
  String get startDate;

  /// Used for the end date of a range of dates.
  ///
  /// In en, this message translates to:
  /// **'End Date'**
  String get endDate;

  /// Used for the start time of a range of times.
  ///
  /// In en, this message translates to:
  /// **'Start Time'**
  String get startTime;

  /// Used for the end time of a range of times.
  ///
  /// In en, this message translates to:
  /// **'End Time'**
  String get endTime;

  /// Default title for entry timer.
  ///
  /// In en, this message translates to:
  /// **'Timer'**
  String get timer;

  /// Default title for top snack bar entry timer.
  ///
  /// In en, this message translates to:
  /// **'Entry Timer'**
  String get entryTimer;

  /// Toast description to prevent more than one timer
  ///
  /// In en, this message translates to:
  /// **'You can\'t have more than one active timer.'**
  String get moreThanOneTimer;

  /// Toast description to prevent more than one tracking entry
  ///
  /// In en, this message translates to:
  /// **'You can\'t have more than one tracking entry.'**
  String get moreThanOneLocationTracking;

  /// A type of scheduled entry. Allows multiple progress reports 0% -> 100%.
  ///
  /// In en, this message translates to:
  /// **'Progressive'**
  String get progressive;

  /// A type of scheduled entry. Allows only one progress report.
  ///
  /// In en, this message translates to:
  /// **'Complete'**
  String get complete;

  /// Notification message when the first report has been created successfully.
  ///
  /// In en, this message translates to:
  /// **'Your first PDF report is ready!'**
  String get myFirstReportIsReady;

  /// Title for scheduled option in entry form. Entry has already been scheduled
  ///
  /// In en, this message translates to:
  /// **'Scheduled Entry'**
  String get scheduledEntry;

  /// Title for income/expense option in entry form.
  ///
  /// In en, this message translates to:
  /// **'Income and/or expense'**
  String get incomeOrExpense;

  /// Invite the user to add a comment to a something
  ///
  /// In en, this message translates to:
  /// **'Add a comment'**
  String get addAComment;

  /// EditText hint for the assigned user of an entry.
  ///
  /// In en, this message translates to:
  /// **'Assigned to'**
  String get assignedTo;

  /// Filter of Open State Sliding panel.
  ///
  /// In en, this message translates to:
  /// **'Assigned to me'**
  String get assignedToMe;

  /// Text of button to change Simplelog to Worklog in entry form page.
  ///
  /// In en, this message translates to:
  /// **'More Fields'**
  String get moreFields;

  /// The title of the logout dialog.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get logoutTitle;

  /// The message of the logout dialog.
  ///
  /// In en, this message translates to:
  /// **'Are you sure that you want to log out?'**
  String get logoutMessage;

  /// An extra line on the logout dialog message showing that data will be permanently lost.
  ///
  /// In en, this message translates to:
  /// **'*You have local changes, they will be permanently lost.'**
  String get logoutWillLoseData;

  /// The message of gpt chat page exit dialog
  ///
  /// In en, this message translates to:
  /// **'The chat will be lost on exit.'**
  String get chatWillBeLost;

  /// Placeholder text of chat input field.
  ///
  /// In en, this message translates to:
  /// **'Write something...'**
  String get writeSomething;

  /// The message of entry form page exit dialog
  ///
  /// In en, this message translates to:
  /// **'Entry not saved'**
  String get entryNotSaved;

  /// The message of doodle canvas mode exit dialog
  ///
  /// In en, this message translates to:
  /// **'Doodle not saved'**
  String get doodleNotSaved;

  /// An extra line on the exit dialog message showing that changes will be permanently lost.
  ///
  /// In en, this message translates to:
  /// **'How do you want to proceed?'**
  String get willLoseData;

  /// Title for a ui that allows the user to choose their active organization.
  ///
  /// In en, this message translates to:
  /// **'Choose your active organization'**
  String get chooseYourOrg;

  /// Title of the top snackbar that notifies that the user was invited to a new organization.
  ///
  /// In en, this message translates to:
  /// **'You were invited to a new organization'**
  String get wereInvitedOrg;

  /// Tooltip text for launching intent to choose organization.
  ///
  /// In en, this message translates to:
  /// **'Switch Organization'**
  String get switchOrganization;

  /// Title for selecting the entry type when generating reports.
  ///
  /// In en, this message translates to:
  /// **'Entry Type'**
  String get reportEntryType;

  /// Specifying a report format of PDF with photos.
  ///
  /// In en, this message translates to:
  /// **'Photo PDF'**
  String get photoPdf;

  /// Option to include date fields in reports.
  ///
  /// In en, this message translates to:
  /// **'Start Date / End Date'**
  String get startDateEndDate;

  /// Option to include income and expense fields in reports.
  ///
  /// In en, this message translates to:
  /// **'Income / Expense'**
  String get incomeExpense;

  /// Option to include provider and status fields in reports.
  ///
  /// In en, this message translates to:
  /// **'Provider / Status'**
  String get providerStatus;

  /// Text in notification to remind the start of scheduled entry
  ///
  /// In en, this message translates to:
  /// **'Start tomorrow'**
  String get startTomorrow;

  /// Text in notification to remind the finish of scheduled entry
  ///
  /// In en, this message translates to:
  /// **'Finish today'**
  String get finishToday;

  /// Text in notification to remind the finish of scheduled entry
  ///
  /// In en, this message translates to:
  /// **'Finish tomorrow'**
  String get finishTomorrow;

  /// A notifications setting sub-category Title for scheduled entries.
  ///
  /// In en, this message translates to:
  /// **'Scheduled Entries'**
  String get scheduledEntries;

  /// Notification time for scheduled entries.
  ///
  /// In en, this message translates to:
  /// **'Notification time'**
  String get notificationTime;

  /// A notification setting for enable/disable schedule entry day of notifications.
  ///
  /// In en, this message translates to:
  /// **'Notify the day of'**
  String get notifyDayOf;

  /// A notification setting subtitle for enable/disable schedule entry same day notifications.
  ///
  /// In en, this message translates to:
  /// **'Alert on the start and/or end day of the task.'**
  String get alertOnTheStartAndOrEndDayOfTheTask;

  /// A notification setting for enable/disable schedule entry one day before notifications.
  ///
  /// In en, this message translates to:
  /// **'Notify the day before'**
  String get notifyTheDayBefore;

  /// A notification setting subtitle for enable/disable schedule entry the day before notifications.
  ///
  /// In en, this message translates to:
  /// **'Alert the day before the task starts and/or ends.'**
  String get alertTheDayBeforeTheTaskStartsAndOrEnds;

  /// No description provided for @dailyReminder.
  ///
  /// In en, this message translates to:
  /// **'Daily reminder'**
  String get dailyReminder;

  /// Daily reminder notifications description
  ///
  /// In en, this message translates to:
  /// **'Notifies daily reminding you to log your notes and evidences'**
  String get dailyReminderDescription;

  /// Daily reminder notification title
  ///
  /// In en, this message translates to:
  /// **'Log your activities of today'**
  String get logYourActivitiesOfToday;

  /// Daily reminder notification body
  ///
  /// In en, this message translates to:
  /// **'Don’t forget to log today’s notes and evidences so you don’t pile up work.'**
  String get dailyReminderBody;

  /// Prompts user to use the previous (legacy) version of some UI
  ///
  /// In en, this message translates to:
  /// **'Use Previous Version'**
  String get usePreviousVersion;

  /// (noun) Title of notification when users reports a bug.
  ///
  /// In en, this message translates to:
  /// **'Bug Report'**
  String get bugReport;

  /// The description for the notification channel associated with bug report creation.
  ///
  /// In en, this message translates to:
  /// **'Notifies when your bug reports are ready and allows you to share them.'**
  String get bugReportChannelDescription;

  /// Shows below a bug report notification with a CTA for user to tap to share bug report file.
  ///
  /// In en, this message translates to:
  /// **'Tap to share.'**
  String get bugReportTapToShare;

  /// A hint for a textfield where we ask the user to describe their issue for a bug report.
  ///
  /// In en, this message translates to:
  /// **'Please describe your issue.'**
  String get bugReportHint;

  /// Title for a dialog confirming intention to delete the current account
  ///
  /// In en, this message translates to:
  /// **'Delete Account'**
  String get deleteAccountTitle;

  /// Title for a dialog confirming intention to delete the current account
  ///
  /// In en, this message translates to:
  /// **'This will initiate the process of deleting your account and log you out.'**
  String get deleteAccountMessage;

  /// Error message of QR scanner when user is too far from the QR location
  ///
  /// In en, this message translates to:
  /// **'You are too far from the QR code location'**
  String get farFromQr;

  /// Error message of QR scanner when QR code does not belong to the current active organization
  ///
  /// In en, this message translates to:
  /// **'This QR does not belong to your current active organization'**
  String get qrDoesNotBelongOrg;

  /// The title of the delete dialog.
  ///
  /// In en, this message translates to:
  /// **'Are you sure?'**
  String get deleteTitle;

  /// The message of the delete-entry dialog.
  ///
  /// In en, this message translates to:
  /// **'This will permanently delete the entry.'**
  String get deleteMessage;

  /// The title of the delete selection dialog.
  ///
  /// In en, this message translates to:
  /// **'Are you sure?'**
  String get deleteSelectionTitle;

  /// The title of the delete selection dialog.
  ///
  /// In en, this message translates to:
  /// **'This will permanently delete the entries.'**
  String get deleteSelectionMessage;

  /// No description provided for @ungroupGroupTitle.
  ///
  /// In en, this message translates to:
  /// **'Are you sure?'**
  String get ungroupGroupTitle;

  /// No description provided for @ungroupGroupMessage.
  ///
  /// In en, this message translates to:
  /// **'This will permanently ungroup all entries.'**
  String get ungroupGroupMessage;

  /// Text to indicate the number of entries selected
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =1{{count} selected entry} other{{count} selected entries}}'**
  String selectedEntries(num count);

  /// No description provided for @selectedNames.
  ///
  /// In en, this message translates to:
  /// **'{count, plural, =1{{count} selected name} other{{count} selected names}}'**
  String selectedNames(num count);

  /// The message of the delete-report dialog.
  ///
  /// In en, this message translates to:
  /// **'This will permanently delete the selected report(s).'**
  String get deleteReport;

  /// Text for active plan indicator when active plan is free
  ///
  /// In en, this message translates to:
  /// **'Free Plan'**
  String get freePlan;

  /// Description for pro features page when active plan is free
  ///
  /// In en, this message translates to:
  /// **'Record your activity notes day by day and view it on the web and app.'**
  String get freePlanDescription;

  /// Description for pro features page when active plan is free trial
  ///
  /// In en, this message translates to:
  /// **'Trial Plan'**
  String get trialPlan;

  /// No description provided for @trialPlanDescription.
  ///
  /// In en, this message translates to:
  /// **'Capture, create reports and add collaborators to explore on the web and app.'**
  String get trialPlanDescription;

  /// Pro features section title
  ///
  /// In en, this message translates to:
  /// **'Start discovering Bitacora.io:'**
  String get discoverBitacora;

  /// Feature title about sync files
  ///
  /// In en, this message translates to:
  /// **'Sync Photos and Files'**
  String get syncPhotosAndFiles;

  /// Feature description about sync files
  ///
  /// In en, this message translates to:
  /// **'Get files across devices, available on web and reports.'**
  String get syncPhotosAndFilesDescription;

  /// Feature title about pdf reports
  ///
  /// In en, this message translates to:
  /// **'PDF Reports'**
  String get pdfReports;

  /// Feature description about pdf reports
  ///
  /// In en, this message translates to:
  /// **'Generate automated reports from your entries.'**
  String get pdfReportsDescription;

  /// Error message when trying to generate a report from records that are not synchronized.
  ///
  /// In en, this message translates to:
  /// **'Entries not yet synchronized'**
  String get entriesNotYetSyncToastError;

  /// Shows in a small toast when failing to paste from clipboard.
  ///
  /// In en, this message translates to:
  /// **'Error pasting from clipboard. Try again.'**
  String get pasteErrorToast;

  /// Feature title about power tools
  ///
  /// In en, this message translates to:
  /// **'Power Tools'**
  String get powerTools;

  /// Feature description about power tools
  ///
  /// In en, this message translates to:
  /// **'QR codes, signatures, charts, map tracking, boards, custom alerts and more.'**
  String get powerToolsDescription;

  /// Onboarding page one title text.
  ///
  /// In en, this message translates to:
  /// **'Organized entries, automatic reports.'**
  String get onboarding1Title;

  /// Onboarding page two title text.
  ///
  /// In en, this message translates to:
  /// **'Record everything.'**
  String get onboarding2Title;

  /// Onboarding page two subtitle text.
  ///
  /// In en, this message translates to:
  /// **'Capture notes, activities, work and evidence - organized for easy access later.'**
  String get onboarding2Subtitle;

  /// Onboarding page three title text.
  ///
  /// In en, this message translates to:
  /// **'Create automatic reports.'**
  String get onboarding3Title;

  /// Onboarding page three subtitle text.
  ///
  /// In en, this message translates to:
  /// **'Create and share reports in PDF or Excel to share with collaborators and clients - you define what to include.'**
  String get onboarding3Subtitle;

  /// Onboarding page four title text.
  ///
  /// In en, this message translates to:
  /// **'Professionalize your work and reduce time with powerful tools.'**
  String get onboarding4Title;

  /// Text button to navigate to functions web page from pro features page
  ///
  /// In en, this message translates to:
  /// **'Discover Bitacora.io'**
  String get knowAllFeatures;

  /// Error message when an entry is no longer editable.
  ///
  /// In en, this message translates to:
  /// **'No longer able to edit this entry.'**
  String get noLongerAbleToEdit;

  /// Error message when there is no permission to edit an entry.
  ///
  /// In en, this message translates to:
  /// **'You don\'t have permission to edit this entry.'**
  String get noPermissionToEdit;

  /// Error message when there is no permission to save entry because of wrong entry date.
  ///
  /// In en, this message translates to:
  /// **'Unable to save entry on the selected date.'**
  String get wrongDateForEntry;

  /// Feature title about ai assistant
  ///
  /// In en, this message translates to:
  /// **'Ai Assistant'**
  String get aiAssistant;

  /// Experimental model easter egg enabled
  ///
  /// In en, this message translates to:
  /// **'Experimental mode enabled'**
  String get experimentalModeEnabled;

  /// Experimental model easter egg disabled
  ///
  /// In en, this message translates to:
  /// **'Experimental mode disabled'**
  String get experimentalModeDisabled;

  /// Label for the transcript section in the transcript notification
  ///
  /// In en, this message translates to:
  /// **'Transcript:'**
  String get transcript;

  /// Label for the sentiment section in the transcript notification
  ///
  /// In en, this message translates to:
  /// **'Sentiment:'**
  String get sentiment;

  /// Label for the keywords section in the transcript notification
  ///
  /// In en, this message translates to:
  /// **'Keywords:'**
  String get keywords;

  /// Label for the action items section in the transcript notification
  ///
  /// In en, this message translates to:
  /// **'Action Items:'**
  String get actionItems;

  /// No description provided for @aiGeneratedNotification.
  ///
  /// In en, this message translates to:
  /// **'This entry was created from an audio recording, photos or video.'**
  String get aiGeneratedNotification;

  /// Text indicating that no resource was selected for the AI generator.
  ///
  /// In en, this message translates to:
  /// **'No resource was selected to generate an entry'**
  String get aiResourceNotSelected;
}

class _AppLocalizationsDelegate extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) => <String>['en', 'es'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {


  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'en': return AppLocalizationsEn();
    case 'es': return AppLocalizationsEs();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.'
  );
}
