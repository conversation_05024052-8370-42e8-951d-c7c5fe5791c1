import 'package:bitacora/util/inject/inject.dart';
import 'package:test/test.dart';

void main() {
  group('Inject tests', () {
    test('Uses generator when no inject', () {
      final result = inject(() => 'aoeu');

      expect(result, 'aoeu');
    });

    test('Injected within injected sync', () {
      withInjected<String>('ueoa', () {
        withInjected<String>('snth', () {
          expect(inject(() => 'aoeu'), 'snth');
        });
        expect(inject(() => 'aoeu'), 'ueoa');
      });
      expect(inject(() => 'aoeu'), 'aoeu');
    });

    test('Injected within injected async', () async {
      await withInjected<String>('ueoa', () async {
        await withInjected<String>('snth', () async {
          await Future.microtask(() => expect(inject(() => 'aoeu'), 'snth'));
        });
        await Future.microtask(() => expect(inject(() => 'aoeu'), 'ueoa'));
      });
      expect(inject(() => 'aoeu'), 'aoeu');
    });

    test('Mixed type injections', () {
      withInjected<int>(2, () {
        withInjected<String>('snth', () {
          // Inner zone mixes with outer zone.
          expect(inject(() => 1), 2);
          expect(inject(() => 'aoeu'), 'snth');
        });
      });
    });
  });
}
