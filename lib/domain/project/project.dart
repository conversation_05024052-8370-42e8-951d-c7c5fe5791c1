import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/value/project_address.dart';
import 'package:bitacora/domain/project/value/project_description.dart';
import 'package:bitacora/domain/project/value/project_is_syncable.dart';
import 'package:bitacora/domain/project/value/project_name.dart';
import 'package:bitacora/domain/project/value/project_sync_last_entry_updated_at.dart';
import 'package:bitacora/domain/project/value/project_sync_last_sync_time.dart';
import 'package:bitacora/domain/project/value/project_sync_next_page_token.dart';
import 'package:bitacora/domain/project/value/project_type.dart';

export 'package:bitacora/domain/project/value/project_address.dart';
export 'package:bitacora/domain/project/value/project_description.dart';
export 'package:bitacora/domain/project/value/project_is_syncable.dart';
export 'package:bitacora/domain/project/value/project_name.dart';
export 'package:bitacora/domain/project/value/project_sync_last_entry_updated_at.dart';
export 'package:bitacora/domain/project/value/project_sync_last_sync_time.dart';
export 'package:bitacora/domain/project/value/project_sync_next_page_token.dart';
export 'package:bitacora/domain/project/value/project_type.dart';

class Project extends Model {
  final ProjectName? name;
  final ProjectDescription? description;
  final ProjectAddress? address;
  final LatLngValueObject? location;
  final ProjectType? type;
  final ProjectIsSyncable? isSyncable;
  final ProjectSyncLastEntryUpdatedAt? syncLastEntryUpdatedAt;
  final ProjectSyncLastSyncTime? syncLastSyncTime;
  final ProjectSyncNextPageToken? syncNextPageToken;
  final Organization? organization;

  const Project({
    super.id,
    super.remoteId,
    this.name,
    this.description,
    this.address,
    this.location,
    this.type,
    this.isSyncable,
    this.syncLastEntryUpdatedAt,
    this.syncLastSyncTime,
    this.syncNextPageToken,
    this.organization,
  });

  Project copyWith({
    ProjectName? name,
    Organization? organization,
    ProjectIsSyncable? isSyncable,
    ProjectSyncLastSyncTime? syncLastSyncTime,
    ProjectSyncNextPageToken? syncNextPageToken,
  }) {
    return Project(
      id: id,
      remoteId: remoteId,
      name: name ?? this.name,
      description: description,
      address: address,
      location: location,
      type: type,
      isSyncable: isSyncable ?? this.isSyncable,
      syncLastEntryUpdatedAt: syncLastEntryUpdatedAt,
      syncLastSyncTime: syncLastSyncTime ?? this.syncLastSyncTime,
      syncNextPageToken: syncNextPageToken ?? this.syncNextPageToken,
      organization: organization ?? this.organization,
    );
  }

  @override
  Map<Field, dynamic> get fields => {
        ProjectField.id: id,
        ProjectField.remoteId: remoteId,
        ProjectField.name: name,
        ProjectField.description: description,
        ProjectField.address: address,
        ProjectField.location: location,
        ProjectField.type: type,
        ProjectField.isSyncable: isSyncable,
        ProjectField.syncLastEntryUpdatedAt: syncLastEntryUpdatedAt,
        ProjectField.syncLastSyncTime: syncLastSyncTime,
        ProjectField.syncNextPageToken: syncNextPageToken,
        ProjectField.organization: organization,
      };
}

enum ProjectField with Field {
  id,
  remoteId,
  name,
  description,
  address,
  location,
  type,
  isSyncable,
  syncLastEntryUpdatedAt,
  syncLastSyncTime,
  syncNextPageToken,
  organization,
}

const projectNestedModelFields = {
  ProjectField.organization,
};
