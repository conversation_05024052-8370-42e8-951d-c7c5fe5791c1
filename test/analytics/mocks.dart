import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/analytics/bitacora_analytics_logger.dart';
import 'package:mocktail/mocktail.dart';

class MockAnalyticsLogger extends Mock implements AnalyticsLogger {}

class MockBitacoraAnalyticsLogger extends Mock
    implements BitacoraAnalyticsLogger {}

AnalyticsLogger mockAnalyticsLogger() {
  final mock = MockAnalyticsLogger();
  when(
    () => mock.logEvent(any(), props: any(named: 'props')),
  ).thenAnswer((_) => Future.value());
  return mock;
}
