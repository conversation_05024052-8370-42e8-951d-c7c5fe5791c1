import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/util/access/access_utils.dart';
import 'package:bitacora/util/access/save_entry_permission.dart';
import 'package:flutter/foundation.dart';

class EntryFormSaveEntryPermissionNotifier
    extends ValueNotifier<SaveEntryPermission?> {
  final Repository db;
  final Organization organization;
  final ValueListenable<Entry?> entry;
  final ValueListenable<LogDay?> day;
  final ValueNotifier<LogTime> time;

  EntryFormSaveEntryPermissionNotifier(
      this.db, this.organization, this.entry, this.day, this.time)
      : super(null) {
    check();
    entry.addListener(check);
    day.addListener(check);
    time.addListener(check);
  }

  @override
  void dispose() {
    entry.removeListener(check);
    day.removeListener(check);
    time.removeListener(check);
    super.dispose();
  }

  Future<void> check() async {
    value = await AccessUtils().findSaveEntryPermission(
      db,
      organization,
      entry.value,
      day.value!,
      time.value,
    );
  }
}
