import 'package:bitacora/l10n/app_localizations_resolver.dart';

enum NotificationChannels {
  openStateEntriesDayOf,
  openStateEntriesDayBefore,
  dailyReminder,
  bugReport,
}

extension NotificationChannelsData on NotificationChannels {
  String get key {
    switch (this) {
      case NotificationChannels.openStateEntriesDayOf:
        return 'open_state_entries_day_of';
      case NotificationChannels.openStateEntriesDayBefore:
        return 'open_state_entries_day_before';
      case NotificationChannels.dailyReminder:
        return 'daily_reminder';
      case NotificationChannels.bugReport:
        return 'bug_report';
    }
  }

  String get name {
    switch (this) {
      case NotificationChannels.openStateEntriesDayOf:
        return AppLocalizationsResolver.get().notifyDayOf;
      case NotificationChannels.openStateEntriesDayBefore:
        return AppLocalizationsResolver.get().notifyTheDayBefore;
      case NotificationChannels.dailyReminder:
        return AppLocalizationsResolver.get().dailyReminder;
      case NotificationChannels.bugReport:
        return AppLocalizationsResolver.get().bugReport;
    }
  }

  String get description {
    switch (this) {
      case NotificationChannels.openStateEntriesDayOf:
        return AppLocalizationsResolver.get()
            .alertOnTheStartAndOrEndDayOfTheTask;
      case NotificationChannels.openStateEntriesDayBefore:
        return AppLocalizationsResolver.get()
            .alertTheDayBeforeTheTaskStartsAndOrEnds;
      case NotificationChannels.dailyReminder:
        return AppLocalizationsResolver.get().dailyReminderDescription;
      case NotificationChannels.bugReport:
        return AppLocalizationsResolver.get().bugReportChannelDescription;
    }
  }
}

enum NotificationChannelGroups { openStateEntries }

extension NotificationChannelGroupsData on NotificationChannelGroups {
  String get key {
    switch (this) {
      case NotificationChannelGroups.openStateEntries:
        return 'open_state_entries';
    }
  }

  String get name {
    switch (this) {
      case NotificationChannelGroups.openStateEntries:
        return AppLocalizationsResolver.get().scheduledEntries;
    }
  }
}
