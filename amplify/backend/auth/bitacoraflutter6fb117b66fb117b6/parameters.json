{"identityPoolName": "bitacoraflutter6fb117b6_identitypool_6fb117b6", "allowUnauthenticatedIdentities": true, "resourceNameTruncated": "bitaco6fb117b6", "userPoolName": "bitacoraflutter6fb117b6_userpool_6fb117b6", "autoVerifiedAttributes": ["email"], "mfaConfiguration": "OFF", "mfaTypes": ["SMS Text Message"], "smsAuthenticationMessage": "Your authentication code is {####}", "smsVerificationMessage": "Your verification code is {####}", "emailVerificationSubject": "Your verification code", "emailVerificationMessage": "Your verification code is {####}", "defaultPasswordPolicy": false, "passwordPolicyMinLength": 8, "passwordPolicyCharacters": [], "requiredAttributes": ["email"], "aliasAttributes": ["preferred_username"], "userpoolClientGenerateSecret": false, "userpoolClientRefreshTokenValidity": 30, "userpoolClientWriteAttributes": ["email"], "userpoolClientReadAttributes": ["email"], "userpoolClientLambdaRole": "bitaco6fb117b6_userpoolclient_lambda_role", "userpoolClientSetAttributes": false, "sharedId": "6fb117b6", "resourceName": "bitacoraflutter6fb117b66fb117b6", "authSelections": "identityPoolAndUserPool", "authRoleArn": {"Fn::GetAtt": ["AuthRole", "<PERSON><PERSON>"]}, "unauthRoleArn": {"Fn::GetAtt": ["UnauthRole", "<PERSON><PERSON>"]}, "useDefault": "manual", "thirdPartyAuth": false, "userPoolGroups": false, "adminQueries": false, "triggers": "{}", "hostedUI": false, "userPoolGroupList": [], "serviceName": "Cognito", "usernameCaseSensitive": false, "parentStack": {"Ref": "AWS::StackId"}, "breakCircularDependency": true, "permissions": [], "dependsOn": []}