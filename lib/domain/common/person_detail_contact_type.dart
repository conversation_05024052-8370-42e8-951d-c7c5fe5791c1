import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';

enum PersonDetailContactTypeValue {
  home,
  office,
  work,
  mobile,
  main;

  static PersonDetailContactTypeValue fromApiValue(String value) {
    switch (value) {
      case 'HOME':
        return PersonDetailContactTypeValue.home;
      case 'OFFICE':
        return PersonDetailContactTypeValue.office;
      case 'WORK':
        return PersonDetailContactTypeValue.work;
      case 'MOBILE':
        return PersonDetailContactTypeValue.mobile;
      case 'MAIN':
        return PersonDetailContactTypeValue.main;
      default:
        throw 'PersonDetailContactType: $value not supported';
    }
  }

  String displayValue() {
    switch (this) {
      case PersonDetailContactTypeValue.home:
        return AppLocalizationsResolver.get().home;
      case PersonDetailContactTypeValue.office:
        return AppLocalizationsResolver.get().office;
      case PersonDetailContactTypeValue.work:
        return AppLocalizationsResolver.get().work;
      case PersonDetailContactTypeValue.mobile:
        return AppLocalizationsResolver.get().mobile;
      case PersonDetailContactTypeValue.main:
        return AppLocalizationsResolver.get().main;
    }
  }

  String apiValue() {
    throw UnimplementedError();
  }
}

class PersonDetailContactType
    extends ValueObject<PersonDetailContactTypeValue> {
  PersonDetailContactType(super.value);

  static PersonDetailContactType home =
      PersonDetailContactType(PersonDetailContactTypeValue.home);
  static PersonDetailContactType office =
      PersonDetailContactType(PersonDetailContactTypeValue.office);
  static PersonDetailContactType work =
      PersonDetailContactType(PersonDetailContactTypeValue.work);
  static PersonDetailContactType mobile =
      PersonDetailContactType(PersonDetailContactTypeValue.mobile);
  static PersonDetailContactType main =
      PersonDetailContactType(PersonDetailContactTypeValue.main);

  static PersonDetailContactType fromDbValue(int value) =>
      PersonDetailContactType(PersonDetailContactTypeValue.values[value - 1]);

  static PersonDetailContactType fromApiValue(String value) =>
      PersonDetailContactType(PersonDetailContactTypeValue.fromApiValue(value));

  @override
  get dbValue => value.index + 1;

  @override
  String get displayValue => value.displayValue();

  @override
  get apiValue => value.apiValue();
}
