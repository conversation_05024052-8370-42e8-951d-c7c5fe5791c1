import 'dart:async';

import 'package:bitacora/application/report/report_service_context_snapshot.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/domain/report/field/report_format.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/cupertino.dart';

typedef ReportCreationStatusCallback = void Function(
    ReportServiceCreationStatus status, Report report);

enum ReportServiceCreationStatus {
  waiting,
  creating,
  processing,
}

abstract class ReportService {
  final ReportServiceContextSnapshot contextSnapshot;

  ReportService(this.contextSnapshot);

  Future<Report> create(
    Report report,
    ReportCreationStatusCallback? callback, {
    waitSyncUpload = false,
  }) async {
    logger.d('report:service New report');

    if (waitSyncUpload) {
      await maybeWaitUpload(report, callback);
    }

    return onCreate(report, callback);
  }

  @protected
  Future<Report> onCreate(
    Report report,
    ReportCreationStatusCallback? callback,
  );

  Future<Report> retry(
    Report report,
    ReportCreationStatusCallback? callback, {
    waitSyncUpload = false,
  }) async {
    logger.d('report:service Retrying ${report.id}');
    if (waitSyncUpload) {
      await maybeWaitUpload(report, callback);
    }

    return onRetry(report, callback);
  }

  @protected
  Future<Report> onRetry(
    Report report,
    ReportCreationStatusCallback? callback,
  );

  @protected
  Future<void> maybeWaitUpload(
    Report report,
    ReportCreationStatusCallback? callback,
  ) async {
    final syncState = contextSnapshot.read<SyncState>();
    if (!_isUploading(report, syncState)) {
      return;
    }

    logReportCreationStatus(callback, ReportServiceCreationStatus.waiting, report);

    final completer = Completer();
    listener() {
      if (!syncState.isApiUploading) {
        completer.complete();
        syncState.removeListener(listener);
      }
    }

    syncState.addListener(listener);

    return completer.future;
  }

  bool _isUploading(Report report, SyncState syncState) {
    return report.postParams!.format == ReportFormat.photoPdf
        ? syncState.isUploading
        : syncState.isApiUploading;
  }

  @protected
  void logReportCreationStatus(
    ReportCreationStatusCallback? callback,
    ReportServiceCreationStatus status,
    Report report,
  ) {
    logger.d('report:service report:${report.id} status:$status');
    if (callback != null) {
      try {
        callback(status, report);
      } catch (e) {
        logger.e('report:callback Failed to update UI $e');
      }
    }
  }
}
