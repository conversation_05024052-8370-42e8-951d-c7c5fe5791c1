import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class ResourceCategoryDbContract extends DbContract {
  static const String _ = 'rsc_';
  static const String _tableName = 'resourceCategory';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String name = '${_}name';
  final String order = '${_}order';
  final String parentId = '${_}parentId';
  final String organizationId = '${_}organizationId';

  const ResourceCategoryDbContract() : super(_, _tableName);

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $name TEXT,
    $order INTEGER,
    $parentId INTEGER,
    $organizationId INTEGER NOT NULL
  )
  ''';

  @override
  int get initialDbVersion => kDbVersionWithResourceTables;
}
