# Sync query... first draft
# A user opens the app.. There's stuff we want to keep in sync. Let's sync.

subscription EntitiesSubscription($orgId: ID, $projectId: ID) {

  subscribeToEntityUpdates(orgId:$orgId, projectId:$projectId) {
    ...projectFields
    ...entryFields
    ... # Other subscribe-able entities.
  }

  ... # Same stuff for all other org-level entities
}

fragment projectFields on Project {
  id
  name
  isDeleted
}

... # more fragments
