import 'package:bitacora/application/location_tracking/entry/location_tracking_owner_strategy_entry.dart';
import 'package:bitacora/application/location_tracking/location_tracking_owner_strategy_context_snapshot.dart';
import 'package:bitacora/application/location_tracking/user/location_tracking_owner_strategy_user.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_owner_type.dart';

abstract class LocationTrackingOwnerStrategy {
  factory LocationTrackingOwnerStrategy.fromOwnerType(
    LocationTrackingOwnerType ownerType,
  ) {
    switch (ownerType.value) {
      case LocationTrackingOwnerTypeValue.entry:
        return LocationTrackingOwnerStrategyEntry();
      case LocationTrackingOwnerTypeValue.user:
        return LocationTrackingOwnerStrategyUser();
    }
  }

  LocationTrackingOwnerType get ownerType;

  Future<LocalId?> create(
    LocationTrackingOwnerStrategyContextSnapshot contextSnapshot,
    LocationTracking locationTracking,
  );

  Future<void> preStop(
    Repository db,
    RepositoryQueryContext context,
    LocalId locationTrackingId,
  );
}
