import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_presenter.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/util/math_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class AudioEntryListenerBarSwipeToCancel extends StatelessWidget {
  final AudioEntryListenerController controller;

  const AudioEntryListenerBarSwipeToCancel(
      {super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Offset?>(
      valueListenable: controller.swipeOffset,
      builder: (context, swipeOffset, _) {
        final rightSpaceScale = mapRange(swipeOffset?.dx ?? 0, 30,
            kMinSwipeDistanceForAction.toDouble(), 0, 50) as double;
        final color = rightSpaceScale >= 50
            ? bitacoraRed
            : Theme.of(context).colorScheme.surface;
        return Expanded(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.end,
            children: [
              Text(
                AppLocalizations.of(context)!.cancel,
                style: TextStyle(color: color),
              ),
              Icon(
                Icons.chevron_left_outlined,
                color: color,
              ),
              SizedBox(width: rightSpaceScale),
            ],
          ),
        );
      },
    );
  }
}
