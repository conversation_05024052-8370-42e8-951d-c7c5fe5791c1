import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/attachment/value/attachment_doodle.dart';
import 'package:bitacora/domain/common/model_translator.dart';

class AttachmentApiTranslator implements ModelTranslator<Attachment> {
  const AttachmentApiTranslator();

  @override
  Attachment fromMap(Map<String, dynamic> data) {
    /// Some system is adding a line break to doodles.
    final doodle = (data['doodle'] as String?)?.replaceAll('\n', '');
    return Attachment(
      s3Key: AttachmentS3Key(data['s3_key']),
      name: AttachmentName(data['name']),
      isUploaded: AttachmentIsUploaded(data['upload_done'] == 1),
      doodle: AttachmentDoodle(doodle),
      comments: AttachmentComments(data['comments']),
    );
  }

  @override
  Map<String, dynamic> toMap(Attachment model) {
    final map = {
      's3_key': model.s3Key!.apiValue,
      'name': model.name!.apiValue,
      'upload_done': model.isUploaded!.apiValue,
      'doodle': model.doodle!.apiValue,
      'comments': model.comments!.apiValue,
    };
    return map;
  }
}
