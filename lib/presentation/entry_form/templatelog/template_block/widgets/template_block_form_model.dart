import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:bitacora/util/action_on_focus_widget.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class TemplateBlockFormModel extends StatefulWidget {
  final TemplateBlockTextFormController controller;
  final TextInputAction? texInputAction;
  final RepositoryQuery<List<String>> suggestionQuery;
  final void Function(dynamic)? onFieldSubmitted;
  final bool isNewable;
  final bool allowNews;
  final String? Function(String text)? validator;

  const TemplateBlockFormModel({
    super.key,
    required this.suggestionQuery,
    required this.controller,
    this.texInputAction,
    this.onFieldSubmitted,
    this.isNewable = true,
    this.allowNews = true,
    this.validator,
  });

  @override
  State<TemplateBlockFormModel> createState() => _TemplateBlockFormModelState();
}

class _TemplateBlockFormModelState extends State<TemplateBlockFormModel> {
  String _lastValidateText = '_';
  String _lasRejectedText = '_';

  @override
  void initState() {
    super.initState();

    if (widget.controller.value.inputController.text != '') {
      _lastValidateText = widget.controller.value.inputController.text;
    }
  }

  @override
  Widget build(BuildContext context) {
    final configuration = TypeaheadTextFieldConfiguration(
      focusNode: widget.controller.focusNode,
      controller: widget.controller.value.inputController,
      textInputAction: widget.texInputAction,
      textCapitalization: TextCapitalization.sentences,
      decoration: InputDecoration(
          labelText: widget.controller.templateBlock.customFieldOptions!
              .customField!.name!.displayValue),
      onSubmitted: widget.onFieldSubmitted,
      onChanged: _validateIfNotAllowedNews,
      validator: _validator,
    );

    if (widget.isNewable) {
      return NewableFieldSuggestionTypeahead(
        key: widget.controller.fieldKey,
        suggestionsQuery: widget.suggestionQuery,
        queryScope: getOrgScope(context)!,
        onSelect: _onSelect,
        textFieldConfiguration: configuration,
      );
    }

    final themeData = Theme.of(context);
    return Theme(
      data: themeData.copyWith(
        inputDecorationTheme: themeData.inputDecorationTheme.copyWith(
          errorStyle: const TextStyle(height: 0, fontSize: 0),
        ),
      ),
      child: ActionOnFocusWidget(
        focusNode: configuration.focusNode,
        onFocusChanged: (hasFocus) => _onFocusChanged(
          hasFocus,
          configuration.controller,
        ),
        child: SuggestionTypeahead(
          suggestionsQuery: widget.suggestionQuery,
          queryScope: getOrgScope(context),
          onSelect: _onSelect,
          textFieldConfiguration: configuration,
        ),
      ),
    );
  }

  void _onSelect(text) {
    if (widget.onFieldSubmitted != null) {
      widget.onFieldSubmitted!(text);
    }
    _validateIfNotAllowedNews(text);
  }

  String? _validator(String? text) {
    if (text?.isEmpty ?? true) {
      return null;
    }

    if (widget.validator != null) {
      final result = widget.validator!(text!);
      if (result != null) {
        return result;
      }
    }

    if (widget.allowNews) {
      return null;
    }

    if (_lastValidateText == text) {
      return null;
    }

    if (_lasRejectedText == text) {
      return '';
    }

    _validateIfNotAllowedNews(text!);
    return '';
  }

  void _onFocusChanged(
    bool hasFocus,
    TextEditingController controller,
  ) async {
    if (hasFocus) {
      return;
    }

    final text = controller.text.trim();
    if (!widget.allowNews) {
      await _validateIfNotAllowedNews(text);
      if (_lasRejectedText == text) {
        controller.text = '';
      }
    }
  }

  Future<void> _validateIfNotAllowedNews(String? text) async {
    if ((text?.isEmpty ?? true) || widget.allowNews) {
      return;
    }

    final db = context.read<Repository>();
    final orgScope = getOrgScope(context)!;
    final result = await db.query<List<String>>(
      widget.suggestionQuery,
      context: db.context(queryScope: orgScope.copyWith(pattern: text)),
    );

    if (result.length == 1) {
      _lastValidateText = text!;
    } else {
      _lasRejectedText = text!;
    }
  }
}
