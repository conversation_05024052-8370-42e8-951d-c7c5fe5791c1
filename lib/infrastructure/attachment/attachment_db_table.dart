import 'dart:async';
import 'dart:io';

import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/attachment/attachment_repository.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_contract.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_fields_builder.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_translator.dart';
import 'package:bitacora/infrastructure/attachment/file_util.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_query_scope_utils.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/organization/organization_db_contract.dart';
import 'package:bitacora/infrastructure/project/project_db_contract.dart';
import 'package:bitacora/infrastructure/projectentriesproject/project_entries_project_db_contract.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';

class AttachmentDbTable extends DbTable<Attachment, AttachmentDbFieldsBuilder>
    implements AttachmentRepository<DbContext, AttachmentDbFieldsBuilder> {
  final StreamController<Attachment> _downloadRequests =
      StreamController<Attachment>.broadcast();

  AttachmentDbTable() : super(isSyncable: false) {
    addIdResolver(_idResolverByS3KeyEntryId);
  }

  Future<LocalId?> _idResolverByS3KeyEntryId(
      DbContext context, Attachment model) async {
    if (model.s3Key == null || model.entry?.id == null) {
      return null;
    }

    final result = await (await context.executor).query(tableName,
        columns: [idColumn],
        where: '${contract.entryId} = ? AND ${contract.s3Key} = ?',
        whereArgs: [model.entry!.id!.dbValue, model.s3Key!.dbValue],
        limit: 1);
    return result.isEmpty ? null : LocalId(result[0][idColumn] as int);
  }

  @override
  AttachmentDbContract get contract => const AttachmentDbContract();

  @override
  DbTranslator<Attachment> get translator => const AttachmentDbTranslator();

  @override
  AttachmentDbFieldsBuilder get fieldsBuilder => AttachmentDbFieldsBuilder();

  @override
  List<String> get searchColumns => [];

  @override
  Future<List<Attachment>> findAll(DbContext context, LocalId entryId) {
    return query(
      context,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );
  }

  // FIXME: rename hasPending -> hasMissing + query, etc...
  // FIXME: remove queryScope considerations... anything downloaded/not-uploaded
  @override
  Future<bool> hasPendingUploads(
    DbContext context,
  ) async {
    assert(context.queryScope!.userId != null);
    final query = '''
      SELECT $idColumn
      ${DbQueryScopeUtils().fromJoin(context.queryScope!)}
      LEFT JOIN $tableName
        ON ${const EntryDbContract().id} = ${contract.entryId}
      WHERE
        ${contract.isDownloaded} = ? AND
        ${contract.isUploaded} = ? AND
        ${DbQueryScopeUtils().where(context.queryScope!)}
    ''';

    final executor = await context.executor;
    final pendingUploads = await executor.rawQuery(query, [
      1,
      0,
      context.queryScope!.userId!.dbValue,
    ]);

    return pendingUploads.isNotEmpty;
  }

  @override
  Future<Attachment?> findNextPendingUpload(
    DbContext context,
    bool ignoreRetryLimit,
  ) async {
    final hasFilterOut = context.queryScope?.filterOut?.isNotEmpty ?? false;
    final filterOutCondition = hasFilterOut
        ? DbQueryScopeUtils().filterOutCondition(idColumn, context.queryScope!)
        : null;
    const organizationContract = OrganizationDbContract();
    const projectContract = ProjectDbContract();
    const projectEntriesProjectContract = ProjectEntriesProjectDbContract();

    final where = [
      '${organizationContract.activePlan} != ?',
      '${contract.isDownloaded} = ?',
      '${contract.isUploaded} = ?',
      '''
      (${contract.transferState} = ? OR
        ((${contract.transferState} = ? OR ${contract.transferState} = ?) 
          ${!ignoreRetryLimit ? ' AND ${contract.transferAttempts} < ? ' : ''}
        ) 
      )
      ''',
      if (hasFilterOut) filterOutCondition,
    ];
    final query = '''
      SELECT ${columnsForSelect(context.fields!)}
      FROM ${contract.tableName}
      INNER JOIN ${projectEntriesProjectContract.tableName}
      ON ${projectEntriesProjectContract.entryId} = ${contract.entryId}
      INNER JOIN ${projectContract.tableName}
      ON ${projectContract.id} = ${projectEntriesProjectContract.projectId}
      INNER JOIN ${organizationContract.tableName} 
      ON ${organizationContract.id} = ${projectContract.organizationId}
      WHERE ${where.join(' AND ')}       
      ORDER BY ${contract.transferAttempts} ASC
      LIMIT 1
    ''';

    final pendingUploads = await rawQuery(
      context,
      query,
      [
        OrganizationActivePlan.free.dbValue,
        1,
        0,
        AttachmentTransferState.na.dbValue,
        AttachmentTransferState.failed.dbValue,
        AttachmentTransferState.inProgress.dbValue,
        if (!ignoreRetryLimit) kAttachmentMaxTransferAttempts,
        if (hasFilterOut)
          ...(context.queryScope!.filterOut! as List<LocalId>)
              .map((e) => e.dbValue),
      ],
    );

    return pendingUploads.isEmpty ? null : pendingUploads.first;
  }

  @override
  Future<List<Attachment>> findPendingUpload(DbContext context) async {
    const organizationContract = OrganizationDbContract();
    const projectContract = ProjectDbContract();
    const projectEntriesProjectContract = ProjectEntriesProjectDbContract();

    final query = '''
      SELECT ${columnsForSelect(context.fields!)}
      FROM ${contract.tableName}
      INNER JOIN ${projectEntriesProjectContract.tableName}
      ON ${projectEntriesProjectContract.entryId} = ${contract.entryId}
      INNER JOIN ${projectContract.tableName}
      ON ${projectContract.id} = ${projectEntriesProjectContract.projectId}
      INNER JOIN ${organizationContract.tableName} 
      ON ${organizationContract.id} = ${projectContract.organizationId}
      WHERE ${organizationContract.activePlan} != ? AND
      ${organizationContract.id} = ? AND
      ${contract.isDownloaded} = ? AND
      ${contract.isUploaded} = ? AND
      (${contract.transferState} = ? OR ${contract.transferState} = ?)
      ORDER BY ${contract.transferAttempts} ASC
    ''';

    return rawQuery(
      context,
      query,
      [
        OrganizationActivePlan.free.dbValue,
        context.queryScope!.orgId!.dbValue,
        1,
        0,
        AttachmentTransferState.na.dbValue,
        AttachmentTransferState.failed.dbValue,
      ],
    );
  }

  @override
  Future<LocalId?> save(
    DbContext context,
    Attachment model, {
    bool requestSync = false,
    bool persistTransferDetails = true,
  }) async {
    if (persistTransferDetails) {
      final transferDetails =
          (await _findTransferDetailsForS3Key(context, model.s3Key!)) ??
              (await _findTransferDetailsForPath(context, model.path));

      // Trust local transfer details, except for isUploaded if true.
      // FIXME: isUploaded can never change to false.
      final attachmentToSave = transferDetails == null
          ? model
          : model.copyWith(
              s3Key: transferDetails.s3Key,
              path: transferDetails.path,
              isDownloaded: transferDetails.isDownloaded,
              isUploaded: model.isUploaded!.value
                  ? model.isUploaded
                  : transferDetails.isUploaded,
              transferState: transferDetails.transferState,
              transferAttempts: transferDetails.transferAttempts,
            );
      return super.save(context, attachmentToSave, requestSync: requestSync);
    }
    return super.save(context, model, requestSync: requestSync);
  }

  @override
  Future<void> saveAll(DbContext context, Mutation<Entry> entryMutation) async {
    assert(context.txn != null);

    final attachments = entryMutation.model!.attachments!;
    final entryId = entryMutation.id!;

    final savedAttachmentIds = <LocalId>{};
    for (final attachment in attachments) {
      assert(attachment.entry?.id == null || attachment.entry?.id == entryId);
      final savedId = await save(
        context,
        attachment.entry?.id == null
            ? attachment.copyWith(entry: Entry(id: entryId))
            : attachment,
        persistTransferDetails: entryMutation.type != MutationType.insert,
      );
      savedAttachmentIds.add(savedId ?? attachment.id!);
    }

    if (entryMutation.type != MutationType.insert) {
      await _deleteLeftoverAttachments(context, entryId, savedAttachmentIds);
    }
  }

  @override
  Future<void> onPreDelete(DbContext context, LocalId id) async {
    final attachmentWithPath =
        await find(context.copyWith(fields: fieldsBuilder.path().build()), id);
    final pathVal = attachmentWithPath?.path?.value;
    if (pathVal == null) {
      return;
    }

    final results = await query(
      context.copyWith(fields: fieldsBuilder.build()),
      where: '${contract.path} = ? AND ${contract.id} != ?',
      whereArgs: [pathVal, id.dbValue],
    );
    if (results.isNotEmpty) {
      return;
    }

    final path =
        await AttachmentUtils().getAbsolutePath(attachmentWithPath!.path!);
    await maybeDeleteFile(File(path!));
  }

  @override
  Future<void> deleteAll(DbContext context, LocalId entryId) async {
    final attachments = await findAll(
        context.copyWith(fields: context.db.attachment.fieldsBuilder.build()),
        entryId);

    for (final attachment in attachments) {
      await delete(context, attachment.id!);
    }
  }

  Future<void> _deleteLeftoverAttachments(
    DbContext context,
    LocalId entryId,
    Set<LocalId> updatedAttachmentIds,
  ) async {
    final currentAttachments = await findAll(
        context.copyWith(fields: context.db.attachment.fieldsBuilder.build()),
        entryId);
    for (final attachment in currentAttachments) {
      if (!updatedAttachmentIds.contains(attachment.id)) {
        await delete(context, attachment.id!);
      }
    }
  }

  Future<Attachment?> _findTransferDetailsForS3Key(
    DbContext context,
    AttachmentS3Key key,
  ) async {
    return takeFirst(query(
      context.copyWith(
        fields: context.db.attachment.fieldsBuilder
            .s3Key()
            .path()
            .isUploaded()
            .isDownloaded()
            .transferState()
            .transferAttempts()
            .build(),
      ),
      where: '${contract.s3Key} = ?',
      whereArgs: [key.dbValue],
    ));
  }

  Future<Attachment?> _findTransferDetailsForPath(
    DbContext context,
    AttachmentPath? path,
  ) async {
    final pathVal = path?.dbValue;
    if (pathVal == null) {
      return null;
    }

    return takeFirst(query(
      context.copyWith(
        fields: context.db.attachment.fieldsBuilder
            .s3Key()
            .path()
            .isUploaded()
            .isDownloaded()
            .transferState()
            .build(),
      ),
      where: '${contract.path} = ?',
      whereArgs: [pathVal],
    ));
  }

  @override
  void download(Attachment attachment) {
    _downloadRequests.sink.add(attachment);
  }

  @override
  Stream<Attachment> get downloadRequests {
    return _downloadRequests.stream;
  }

  @override
  Future<void> saveTransferDetails(DbContext context, Attachment attachment) {
    return wrapTransaction(context,
        (wrappedContext) => _doSaveTransferDetails(wrappedContext, attachment));
  }

  Future<void> _doSaveTransferDetails(
      DbContext context, Attachment attachment) async {
    assert(context.txn != null);

    final attachmentTransferDetails = Attachment(
      s3Key: attachment.s3Key!,
      transferState: attachment.transferState!,
      transferAttempts: await _maybeAdjustTransferAttempt(context, attachment),
      path: attachment.path,
      isUploaded: attachment.isUploaded,
      isDownloaded: attachment.isDownloaded,
    );

    final executor = await context.executor;
    final result = await executor.update(
      tableName,
      await translator.toDb(context, attachmentTransferDetails),
      where: '${contract.s3Key} = ?',
      whereArgs: [attachment.s3Key!.dbValue],
    );

    if (result > 0) {
      final mutatedList = await query(
        context.copyWith(fields: fieldsBuilder.build()),
        where: '${contract.s3Key} = ?',
        whereArgs: [attachment.s3Key!.dbValue],
      );
      for (final mutatedEntry in mutatedList) {
        broadcastMutation(Mutation(
          type: MutationType.update,
          id: mutatedEntry.id,
          model: attachmentTransferDetails.copyWith(id: mutatedEntry.id),
        ));
      }
    }
  }

  Future<AttachmentTransferAttempts?> _maybeAdjustTransferAttempt(
      DbContext context, Attachment attachment) async {
    final transferState = attachment.transferState!.value;
    final isSystemicFail =
        context.props?[kAttachmentTransferSystemicFail] ?? false;
    if (!(transferState == AttachmentTransferState.inProgress ||
        (transferState == AttachmentTransferState.failed && isSystemicFail))) {
      /// Increase the count only when we are setting the transfer state to
      /// inProgress or revert when transfer state failed due to systemic cause.
      return null;
    }

    final results = await query(
      context.copyWith(fields: fieldsBuilder.transferAttempts().build()),
      where: '${contract.s3Key} = ?',
      whereArgs: [attachment.s3Key!.dbValue],
    );
    if (results.isEmpty) {
      throw 'Attachment missing';
    }
    final currentTransferAttempts = results.first.transferAttempts!.value;

    if (transferState == AttachmentTransferState.inProgress) {
      return AttachmentTransferAttempts(currentTransferAttempts + 1);
    }

    /// For systemic fail revert the attempt if we are at the max transfer
    /// attempts limit in order to keep retrying.
    if (currentTransferAttempts < kAttachmentMaxTransferAttempts) {
      return null;
    }

    return AttachmentTransferAttempts(currentTransferAttempts - 1);
  }

  @override
  Future<List<Attachment>> findMissing(DbContext context) {
    return query(
      context,
      where: '${contract.path} IS NULL '
          'AND ${contract.isDownloaded} = ? '
          'AND ${contract.isUploaded} = ?',
      whereArgs: [0, 0],
    );
  }
}
