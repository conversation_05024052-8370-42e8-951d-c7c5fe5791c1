import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/util/entry/entry_url_util_snapshot.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/share.dart';
import 'package:bitacora/util/toast/toast.dart';
import 'package:bitacora/util/web_app_launcher.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';

class EntryUrlUtil {
  factory EntryUrlUtil() => inject(() => const EntryUrlUtil._());

  const EntryUrlUtil._();

  Future<void> maybeLaunchEntryInWebApp(
    EntryUrlUtilContextSnapshot contextSnapshot,
    RemoteId remoteId,
  ) async {
    final recoverSessionLauncher =
        contextSnapshot.read<RecoverSessionLauncher>();
    final navigatorState = contextSnapshot.read<NavigatorState>();
    final activeSession = contextSnapshot.read<ActiveSession>();
    final locale = contextSnapshot.read<Locale>().languageCode;

    recoverSessionLauncher.launchOrMaybeRun(
      navigatorState,
      activeSession,
      runIsValid: () async {
        try {
          final hashId =
              await _getHashId(contextSnapshot.read<ApiHelper>(), remoteId);

          WebAppLauncher().maybeLaunchBitacoraWebApp(
            recoverSessionLauncher,
            navigatorState,
            activeSession,
            locale,
            'entry/$hashId',
          );
        } on DioException catch (_) {
          Toast().showNetworkErrorToast();
        }
      },
      forceRecoverSessionUi: true,
    );
  }

  Future<void> shareEntry(
    EntryUrlUtilContextSnapshot contextSnapshot,
    RemoteId remoteId,
  ) async {
    await contextSnapshot.read<RecoverSessionLauncher>().launchOrMaybeRun(
      contextSnapshot.read<NavigatorState>(),
      contextSnapshot.read<ActiveSession>(),
      runIsValid: () async {
        final hashId =
            await _getHashId(contextSnapshot.read<ApiHelper>(), remoteId);

        unawaited(contextSnapshot
            .read<AnalyticsLogger>()
            .logEvent(AnalyticsEvent.shareEntry));
        await Share().share('${AppConfig().webAppUrl}/entry/$hashId');
      },
      forceRecoverSessionUi: true,
    );
  }

  Future<String> _getHashId(ApiHelper apiHelper, RemoteId remoteId) async {
    final hashIdResult = await apiHelper.get<Map<String, dynamic>>(
      'entries/${remoteId.apiValue}/share',
    );

    return hashIdResult.data!['hash_id'];
  }
}
