import 'package:bitacora/application/location_tracking/location_tracking_common_queries.dart';
import 'package:bitacora/application/sync/machine/steps/upload/location_tracking/location_point_outgoing_mutation_uploader.dart';
import 'package:bitacora/application/sync/machine/steps/upload/location_tracking/location_tracking_outgoing_mutation_uploader.dart';
import 'package:bitacora/application/sync/machine/steps/upload/outgoing_mutation_uploader.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:dio/dio.dart';

class LocationTrackingProxyOutgoingMutationUploader
    extends OutgoingMutationUploader {
  late Future<OutgoingMutationUploader> uploader;

  LocationTrackingProxyOutgoingMutationUploader(
    super.params,
    super.mutation,
    super.onProduceJsonFail,
  ) {
    uploader = determineUploader();
  }

  Future<OutgoingMutationUploader> determineUploader() async {
    final locationTracking = mutation.model!.value;
    final notSyncedPoints = await db.query(
      LocationTrackingNotSyncedLocationPointsRepositoryQuery(
          locationTrackingId: locationTracking.id!),
    );

    if (locationTracking.remoteId?.value != null &&
        notSyncedPoints.isNotEmpty) {
      return LocationPointOutgoingMutationUploader(
        params,
        mutation,
        onProduceJsonFail,
      );
    }

    return LocationTrackingOutgoingMutationUploader(
      params,
      mutation,
      onProduceJsonFail,
    );
  }

  @override
  Future<Response?> upload() async {
    return (await uploader).upload();
  }

  @override
  Future<void> updateLocalData(RepositoryQueryContext context, data) async {
    return (await uploader).updateLocalData(context, data);
  }

  @override
  void onUploadDone() async {
    (await uploader).onUploadDone();
  }

  @override
  Future produceJson() => throw UnimplementedError();
}
