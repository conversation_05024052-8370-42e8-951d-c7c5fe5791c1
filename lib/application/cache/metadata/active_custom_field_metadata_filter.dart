import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:flutter/foundation.dart';

class ActiveCustomFieldMetadataFilter
    extends ValueNotifier<CustomFieldMetadata?> {
  ActiveCustomFieldMetadataFilter(super.value);

  void maybeSelectActiveCustomFieldMetadataAfterSave(
    Repository db,
    Organization organization,
    Entry entry,
  ) {
    if (value == null) {
      return;
    }

    if (entry.templatelog == null) {
      value = null;
    }

    if (!entry.templatelog!.fieldsMetadata!
        .map((e) => e.value!)
        .contains(value?.value)) {
      value = null;
    }
  }
}
