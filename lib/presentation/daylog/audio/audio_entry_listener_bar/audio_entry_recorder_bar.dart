import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_bar/audio_entry_listener_bar.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_bar/audio_entry_listener_bar_swipe_to_cancel.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/widgets/audio_timeline_player.dart';
import 'package:bitacora/presentation/widgets/recorder_wave_indicator.dart';
import 'package:bitacora/util/audio_player.dart';
import 'package:bitacora/util/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class AudioEntryRecorderBar extends StatefulWidget {
  final AudioEntryListenerController controller;

  const AudioEntryRecorderBar({super.key, required this.controller});

  @override
  State<AudioEntryRecorderBar> createState() => _AudioEntryRecorderBarState();
}

class _AudioEntryRecorderBarState extends State<AudioEntryRecorderBar> {
  final AudioPlayer _audioPlayer = AudioPlayerInjector.get();

  late final Timer _timer;
  Duration _elapsed = Duration.zero;

  @override
  void initState() {
    super.initState();
    _updateStopwatch();
    _timer =
        Timer.periodic(const Duration(seconds: 1), (_) => _updateStopwatch());
  }

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  void _updateStopwatch() {
    if (widget.controller.stopwatch.isRunning) {
      setState(() {
        _elapsed = widget.controller.stopwatch.elapsed;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: widget.controller.isAnchored,
      builder: (context, isAnchored, _) {
        return ValueListenableBuilder<bool>(
          valueListenable: widget.controller.isListening,
          builder: (context, isListening, __) => StreamBuilder<Duration>(
            stream: _audioPlayer.onPositionChanged,
            builder: (context, snapshot) {
              final duration =
                  isListening ? _elapsed : (snapshot.data ?? Duration.zero);
              return Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Flexible(
                    child: Container(
                      width: 240,
                      padding: const EdgeInsets.only(
                        top: 5.0,
                        bottom: 5.0,
                        right: 15.0,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: const BorderRadius.all(
                          Radius.circular(24.0),
                        ),
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          _AudioEntryListenerBarLeftIconButton(
                            controller: widget.controller,
                          ),
                          if (isAnchored && !isListening)
                            AudioTimelinePlayer(
                              playerPosition: snapshot.hasData
                                  ? snapshot.data!
                                  : Duration.zero,
                              totalDuration:
                                  widget.controller.duration ?? Duration.zero,
                            ),
                          Text(
                            getTimerTimestamp(duration),
                            style: TextStyle(
                              color: Theme.of(context).colorScheme.surface,
                            ),
                          ),
                          if (isAnchored && isListening)
                            const Expanded(
                              child: Padding(
                                padding: EdgeInsets.only(
                                  left: 8.0,
                                  top: 5.0,
                                  bottom: 5.0,
                                ),

                                /// FIXME: Audio waves according to sound and state of the recorder.
                                child: SizedBox(
                                  height: kMinHeightAudioEntryListenerBar / 2,
                                  child: RecorderWaveIndicator(),
                                ),
                              ),
                            ),
                          if (!isAnchored)
                            AudioEntryListenerBarSwipeToCancel(
                              controller: widget.controller,
                            ),
                        ],
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        );
      },
    );
  }
}

class _AudioEntryListenerBarLeftIconButton extends StatelessWidget {
  final AudioEntryListenerController controller;

  const _AudioEntryListenerBarLeftIconButton({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: ValueListenableBuilder<bool>(
          valueListenable: controller.isListening,
          builder: (context, isListening, _) {
            return ValueListenableBuilder<bool>(
                valueListenable: controller.isAnchored,
                builder: (context, isAnchored, _) {
                  if (!isAnchored) {
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 8.0),
                      child: Icon(
                        Icons.mic_outlined,
                        color: isListening
                            ? bitacoraRed
                            : Theme.of(context).scaffoldBackgroundColor,
                      ),
                    );
                  }

                  return GestureDetector(
                    onTap: () =>
                        controller.cancel(context.read<AnalyticsLogger>()),
                    child: CircleAvatar(
                      radius: kMinHeightAudioEntryListenerBar / 3,
                      backgroundColor:
                          Theme.of(context).scaffoldBackgroundColor,
                      child: Icon(
                        Icons.close,
                        color: Theme.of(context)
                            .floatingActionButtonTheme
                            .backgroundColor,
                      ),
                    ),
                  );
                });
          }),
    );
  }
}
