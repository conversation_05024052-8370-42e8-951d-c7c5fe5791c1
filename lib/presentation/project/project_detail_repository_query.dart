import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/project/project.dart';

class ProjectDetailRepositoryQuery extends RepositoryQuery<Project?> {
  final LocalId id;

  const ProjectDetailRepositoryQuery(this.id);

  @override
  Future<Project?> run(RepositoryQueryContext context) =>
      context.db.project.find(context, id);

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.project.fieldsBuilder
          .name()
          .description()
          .address()
          .location()
          .type()
          .build();
}
