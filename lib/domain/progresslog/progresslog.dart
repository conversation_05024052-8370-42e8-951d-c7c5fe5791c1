import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/progresslog/value/progresslog_progress.dart';

export 'package:bitacora/domain/progresslog/value/progresslog_progress.dart';

class Progresslog extends Extension {
  final ProgresslogProgress? progress;

  final Entry? entry;

  const Progresslog({
    super.id,
    super.remoteId,
    this.progress,
    this.entry,
  });

  @override
  ExtensionType get extensionType => ExtensionType.progresslog;

  @override
  Map<Field, dynamic> get fields => {
        ProgresslogField.id: id,
        ProgresslogField.remoteId: remoteId,
        ProgresslogField.progress: progress,
        ProgresslogField.entry: entry,
      };
}

enum ProgresslogField with Field {
  id,
  remoteId,
  progress,
  entry,
}

const progresslogNestedModelFields = {
  ProgresslogField.entry,
};
