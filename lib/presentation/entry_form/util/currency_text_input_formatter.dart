import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class CurrencyTextInputFormatter extends TextInputFormatter {
  static final NumberFormat _currencyFormat0 =
      NumberFormat.simpleCurrency(decimalDigits: 0);
  static final NumberFormat _currencyFormat2 =
      NumberFormat.simpleCurrency(decimalDigits: 2);

  // FIXME: Europe ',' -> '.'
  // FIXME: lots of copy pasta from CentesimalTextInputFormatter
  CurrencyTextInputFormatter();

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // Allow empty
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Check it's a number
    num n;
    try {
      n = _currencyFormat2.parse(newValue.text);
    } catch (_) {
      if (newValue.text.length == 1) {
        // '$1' -> '$' => Deleting the last digit?
        return TextEditingValue.empty;
      } else if (newValue.text.substring(newValue.text.length - 1) == '.') {
        // Currency format doesn't like trailing '.'
        try {
          n = _currencyFormat2
              .parse(newValue.text.substring(0, newValue.text.length - 1));
        } catch (_) {}
      }
      return oldValue;
    }

    // Two digits
    if (_currencyFormat2.parse(n.toStringAsFixed(2)) !=
        _currencyFormat2.parse('$n')) {
      return oldValue;
    }

    // Add possible trail.
    // i.e. User might be typing 1.0, we shouldn't reformat it to 1 in case
    // they were trying to reach 1.01
    final split = newValue.text.split('.');
    String? trail;
    if (split.length == 2) {
      trail = split[1];
      if (trail.length > 2) {
        trail = trail.substring(0, 2);
      }
    }

    // Reformat it, but keep the trail for now. This adds commas: 1000 -> 1,000
    final fixedNewValue = '${_currencyFormat0.format(n.toInt())}'
        '${trail != null ? '.$trail' : ''}';
    final lengthDelta = fixedNewValue.length - newValue.text.length;
    return TextEditingValue(
      text: fixedNewValue,
      selection: newValue.selection.copyWith(
        baseOffset: newValue.selection.baseOffset + lengthDelta,
        extentOffset: newValue.selection.extentOffset + lengthDelta,
      ),
      composing: newValue.composing,
    );
  }
}
