import 'package:flutter/material.dart';

class PageViewDotsIndicator extends StatefulWidget {
  final PageController controller;
  final int pageCount;

  const PageViewDotsIndicator(
      {super.key, required this.controller, required this.pageCount});

  @override
  State<PageViewDotsIndicator> createState() => _PageViewDotsIndicatorState();
}

class _PageViewDotsIndicatorState extends State<PageViewDotsIndicator> {
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_updateCurrentPage);
  }

  @override
  void dispose() {
    widget.controller.removeListener(_updateCurrentPage);
    super.dispose();
  }

  void _updateCurrentPage() {
    final nextPage = widget.controller.page!.round();
    if (_currentPage != nextPage) {
      setState(() {
        _currentPage = nextPage;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.pageCount <= 1) {
      return const SizedBox();
    }
    return Row(
      children: List.generate(
        widget.pageCount,
        (index) => Padding(
          padding: const EdgeInsets.all(4.0),
          child: InkWell(
            onTap: () {
              widget.controller.animateToPage(index,
                  duration: const Duration(milliseconds: 200),
                  curve: Curves.easeInOut);
            },
            child: Container(
              width: 8.0,
              height: 8.0,
              decoration: BoxDecoration(
                color: index == _currentPage
                    ? Theme.of(context).colorScheme.primary
                    : Colors.grey,
                borderRadius: BorderRadius.circular(8.0),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
