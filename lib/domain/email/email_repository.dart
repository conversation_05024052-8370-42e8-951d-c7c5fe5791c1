import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/email/email.dart';
import 'package:bitacora/domain/email/email_post_fields_builder.dart';

abstract class EmailRepository<C extends RepositoryQueryContext,
    F extends EmailFieldsBuilder> extends RepositoryTable<Email, C, F> {
  Future<List<Email>> findAll(C context, LocalId personDetailId);
}
