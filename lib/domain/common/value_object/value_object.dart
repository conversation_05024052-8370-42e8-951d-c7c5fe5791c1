class ValueObject<T> {
  final T value;

  const ValueObject(this.value);

  @override
  bool operator ==(Object other) =>
      identical(other, this) ||
      (isSameType(other) && other.hashCode == hashCode);

  @override
  int get hashCode => value?.hashCode ?? 0;

  bool isSameType(Object other) {
    return other is ValueObject;
  }

  dynamic get dbValue => value;

  dynamic get apiValue => dbValue;

  String get displayValue => value == null ? '' : '$value';

  @override
  String toString() => 'ValueObject[$displayValue]';
}
