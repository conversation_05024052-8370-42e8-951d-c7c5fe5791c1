import 'package:bitacora/presentation/entry_form/options/tags/entry_form_tag_option_actions.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../mocktail_fallback_values.dart';
import '../../util/mocks.dart';
import '_robots/entry_form_tag_options_actions_test_robot.dart';

void main() {
  group('$EntryFormTagOptionActions test', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Is Tag selecting', (tester) async {
      final robot = EntryFormTagOptionActionsTestRobot(
        tester,
        collectionSelection: mockCollectionSelection(isSelecting: true),
      );

      await robot.pumpWidget();

      robot.verifyUi();
    });

    testWidgets('No Tag is selected', (tester) async {
      final robot = EntryFormTagOptionActionsTestRobot(
        tester,
        collectionSelection: mockCollectionSelection(isSelecting: false),
      );

      await robot.pumpWidget();

      robot.verifyUi();
    });

    testWidgets('Delete Selection', (tester) async {
      final robot = EntryFormTagOptionActionsTestRobot(
        tester,
        collectionSelection: mockCollectionSelection(isSelecting: true),
      );
      await robot.pumpWidget();
      robot.verifyUi();

      await robot.tapDeleteSelection();

      robot.verifyDeleteSelection();
    });

    testWidgets('Clear Selection', (tester) async {
      final robot = EntryFormTagOptionActionsTestRobot(
        tester,
        collectionSelection: mockCollectionSelection(isSelecting: true),
      );
      await robot.pumpWidget();
      robot.verifyUi();

      await robot.tapClearSelection();

      robot.verifyClearSelection();
    });

    testWidgets('On Add Tag', (tester) async {
      var added = false;
      final robot = EntryFormTagOptionActionsTestRobot(
        tester,
        collectionSelection: mockCollectionSelection(isSelecting: false),
        onAddFromTextFields: () {
          added = true;
        },
      );
      await robot.pumpWidget();
      robot.verifyUi();

      await robot.tapAddTag();

      expect(added, true);
    });
  });
}
