import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/template_block/template_block.dart';
import 'package:bitacora/domain/template_group/template_group.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_api_translator.dart';

class TemplateBlockApiTranslator implements ModelTranslator<TemplateBlock> {
  const TemplateBlockApiTranslator();

  @override
  TemplateBlock fromMap(Map<String, dynamic> data) {
    return TemplateBlock(
      remoteId: RemoteId(data['id']),
      row: TemplateBlockRow(data['row']),
      order: TemplateBlockOrder(data['order']),
      weight: TemplateBlockWeight(data['weight']),
      role: TemplateBlockRole.fromApiValue(data['role']),
      templateGroup: TemplateGroup(id: LocalId(data['template_group_id'])),
      customFieldOptions: const CustomFieldOptionsApiTranslator()
          .fromMap(data['custom_field_option']),
    );
  }

  @override
  Map<String, dynamic> toMap(TemplateBlock model) {
    throw UnimplementedError();
  }
}
