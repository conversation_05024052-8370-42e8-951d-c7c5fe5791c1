import 'package:bitacora/domain/common/api_enum.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:flutter/widgets.dart';
import 'package:bitacora/l10n/app_localizations.dart';

/// FIXME: Always use UNIFIED_ENTRIES, filter record types via extension_type or template_id params.
enum ReportEntryTypeValue with ApiEnum {
  all('UNIFIED_ENTRIES'),
  worklog('WORKLOG'),
  inventorylog('INVENTORYLOG'),
  personnellog('PERSONNELLOG'),
  templatelog('TEMPLATELOG');

  @override
  final String apiValue;

  const ReportEntryTypeValue(this.apiValue);

  factory ReportEntryTypeValue.fromString(String apiValue) =>
      ApiEnum.fromApiValue(values, apiValue);

  String getDisplayValue(BuildContext context) {
    switch (this) {
      case ReportEntryTypeValue.all:
        return AppLocalizations.of(context)!.all;
      case ReportEntryTypeValue.worklog:
        return AppLocalizations.of(context)!.log;
      case ReportEntryTypeValue.inventorylog:
        return AppLocalizations.of(context)!.inventory;
      case ReportEntryTypeValue.personnellog:
        return AppLocalizations.of(context)!.personnel;
      case ReportEntryTypeValue.templatelog:
        return '';
    }
  }

  String getUrlValue() {
    switch (this) {
      case ReportEntryTypeValue.all:
      case ReportEntryTypeValue.templatelog:
        throw 'No supported';
      case ReportEntryTypeValue.worklog:
        return 'worklogs';
      case ReportEntryTypeValue.inventorylog:
        return 'inventorylogs';
      case ReportEntryTypeValue.personnellog:
        return 'personnellogs';
    }
  }
}

class ReportEntryType {
  final ReportEntryTypeValue value;
  final Template? template;

  ReportEntryType({required this.value, this.template});

  static List<ReportEntryType> get staticValues => [
        ReportEntryType.all(),
        ReportEntryType.worklog(),
        ReportEntryType.inventorylog(),
        ReportEntryType.personnellog(),
      ];

  factory ReportEntryType.fromValue(String value) {
    return ReportEntryType(value: ReportEntryTypeValue.fromString(value));
  }

  factory ReportEntryType.all() {
    return ReportEntryType(value: ReportEntryTypeValue.all);
  }

  factory ReportEntryType.worklog() {
    return ReportEntryType(value: ReportEntryTypeValue.worklog);
  }

  factory ReportEntryType.inventorylog() {
    return ReportEntryType(value: ReportEntryTypeValue.inventorylog);
  }

  factory ReportEntryType.personnellog() {
    return ReportEntryType(value: ReportEntryTypeValue.personnellog);
  }

  factory ReportEntryType.templatelog(Template template) {
    return ReportEntryType(
        value: ReportEntryTypeValue.templatelog, template: template);
  }

  String getDisplayValue(BuildContext context) {
    return value == ReportEntryTypeValue.templatelog
        ? template?.name!.displayValue ?? value.getDisplayValue(context)
        : value.getDisplayValue(context);
  }

  @override
  int get hashCode => super.hashCode + value.hashCode + template.hashCode;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }

    if (other.runtimeType != runtimeType) {
      return false;
    }

    other as ReportEntryType;
    return (other.value == value && other.template == template);
  }
}
