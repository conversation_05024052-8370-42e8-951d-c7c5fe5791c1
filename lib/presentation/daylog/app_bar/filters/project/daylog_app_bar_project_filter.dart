import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/presentation/daylog/app_bar/filters/project/daylog_filters_project.dart';
import 'package:bitacora/util/bottom_sheet/bottom_sheet_utils.dart';

import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

const kFontSize = 14.0;

class DaylogAppBarProjectFilter extends StatefulWidget {
  final Widget selectedItemSubtitle;

  const DaylogAppBarProjectFilter({
    super.key,
    required this.selectedItemSubtitle,
  });

  @override
  State<DaylogAppBarProjectFilter> createState() =>
      _DaylogAppBarProjectFilterState();
}

class _DaylogAppBarProjectFilterState extends State<DaylogAppBarProjectFilter> {
  @override
  Widget build(BuildContext context) {
    final activeProject = context.watch<ActiveProject>();
    final allProjectsOption = _getAllProjectsOption(context);

    final theme = Theme.of(context);
    final isAllOptionSelected = activeProject.value == null;
    return InkWell(
      borderRadius: BorderRadius.circular(8.0),
      onTap: () {
        _show(context);
      },
      child: Row(
        children: [
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(4.0),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: EdgeInsets.only(
                      left: isAllOptionSelected ? 0.0 : 4.0,
                      top: 2.0,
                      bottom: 2.0,
                    ),
                    decoration: BoxDecoration(
                      color: isAllOptionSelected
                          ? Colors.transparent
                          : theme.colorScheme.primary,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Flexible(
                          child: Text(
                            activeProject.value?.name!.displayValue ??
                                allProjectsOption.name!.displayValue,
                            overflow: TextOverflow.fade,
                            softWrap: false,
                            style: TextStyle(
                              color: isAllOptionSelected
                                  ? theme.colorScheme.onSurface
                                  : theme.colorScheme.surface,
                              fontSize: kFontSize,
                            ),
                          ),
                        ),
                        Icon(
                          Icons.arrow_drop_down,
                          color: isAllOptionSelected
                              ? theme.colorScheme.onSurface
                              : theme.colorScheme.surface,
                          size: 16,
                        )
                      ],
                    ),
                  ),
                  const SizedBox(height: 2.0),
                  widget.selectedItemSubtitle,
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _show(BuildContext context) {
    const BottomSheetUtils().show(context, const DaylogFiltersProject());
  }

  Project _getAllProjectsOption(BuildContext context) {
    return Project(
      name: ProjectName(AppLocalizations.of(context)!.allProjects),
    );
  }
}
