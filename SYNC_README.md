# Sync

## Why? Because.

We sync THE ENTIRE user dataset (for the current active organization) in order to enable rich offline features in the
client. We also strive for all UI to respond immediately (no network lag).

i.e. we can do the following offline and very quickly:

1. View any entry.
2. Edit any entry.
3. Create an entry.
4. Suggest texts for autocompletion.
5. <PERSON><PERSON> about upcoming tasks.
6. Invite users. * Hybrid: No automatic retries.
7. Create reports. * Hybrid: No automatic retries, report generated in server. Fully local feature to be implemented.

The app's design philosophy is "database first". UI saves to local DB first and then Sync is triggered. Remote data
saves to local DB first and then UI updates.

## What? Steps.

Sync is a multistep process.

During a Sync iteration, some steps may not fire at all, if they consider themselves to already have been successfully
synced. Some steps fire in parallel, while others fire sequentially.

#### 1. Head

Downloads the user's organizations and projects, with metadata to help the client understand the server-side db state
and what to download.

#### 2. Access

Downloads the user's access model. This defines what resources should be available to the user.

#### 3. Users

Downloads the entire organization's users.

#### 4. Download

Downloads missing entries. Iterates through projects and queries the server for entries updated (or inserted)
after a certain time, based on project metadata from Head and previous iterations of this step.

#### 5. Upload

Uploads missing entries. Iterates through the outgoing mutations table and uploads whatever is pending.

> What happens if an upload fails?
>
> It should try again in the next Sync iteration, with a retry-limit (5). If the user triggers Sync intentionally
(Sync button), the retry-limit is ignored.

#### 6. Attachments

Uploads missing attachments. Iterates through the attachments table and uploads whatever is pending.

If the attachment upload fails, it retries with the same rules as entry-upload (retry-limit and forced Sync).

## How? Triggers.

Sync can be triggered in the foreground or in the background under various scenarios.

### Foreground Sync

Sync is normally run in the foreground and is triggered by the following actions:

1. `Session` On login.
2. `Session` On app start if logged-in.
3. `Session` On app resumed if Sync pending.
4. `Connectivity` On connectivity restored.
5. `Db` On entry save.
6. `UserInvite` On user added to org.
7. `Notification` On specific FCM payload.

#### What happens if Foreground Sync fails?

If the app is still in foreground, any of the above will trigger a Sync retry. Otherwise, a Background Sync should
eventually trigger.

### Background Sync

Sync is run in the background in the following scenarios:

1. `Interrupt` If app interrupts abruptly during sync.
2. `Background` If app is backgrounded and Sync is pending.
3. `Periodic Task` If Sync pending.
4. `Notification` On specific FCM payload.

#### How is Background Sync scheduled?

`Interrupt` and `Background` schedule through a pinging mechanism.

When a Foreground Sync triggers, a Background Sync is simultaneously scheduled to run some time in the future (1 min). A
short time before the Background Sync is set to trigger, we check if we are still in foreground and reschedule for
later. This process repeats while the app is in foreground and there is a pending sync. If the Foreground Sync finishes
successfully, the Background Sync is canceled.

`Periodic Task` and `Notification` trigger immediately.

#### What happens if Background Sync fails?

`Periodic Task` should eventually trigger Background Sync again.

#### Do `bit4a` and `bit4ios` behave the same way?

No. `bit4a` performs one-off `Workmanager` tasks more reliably on the expected time. `bit4ios` may delay the scheduled
Background Sync indefinitely, and relies more heavily on `Periodic Task`.

#### Can Syncs trigger simultaneously?

Yes.

1. Foreground Sync could be running when `Periodic Task`
   triggers.
2. Background Sync could be running when `Periodic Task`
   triggers.
3. Background Sync could be running when a `Notification`
   triggers.
4. Background Sync could be running when the app is foregrounded and a `Session` triggers Foreground Sync.

There is no effective way to communicate between the
`Workmanager` processes themselves or with the foregrounded app. So to solve this problem, we use a pinging lock
mechanism. *TO BE IMPLEMENTED
