import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/infrastructure/project/project_api_translator.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('$ProjectApiTranslator tests', () {
    test('Project without remote id sends name and organization id', () {
      const name = 'My Project';
      const orgId = 321;
      const project = Project(
        remoteId: RemoteId(null),
        name: ProjectName(name),
        organization: Organization(remoteId: RemoteId(orgId)),
      );
      const projectApiTranslator = ProjectApiTranslator();

      final map = projectApiTranslator.toMap(project);

      expect(map['id'], null);
      expect(map['name'], name);
      expect(map['organization_id'], orgId);
    });

    test('Project with remote id sends id', () {
      const id = 123;
      const project = Project(remoteId: RemoteId(id));
      const projectApiTranslator = ProjectApiTranslator();

      final map = projectApiTranslator.toMap(project);

      expect(map['name'], null);
      expect(map['organization_id'], null);
      expect(map['id'], id);
    });
  });
}
