import 'dart:math';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:console/console.dart';

final Random _random = Random();
const chars = '''
∀∁∂∃∄∅∆∇∈∉∊∋∌∍∎∏
∐∑−∓∔∕∖∗∘∙√∛∜∝∞∟
∠∡∢∣∤∥∦∧∨∩∪∫∬∭∮∯
∰∱∲∳∴∵∶∷∸∹∺∻∼∽∾∿
≀≁≂≃≄≅≆≇≈≉≊≋≌≍≎≏
≐≑≒≓≔≕≖≗≘≙≚≛≜≝≞≟
≠≡≢≣≤≥≦≧≨≩≪≫≬≭≮≯
≰≱≲≳≴≵≶≷≸≹≺≻≼≽≾≿
⊀⊁⊂⊃⊄⊅⊆⊇⊈⊉⊊⊋⊌⊍⊎⊏
⊐⊑⊒⊓⊔⊕⊖⊗⊘⊙⊚⊛⊜⊝⊞⊟
⊠⊡⊢⊣⊤⊥⊦⊧⊨⊩⊪⊫⊬⊭⊮⊯
⊰⊱⊲⊳⊴⊵⊶⊷⊸⊹⊺⊻⊼⊽⊾⊿
⋀⋁⋂⋃⋄⋅⋆⋇⋈⋉⋊⋋⋌⋍⋎⋏
⋐⋑⋒⋓⋔⋕⋖⋗⋘⋙⋚⋛⋜⋝⋞⋟
⋠⋡⋢⋣⋤⋥⋦⋧⋨⋩⋪⋫⋬⋭⋮⋯
⋰⋱⋲⋳⋴⋵⋶⋷⋸⋹⋺⋻⋼⋽⋾⋿
''';

void printUniverse() async {
  for (var row = 0; row < Console.rows; row++) {
    for (var column = 0; column < Console.columns; column++) {
      _printPixel(row, column);
    }
  }
}

void _printPixel(int row, int column) {
  final dx = (column - Console.columns / 2);
  final dy = (row - Console.rows / 2);
  final dz = 300 ~/ sqrt(sqrt(dx * dx + dy * dy).round() + 1);
  if (_random.nextInt(dz) < 20) {
    return;
  }

  if (_random.nextInt(dz) < 40) {
    Console.write(kAnsiColorRed);
  } else {
    Console.write(
      _random.nextInt(dz) < 80 ? kAnsiColorYellow : kAnsiColorGreen,
    );
  }
  Console.moveCursor(row: row, column: column);
  final charPos = _random.nextInt(chars.length);
  final char = chars.substring(charPos, charPos + 1);
  Console.write(char);
}
