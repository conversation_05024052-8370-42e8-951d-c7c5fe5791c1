import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/ascii/bitacora_banner_printer.dart';
import 'package:bit/ascii/skull_printer.dart';
import 'package:bit/ascii/universe_printer.dart';
import 'package:console/console.dart';

Future<void> printHeader({
  Function(int frame)? inBetweenPrinter,
  bool animate = false,
  int frames = 60,
}) async {
  writeln(
    '━╾────╼━━╾────╼━━╾────╼━━╾────╼━━╾────╼━━╾────╼━━╾────╼━━╾────╼━━╾────╼━',
    TextStyle.focus,
  );
  if (!animate) {
    _printHeader(inBetweenPrinter);
    return;
  }
  for (var frame = 0; frame < frames; frame++) {
    _printHeader(inBetweenPrinter, frame);
    await Future.delayed(Duration(milliseconds: 8), () => null);
  }
}

void _printHeader(Function(int frame)? inBetweenPrinter, [int frame = -1]) {
  _printHeaderBack(frame);
  if (inBetweenPrinter != null) {
    inBetweenPrinter(frame);
  }
  _printHeaderFront(frame);
}

void _printHeaderBack(int frame) {
  if (frame > 0) {
    return;
  }

  Console.write(Process.runSync('clear', [], runInShell: true).stdout);
  Console.moveCursor(row: 0);
  printUniverse();
  printBitacoraBanner();
}

void _printHeaderFront(frame) {
  printSkull(frame);
}
