import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_label.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_value.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../mocktail_fallback_values.dart';
import '_robots/template_block_form_checkbox_group_test_robot.dart';

void main() {
  group('TemplateBlockFormCheckboxGroup tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Renders checkbox group with allowed values', (tester) async {
      final robot = TemplateBlockFormCheckboxGroupTestRobot(tester);

      final allowedValues = [
        CustomFieldAllowedValue(
          id: LocalId(1),
          value: CustomFieldAllowedValueValue('Option 1'),
          label: CustomFieldAllowedValueLabel('Option 1'),
        ),
        CustomFieldAllowedValue(
          id: LocalId(2),
          value: CustomFieldAllowedValueValue('Option 2'),
          label: CustomFieldAllowedValueLabel('Option 2'),
        ),
        CustomFieldAllowedValue(
          id: LocalId(3),
          value: CustomFieldAllowedValueValue('Option 3'),
          label: CustomFieldAllowedValueLabel('Option 3'),
        ),
      ];

      await robot.pumpWidget(allowedValues: allowedValues);

      robot.verifyCheckboxesCount(3);
      robot.verifyCheckboxLabels(['Option 1', 'Option 2', 'Option 3']);
      robot.verifyAllCheckboxesUnchecked();
    });

    testWidgets('Selects and deselects values when tapped', (tester) async {
      final robot = TemplateBlockFormCheckboxGroupTestRobot(tester);

      final allowedValues = [
        CustomFieldAllowedValue(
          id: LocalId(1),
          value: CustomFieldAllowedValueValue('Option 1'),
          label: CustomFieldAllowedValueLabel('Option 1'),
        ),
        CustomFieldAllowedValue(
          id: LocalId(2),
          value: CustomFieldAllowedValueValue('Option 2'),
          label: CustomFieldAllowedValueLabel('Option 2'),
        ),
      ];

      await robot.pumpWidget(allowedValues: allowedValues);

      robot.verifyCheckboxesCount(2);
      robot.verifyAllCheckboxesUnchecked();

      await robot.tapCheckbox(0);
      robot.verifyCheckboxChecked(0, true);
      robot.verifyCheckboxChecked(1, false);
      robot.verifyControllerValue([allowedValues[0]]);

      await robot.tapCheckbox(1);
      robot.verifyCheckboxChecked(0, true);
      robot.verifyCheckboxChecked(1, true);
      robot.verifyControllerValue([allowedValues[0], allowedValues[1]]);

      await robot.tapCheckbox(0);
      robot.verifyCheckboxChecked(0, false);
      robot.verifyCheckboxChecked(1, true);
      robot.verifyControllerValue([allowedValues[1]]);
    });

    testWidgets('Displays selected values in read-only mode', (tester) async {
      final robot = TemplateBlockFormCheckboxGroupTestRobot(tester);

      final allowedValues = [
        CustomFieldAllowedValue(
          id: LocalId(1),
          value: CustomFieldAllowedValueValue('Option 1'),
          label: CustomFieldAllowedValueLabel('Option 1'),
        ),
        CustomFieldAllowedValue(
          id: LocalId(2),
          value: CustomFieldAllowedValueValue('Option 2'),
          label: CustomFieldAllowedValueLabel('Option 2'),
        ),
      ];

      await robot.pumpWidget(
        allowedValues: allowedValues,
        isEditable: false,
        initialSelectedValues: [allowedValues[0], allowedValues[1]],
      );

      robot.verifyReadOnlyText('Option 1, Option 2');
    });

    testWidgets('Displays dash when no values selected in read-only mode',
        (tester) async {
      final robot = TemplateBlockFormCheckboxGroupTestRobot(tester);

      final allowedValues = [
        CustomFieldAllowedValue(
          id: LocalId(1),
          value: CustomFieldAllowedValueValue('Option 1'),
          label: CustomFieldAllowedValueLabel('Option 1'),
        ),
        CustomFieldAllowedValue(
          id: LocalId(2),
          value: CustomFieldAllowedValueValue('Option 2'),
          label: CustomFieldAllowedValueLabel('Option 2'),
        ),
      ];

      await robot.pumpWidget(
        allowedValues: allowedValues,
        isEditable: false,
        initialSelectedValues: [],
      );

      robot.verifyReadOnlyText('-');
    });
  });
}
