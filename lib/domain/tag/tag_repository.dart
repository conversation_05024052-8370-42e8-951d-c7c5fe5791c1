import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/tag/tag_fields_builder.dart';

abstract class TagRepository<C extends RepositoryQueryContext,
    F extends TagFieldsBuilder> extends RepositoryTable<Tag, C, F> {
  Future<List<Tag>> findByOrganization(C context, LocalId orgId);

  Future<List<Tag>> findAll(C context, LocalId entryId);

  Future<void> saveAll(C context, Mutation<Entry> entryMutation);

  Future<void> deleteAll(C context, LocalId entryId);

  Future<List<String>> names(C context, List<String> filterOut);

  Future<List<Tag>> searchAll(C context);
}
