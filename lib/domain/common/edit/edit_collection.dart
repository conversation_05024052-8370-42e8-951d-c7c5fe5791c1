import 'package:bitacora/domain/common/edit/edit_model.dart';
import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:flutter/material.dart';

typedef EditModelBuilder<T extends Model> = EditModel<T> Function(
    ValueNotifier<T?> liveModel);

class EditCollection<T extends Model>
    extends ValueNotifier<List<EditModel<T>>> {
  final EditModelBuilder<T> editModelBuilder;
  final List<EditModel<T>> _inserts = [];
  final List<LocalId> _deletes = [];

  Map<LocalId, EditModel<T>> _editableDbCollection = {};

  EditCollection(Iterable<LiveModel<T>> dbCollection, this.editModelBuilder)
      : super([]) {
    updateListFromDb(dbCollection);
  }

  void updateListFromDb(Iterable<LiveModel<T>> dbCollection) {
    final updatedDbCollection = <LocalId, EditModel<T>>{};
    for (final dbItem in dbCollection) {
      final id = dbItem.value!.id!;
      updatedDbCollection[id] = _editableDbCollection[id] != null
          ? _editableDbCollection[id]!
          : editModelBuilder(dbItem);
    }
    for (final entry in _editableDbCollection.entries) {
      if (!updatedDbCollection.containsKey(entry.key)) {
        entry.value.dispose();
      }
    }
    _editableDbCollection = updatedDbCollection;
    _update();
  }

  void insert(List<T> models) {
    for (final model in models) {
      assert(model.id == null);
      _inserts.add(editModelBuilder(ValueNotifier(model)));
    }
    _update();
  }

  void delete(List<EditModel<T>> models) {
    for (final model in models) {
      if (model.liveModel.value?.id != null) {
        _deletes.add(model.liveModel.value!.id!);
      } else {
        _inserts.remove(model);
      }
    }
    _update();
  }

  void _update() {
    final updatedCollection = <EditModel<T>>[];
    for (final entry in _editableDbCollection.entries) {
      if (!_deletes.contains(entry.key)) {
        updatedCollection.add(entry.value);
      }
    }
    updatedCollection.addAll(_inserts);
    value = updatedCollection;
  }
}
