import 'package:bitacora/domain/common/value_object/int_value_object.dart';
import 'package:intl/intl.dart';

class CentesimalValueObject extends IntValueObject {
  static final _numberFormat = NumberFormat('###,###.##');

  const CentesimalValueObject(super.value);

  @override
  bool isSameType(Object other) {
    return other is CentesimalValueObject;
  }

  @override
  String get displayValue => value == null ? '' : format(value!);

  static int parse(String text) {
    return (_numberFormat.parse(text) * 100).round();
  }

  static String format(int n) {
    return _numberFormat.format(n / 100);
  }
}

class NonNullableCentesimalValueObject extends NonNullableIntValueObject {
  const NonNullableCentesimalValueObject(super.value);

  @override
  bool isSameType(Object other) {
    return other is CentesimalValueObject;
  }

  @override
  String get displayValue => CentesimalValueObject.format(value);
}
