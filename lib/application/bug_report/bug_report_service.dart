import 'dart:async';
import 'dart:convert';
import 'dart:io' as io;

import 'package:archive/archive_io.dart';
import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/error/error_logger.dart';
import 'package:bitacora/application/notification/notification_id_offsets.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/infrastructure/amplify/amplify_storage_util.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/awesome_notifications/awesome_notifications_channels.dart';
import 'package:bitacora/util/awesome_notifications/awesome_notifications_utils.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/device_info_plugin.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/logger/logger_to_file_output.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:intl/intl.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:uuid/uuid.dart';

const _kGuestUserId = 'Guest';
const _kTotalSteps = 10;

typedef BugReportServiceCallback = void Function(int step, int totalSteps);

class BugReportService {
  factory BugReportService() => inject(() => const BugReportService._());

  const BugReportService._();

  DateFormat get dateFormat => DateFormat('yyyy-MM-dd HH:mm');

  Future<String> getDirectory() async {
    return (await getTemporaryDirectory()).path;
  }

  Future<File> createAndSend(
    Repository db,
    Session? session,
    AnalyticsLogger analyticsLogger, {
    required String description,
    bool isSimpleName = false,
    bool showNotification = true,
    bool shouldUploadToAmplify = true,
    bool shouldLogAnalytic = true,
    BugReportServiceCallback? callback,
  }) async {
    logger.i('bug-report-service:create New bug report');
    final timestamp = Clock().now().millisecondsSinceEpoch;
    final userId = session?.user.remoteId!.apiValue.toString() ?? _kGuestUserId;

    final filename =
        isSimpleName ? 'bug_report.zip' : 'bug_report_${userId}_$timestamp.zip';
    final filepath = path.join(
      await getDirectory(),
      filename,
    );
    final s3Key = 'bug_report_${userId}_${timestamp}_${const Uuid().v4()}';

    final zip =
        await _zip(db, filepath, timestamp, userId, description, callback);

    if (showNotification) {
      await _showNotification(timestamp, filename);
    }

    _reportToCallback(callback, 9);
    if (shouldLogAnalytic) {
      _logAnalytic(analyticsLogger, s3Key, description);
    }

    _reportToCallback(callback, 10);
    if (shouldUploadToAmplify) {
      unawaited(_upload(filepath, s3Key));
    }

    logger.i('bug-report-service:create Bug report created.');

    return zip;
  }

  Future<File> _zip(
    Repository db,
    String filepath,
    int timestamp,
    String userId,
    String description,
    BugReportServiceCallback? callback,
  ) async {
    _reportToCallback(callback, 1);
    logger.i('bug-report-service:zip Creating zip file');
    final fileSystem = FileSystemInjector.get();
    final encoder = ZipFileEncoder();
    encoder.create(filepath);

    _reportToCallback(callback, 2);
    logger.i('bug-report-service:zip Adding header file');
    await encoder.addFile(await _getHeaderFile(timestamp, userId, description));

    _reportToCallback(callback, 3);
    logger.i('bug-report-service:zip Adding log files');
    await encoder.addDirectory(await LoggerToFileOutput.getLogsDirectory());

    _reportToCallback(callback, 4);
    logger.i('bug-report-service:zip Adding bitacora.db');
    await encoder.addFile(fileSystem.file(await db.path));

    _reportToCallback(callback, 5);
    logger.i('bug-report-service:zip Adding shared prefs');
    await encoder.addFile(await _getSharedPrefsFile());

    _reportToCallback(callback, 6);
    logger.i('bug-report-service:zip Adding attachment contents');
    await encoder.addFile(await _getAttachmentContentsFile());

    _reportToCallback(callback, 7);
    logger.i('bug-report-service:zip Adding report contents');
    await encoder.addFile(await _getReportContentFile());

    encoder.closeSync();
    _reportToCallback(callback, 8);
    logger.i('bug-report-service:zip Done $filepath');
    return fileSystem.file(filepath);
  }

  Future<void> _showNotification(int timestamp, String filename) {
    return AwesomeNotificationsUtils().createNotification(
      content: NotificationContent(
        id: (kBugReportsIdStart + (timestamp % 10000)),
        title: AppLocalizationsResolver.get().bugReport,
        body: AppLocalizationsResolver.get().bugReportTapToShare,
        channelKey: NotificationChannels.bugReport.key,
        payload: {'filename': filename},
        displayOnForeground: true,
      ),
    );
  }

  Future<File> _getHeaderFile(
    int timestamp,
    String userId,
    String description,
  ) async {
    final tempDirectory = await getTemporaryDirectory();
    final file = FileSystemInjector.get()
        .file(path.join(tempDirectory.path, 'bug_report_$timestamp.txt'));
    final creationDate =
        dateFormat.format(DateTime.fromMillisecondsSinceEpoch(timestamp));
    final packageInfo = await PackageInfo.fromPlatform();
    final packageNameAndVersion = '${AppConfig().appName} '
        '${AppConfig().targetName} '
        '${packageInfo.packageName} v${packageInfo.version}\n';

    await file.writeAsString('''
Bug Report v1.2

$packageNameAndVersion
${await _getOsInfo()}

User ID: $userId
Creation Date: $creationDate

Description: 
$description

Files included:
- logs/*
- db ${await _getDbCreationInfo()}
- sharedprefs
- attachments directory stats
- reports directory stats
      ''');
    return file;
  }

  Future<String> _getOsInfo() async {
    final info = DeviceInfoPluginInjector.get();
    if (io.Platform.isAndroid) {
      final androidInfo = await info.androidInfo;
      final release = androidInfo.version.release;
      final sdkInt = androidInfo.version.sdkInt;
      final manufacturer = androidInfo.manufacturer;
      final model = androidInfo.model;
      final arch = androidInfo.supportedAbis;
      return 'android $release (SDK $sdkInt), $manufacturer $model\n$arch';
      // Android 9 (SDK 28), Xiaomi Redmi Note 7
      // [arm64-v8a, armeabi-v7a, armeabi]
    }

    if (io.Platform.isIOS) {
      final iosInfo = await info.iosInfo;
      final systemName = iosInfo.systemName;
      final version = iosInfo.systemVersion;
      final name = iosInfo.name;
      final model = iosInfo.model;
      return '$systemName $version, $name $model\n[arm64]';
      // iOS 13.1, iPhone 11 Pro Max iPhone
      // [arm64]
      // https://docs.flutter.dev/reference/supported-platforms
    }

    return '${(await info.deviceInfo).data}';
  }

  Future<File> _getSharedPrefsFile() async {
    final tempDirectory = await getTemporaryDirectory();
    final prefs = await SharedPreferences.getInstance();
    final map = <String, dynamic>{};
    for (final key in prefs.getKeys()) {
      map[key] = prefs.get(key);
    }

    final file = FileSystemInjector.get()
        .file(path.join(tempDirectory.path, 'shared_prefs.json'));
    await file.writeAsString(json.encode(map));
    return file;
  }

  Future<File> _getAttachmentContentsFile() async {
    return _getDirectoryStatsFile(await AttachmentUtils().getDirectory());
  }

  Future<File> _getReportContentFile() async {
    return _getDirectoryStatsFile(
        await StorageUtils().getDirectory(StorageSubdirectory.reports));
  }

  Future<File> _getDirectoryStatsFile(Directory directory) async {
    final tempDirectory = await getTemporaryDirectory();
    final file = FileSystemInjector.get()
        .file(path.join(tempDirectory.path, '${directory.basename}.txt'));
    var totalSize = 0;

    if (directory.existsSync()) {
      final files =
          directory.listSync(recursive: true, followLinks: false).map((file) {
        if (file is File) {
          final fileSize = file.lengthSync();
          totalSize += fileSize;
          return '$fileSize'
              ' ${dateFormat.format(file.lastModifiedSync())}'
              ' ${file.basename}';
        }
        return '';
      }).where((element) => element.isNotEmpty);
      await file.writeAsString(
        '${files.length} files: $totalSize bytes'
        '\n${files.join('\n')}',
      );
    }

    return file;
  }

  Future<bool> _upload(String path, String s3Key) async {
    try {
      await AmplifyStorageUtil().uploadFile(
        path: path,
        key: s3Key,
      );
      logger.i('bug-report-service Report uploaded to s3.');
      return true;
    } catch (e, stack) {
      unawaited(ErrorLogger().recordError(e, stack));
      return false;
    }
  }

  void _reportToCallback(BugReportServiceCallback? callback, int step) {
    if (callback == null) {
      return;
    }
    callback(step, _kTotalSteps);
  }

  void _logAnalytic(
    AnalyticsLogger analyticsLogger,
    String s3Key,
    String description,
  ) {
    analyticsLogger.logEvent(AnalyticsEvent.bugReport, props: {
      kAnalyticsPropBugReportS3Key: s3Key,
      kAnalyticsPropBugReportS3Url:
          'https://bitacora-attachments.s3.amazonaws.com/$s3Key',
      kAnalyticsPropBugReportDescription: description,
    });
  }

  Future<String> _getDbCreationInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final version = prefs.getString(SharedPreferencesKeys.dbCreateAppVersion);
    final dateMs = prefs.getInt(SharedPreferencesKeys.dbCreateDateTime);

    if (version == null) {
      return '[unknown]';
    }
    return '['
        'v$version '
        '${dateFormat.format(DateTime.fromMillisecondsSinceEpoch(dateMs!))}]';
  }
}
