import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/util/time_utils.dart';

class PersonnellogMinutes extends IntValueObject {
  const PersonnellogMinutes(super.value);

  @override
  bool isSameType(Object other) {
    return other is PersonnellogMinutes;
  }

  @override
  String get displayValue => getFormattedHours(value, suffix: '');

  String get inHours => getFormattedHours(value);
}
