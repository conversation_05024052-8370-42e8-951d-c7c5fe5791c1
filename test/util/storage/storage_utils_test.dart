import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:test/test.dart';

import '../../mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../../util/file_system/mocks.dart';
import '../mocks.dart';

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
    PathProviderPlatform.instance = MockPathProvider();
  });

  group('', () {
    test('getBaseDirectory', () async {
      final directory = await StorageUtils().getBaseDirectory();

      expect(directory.path, MockPathProvider.kStoragePath);
    });

    test('getStagingDirectory', () async {
      final fileSystem = await mockFileSystem();
      final key = ValueKey('${DateTime.now().microsecondsSinceEpoch}');

      final directory = await withInjected<FileSystem>(
          fileSystem, () => StorageUtils().getStagingDirectory(key));

      expect(
        directory.path,
        path.join(MockPathProvider.kStagingPath, 'temp_${key.value}'),
      );
    });

    test('maybeRenameUntilNew', () async {
      final fileSystem = await mockFileSystem();
      final directory = await fileSystem.directory('directory').create();
      final file = await fileSystem.file('file.jpg').create();

      final filename = await withInjected<FileSystem>(fileSystem,
          () => StorageUtils().maybeRenameUntilNew(directory.path, file.path));

      expect(filename, file.path);
    });

    test('renameUntilNew', () async {
      final fileSystem = await mockFileSystem();
      final clock = mockClock(DateTime(1996, 01, 29));
      final directory = await fileSystem.directory('directory').create();
      final file = fileSystem.file('file.jpg');

      final filename = await withInjected2<FileSystem, Clock>(
        fileSystem,
        clock,
        () => StorageUtils().renameUntilNew(directory.path, file.path),
      );

      final fileNameMillis =
          int.parse(path.basenameWithoutExtension(filename).split('_').last);
      expect(filename, isNot(file.path));
      expect(fileNameMillis, clock.now().millisecondsSinceEpoch);
    });

    test('File is In Staging', () async {
      final fileSystem = await mockFileSystem();
      final key = ValueKey('${DateTime.now().microsecondsSinceEpoch}');
      final directoryPath =
          path.join(MockPathProvider.kStagingPath, 'temp_${key.value}');
      await fileSystem.directory(directoryPath).create();
      final file =
          await fileSystem.file(path.join(directoryPath, 'task.xlsx')).create();

      withInjected<FileSystem>(
        fileSystem,
        () async =>
            expect(await StorageUtils().isInStaging(file.path, key), true),
      );
    });

    test('File is not in Staging', () async {
      final fileSystem = await mockFileSystem();
      final file = fileSystem.file('task.xlsx');
      final key = ValueKey('${DateTime.now().microsecondsSinceEpoch}');

      withInjected<FileSystem>(
          fileSystem,
          () async =>
              expect(await StorageUtils().isInStaging(file.path, key), false));
    });
  });
}
