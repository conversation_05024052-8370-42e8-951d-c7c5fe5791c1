import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class OutgoingMutationDbContract extends DbContract {
  static const String _ = 'om_';
  static const String _tableName = 'outgoingMutation';

  final String id = '${_}id';
  final String key = '${_}key';
  final String statusCode = '${_}statusCode';
  final String failedAttempts = '${_}failedAttempts';
  final String mutationType = '${_}mutationType';
  final String modelType = '${_}modelType';
  final String modelId = '${_}modelId';
  final String modelRemoteId = '${_}modelRemoteId';
  final String organizationId = '${_}organizationId';

  const OutgoingMutationDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersion;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $key INTEGER NOT NULL,
    $statusCode INTEGER,
    $failedAttempts INTEGER NOT NULL DEFAULT 0,
    $mutationType INTEGER NOT NULL,
    $modelType INTEGER NOT NULL,
    $modelId INTEGER,
    $modelRemoteId INTEGER,
    $organizationId INTEGER NOT NULL
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) {
    if (oldVersion < kDbVersionWithOutgoingMutationFailedAttempts) {
      return db.execute(
        'ALTER TABLE $tableName ADD $failedAttempts INTEGER NOT NULL DEFAULT 0',
      );
    }
  }
}
