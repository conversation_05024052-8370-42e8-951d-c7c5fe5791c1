const kMockChars =
    'AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz1234567890';

const kMockOrganizationNames = [
  'Eolus',
  'Personal',
  'Micro\$oft',
  'Gobierno',
  'Construcciones Hernán',
  'SpaceX',
];

const kMockProjectNames = [
  'Casa',
  'Taller',
  'Taquer<PERSON>',
  'Escuela',
  'Todos caminamos',
  'Deforestación',
  'Reforestación',
  'Rodando',
  'Huerto',
  'Construcción',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON>',
  '<PERSON>',
  'BitácoraIO',
  'tictic',
  'yosisalgo',
];

const kMockTagNames = [
  'Mu<PERSON>',
  'Vaceado',
  'Pi<PERSON>',
  'Tech<PERSON>',
  'Supervisión',
  'Transporte',
  'Estudio',
  'Reuni<PERSON>',
  'Entrenamient<PERSON>',
  '<PERSON>cci<PERSON><PERSON>',
  '<PERSON><PERSON><PERSON>',
  '<PERSON><PERSON> Activa',
  '<PERSON>',
  'Bug',
  '<PERSON><PERSON>ctor',
  '<PERSON><PERSON>',
];

const kMockFirstNames = [
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  '<PERSON>',
  'Christina',
  'Kyle',
  'Rachel',
  'Laura',
  'Lauren',
  'Amber',
  'Brittany',
  'Danielle',
  'Richard',
  'Kimberly',
  'Jeffrey',
  'Amy',
  'Crystal',
  'Michelle',
  'Tiffany',
  'Jeremy',
  'Benjamin',
  'Mark',
  'Emily',
  'Aaron',
  'Charles',
  'Rebecca',
  'Jacob',
  'Stephen',
  'Patrick',
  'Sean',
  'Erin',
  'Zachary',
  'Jamie',
  'Kelly',
  'Samantha',
  'Nathan',
  'Sara',
  'Dustin',
  'Paul',
  'Angela',
  'Tyler',
  'Scott',
  'Katherine',
  'Andrea',
  'Gregory',
  'Erica',
  'Mary',
  'Travis',
  'Lisa',
  'Kenneth',
  'Bryan',
  'Lindsey',
  'Kristen',
  'Jose',
  'Alexander',
  'Jesse',
  'Katie',
  'Lindsay',
  'Shannon',
  'Vanessa',
  'Courtney',
  'Christine',
  'Alicia',
  'Cody',
  'Allison',
  'Bradley',
  'Samuel',
];

const kMockSublocations = [
  'Terraza',
  'Sala',
  'Cocina',
  'Gimnasio',
  'Baño',
  'Banqueta',
  'Zona Norte',
  'Zona Sur',
  'Zona Poniente',
  'Zona Oriente',
  'Zona Oriente',
  'App móvil',
  'Backend',
  'Frontend',
  'Diseño',
  'Cuentas',
  'Junta',
];

const kMockEntryComments = [
  'Va quedando chido.',
  'No jala.',
  'Va a estar difícil.',
  'Va a estar bien fácil.',
  'La cagué con la cosa, sorry.',
  'No sé cómo le hice, pero así salió.',
  'Está medio roto.',
  'Se ve muy sucio.',
  'No le gustó al cliente.',
  'Me tardé un chingo.',
  'Cómo se ponían los attachments?',
  'Hasta allí llegué por hoy.',
  'Me sale más barato los martes.',
  'Me los llevé en la cajuela.',
  'No había nadie.',
  'Me descontaron \$400.',
  'Ya se me acabó el dinero.',
  'Manden más papas.',
  'Usé mi popó como fertilizante.',
  'Está bien caliente aquí.',
  'Yo creo que ya valió este pedo.',
];

const kMockWorklogTitles = [
  'Cortar el césped',
  'Lavar la ropa',
  'Hacer de comer',
  'Mesa de centro',
  'Escritorio',
  'Reparar frenos',
  'Ensamblar bicicleta',
  'Instalación de monturas',
  'Pedir gas',
  'Contratar maestro de 3o',
  'Cierre de mes',
  'Enviar calificaciones a padres de familia',
  'Preparar kermés',
  'Arreglar ventana rotas en 5A',
  'Barrera en la calle 23',
  'Restauración de área 103ZA',
  'Talar árboles',
  'Remover todos los arbustos',
  'Levantar la columna',
  'Hacer el pozo para la columna',
  'Echarle concreto al pozo',
  'Recibir la mezcla',
  'Conectar las tuberías hasta atrás',
  'Poner los pisos',
  'Pulida del piso',
  'La vista de registros',
  'Poblar la lista de la base de datos local',
  'Sincronizar registros',
  'Mostrar registros abiertos',
  'Editar registros de trabajo',
  'Editar registros de inventario',
  'Editar registros de personal',
  'Permitir cambiar de organización',
  'Mostrar el almacén de inventario',
  'Autentificación',
  'Tarea de migración',
  'Cierre del día',
  'Apertura del día',
  'Mantenimiento de horno',
  'Contratar aprendiz',
  'Branding',
  'Construir cohete',
  'Despegar',
  'Aterrizar',
  'Tomar muestras',
  'Plantar papas',
  'Plantar tomates',
  'Plantar cebollas',
  'Plantar frijol',
  'Plantar maíz',
];

const kMockProviders = [
  'Juan el de enfrente',
  'Talleres Efrén',
  'Soriana',
  'HEB',
  'Walmart',
  'Costco',
  'Ferretería Independencia',
  'CFE',
  'SEP',
  'Servicios de Jardinería SA de CV',
  'Home Depot',
  'Agrihermanos',
  'CEMEX',
  'NASA',
  'Google',
  'Apple',
  'Ternium',
];

const kMockInventoryItemNames = [
  'Escobas',
  'Frascos',
  'Cajas',
  'Cartones',
  'Bicicletas',
  'Llantas',
  'Ruedas',
  'Juegos de herramientas',
  'Trapos',
  'Hornos',
  'Parrillas',
  'Platos',
  'Tenedores',
  'Botes de salsa',
  'Bancos',
  'Balones de fútbol',
  'Balones de basquet',
  'Uniformes',
  'Ventanas',
  'Concreto',
  'Pintura amarilla',
  'Varas de fierro',
  'Tierra',
  'Picos',
  'Palas',
  'Guantes',
  'Gasolina',
  'Láminas',
  'Tractores',
  'Robots',
  'Mac Mini',
  'Monitores',
];

const kMockInventoryReasons = [
  'Se estaban acabando.',
  'Necesito más para el evento de mañana.',
  'El almacén estaba lleno.',
  'Me los pidió Juan.',
  'La fecha de expiración es la más cercana.',
  'Se cayó el techo.',
  'Se metieron a robar.',
];
