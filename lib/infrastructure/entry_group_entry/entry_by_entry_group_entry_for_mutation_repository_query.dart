import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';

class EntryByEntryGroupEntryForMutationRepositoryQuery
    extends RepositoryQuery<LocalId?> {
  final LocalId entryGroupEntryId;

  EntryByEntryGroupEntryForMutationRepositoryQuery(
      {required this.entryGroupEntryId});

  @override
  Future<LocalId?> run(RepositoryQueryContext context) async {
    final entryGroupEntry =
        await context.db.entryGroupEntry.find(context, entryGroupEntryId);

    return entryGroupEntry!.entry!.id!;
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) {
    return db.entryGroupEntry.fieldsBuilder
        .entry(db.entry.fieldsBuilder.build())
        .build();
  }
}
