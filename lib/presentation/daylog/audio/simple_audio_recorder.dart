import 'package:bitacora/presentation/daylog/audio/simple_audio_recorder_controller.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/util/time_utils.dart';
import 'package:flutter/material.dart';

class SimpleAudioRecorder extends StatefulWidget {
  final SimpleAudioRecorderController controller;
  final VoidCallback onCancel;
  final VoidCallback onSave;

  const SimpleAudioRecorder({
    super.key,
    required this.controller,
    required this.onCancel,
    required this.onSave,
  });

  @override
  State<SimpleAudioRecorder> createState() => _SimpleAudioRecorderState();
}

class _SimpleAudioRecorderState extends State<SimpleAudioRecorder>
    with SingleTickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    )..repeat(reverse: true);

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.5).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: widget.controller.isRecording,
      builder: (context, isRecording, _) {
        if (!isRecording) {
          return const SizedBox.shrink();
        }

        return Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10),
            border: Border.all(color: Colors.grey),
            color: Theme.of(context).colorScheme.surface,
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              AnimatedBuilder(
                animation: _pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _pulseAnimation.value,
                    child: Container(
                      width: 12,
                      height: 12,
                      decoration: const BoxDecoration(
                        color: bitacoraRed,
                        shape: BoxShape.circle,
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(width: 12),
              ValueListenableBuilder<Duration>(
                valueListenable: widget.controller.recordingDuration,
                builder: (context, duration, _) {
                  return Text(
                    getTimerTimestamp(duration),
                    style: Theme.of(context).textTheme.bodyMedium,
                  );
                },
              ),
              const SizedBox(width: 16),
              IconButton(
                iconSize: 20,
                icon: const Icon(Icons.close),
                visualDensity: VisualDensity.compact,
                onPressed: widget.onCancel,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                color: Theme.of(context).colorScheme.onSurface,
              ),
              const SizedBox(width: 16),
              IconButton(
                iconSize: 20,
                visualDensity: VisualDensity.compact,
                icon: const Icon(Icons.check),
                onPressed: widget.onSave,
                padding: EdgeInsets.zero,
                constraints: const BoxConstraints(),
                color: Theme.of(context).colorScheme.primary,
              ),
            ],
          ),
        );
      },
    );
  }
}
