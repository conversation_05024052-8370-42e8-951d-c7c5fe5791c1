import 'package:bitacora/domain/common/payment_status.dart';
import 'package:bitacora/domain/common/value_object/payment_status_value_object.dart';

class InventorylogPaymentStatus extends PaymentStatusValueObject {
  const InventorylogPaymentStatus(super.value);

  factory InventorylogPaymentStatus.fromDbValue(int? dbValue) {
    return InventorylogPaymentStatus(IndexedPaymentStatus.fromDbValue(dbValue));
  }

  @override
  bool isSameType(Object other) {
    return other is InventorylogPaymentStatus;
  }
}
