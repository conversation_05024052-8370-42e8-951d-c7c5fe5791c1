import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/report/report.dart';

class ReportRowRepositoryQuery extends RepositoryQuery<Report?> {
  final LocalId reportId;

  ReportRowRepositoryQuery({required this.reportId});

  @override
  Future<Report?> run(RepositoryQueryContext context) =>
      context.db.report.find(context, reportId);

  @override
  Fields fields(Repository db) => db.report.fieldsBuilder
      .isDownloaded()
      .path()
      .postParams()
      .getParams()
      .createdAt()
      .build();
}
