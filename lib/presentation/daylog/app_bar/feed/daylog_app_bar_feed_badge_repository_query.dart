import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';

class DaylogAppBarFeedBadgeRepositoryQuery extends RepositoryQuery<bool> {
  const DaylogAppBarFeedBadgeRepositoryQuery();

  @override
  Future<bool> run(RepositoryQueryContext context) =>
      context.db.feedPost.hasUnread(context);

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.feedPost.fieldsBuilder.build();
}
