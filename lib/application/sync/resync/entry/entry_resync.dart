import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/entry/entry_api_translator.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:path/path.dart' as path;

class EntryResyncInjector {
  factory EntryResyncInjector() => inject(() => const EntryResyncInjector._());

  const EntryResyncInjector._();

  EntryResync get(
          Repository db, ApiHelper apiHelper, EntryApiTranslator translator) =>
      inject(() => EntryResync._(db, apiHelper, translator));
}

class EntryResync {
  final Repository _db;
  final ApiHelper _apiHelper;
  final EntryApiTranslator _apiTranslator;

  EntryResync._(this._db, this._apiHelper, this._apiTranslator);

  Future<void> resyncEntry(Entry entry) async {
    logger.i('entry:resync old:${entry.syncVersion?.value} '
        'vs new:$kEntrySyncVersion');
    final endpoint = path.join(
      entry.extension!.extensionType.apiPostEndpoint,
      '${entry.remoteId!.apiValue}',
    );

    final entryData =
        (await _apiHelper.get<Map<String, dynamic>>(endpoint)).data;
    final updatedEntry = _apiTranslator.updateFromMap(entry, entryData!);
    await _db.entry.save(_db.context(), updatedEntry);
  }
}
