import 'dart:async';
import 'dart:isolate';
import 'dart:ui';

import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/future/future.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';

typedef _IsolateMessageResponse = void Function(Map<String, dynamic> response);

const Duration kHiPriLockDuration = Duration(seconds: 5);
const Duration kLoPriLockDuration = Duration(milliseconds: 1500);

const String _kMessageDictMessageKey = 'k';
const String _kMessageDictType = 't';
const String _kMessageDictWaitDuration = 'w';
const String _kMessageDictLockGranted = 'g';
const String _kMessageDictSender = 's';
const String _kMessageDictPriority = 'p';
const int _kMessageTypeRequest = 1;
const int _kMessageTypeResult = 2;

enum DbLockKey {
  foreground(1, 10, 'ui'),
  backgroundSync(2, 5, 'bg-sync'),
  fcm(3, 4, 'fcm'),
  periodicTask(4, 0, 'periodic');

  final int tag;
  final int priority;
  final String shortName;

  const DbLockKey(this.tag, this.priority, this.shortName);

  static DbLockKey fromTag(int tag) {
    for (final value in values) {
      if (value.tag == tag) {
        return value;
      }
    }
    throw 'db-lock Unsupported DbLockKey tag $tag';
  }

  @override
  String toString() {
    return 'db-lock-key[$tag]';
  }
}

class DbLockInjector {
  factory DbLockInjector() => inject(() => const DbLockInjector._());

  const DbLockInjector._();

  DbLock get(DbLockKey key, Future<void> Function() onRelease) =>
      inject(() => DbLock._(key, onRelease));
}

class DbLock {
  static String? _path;
  static File? _file;

  final DbLockKey key;
  final Future<void> Function() onRelease;
  final String portName = '${Clock().now().microsecondsSinceEpoch}';
  final Map<int, _IsolateMessageResponse> _portListeners = {};

  DateTime? _lockTime;
  Completer<void>? _verifyCompleter;

  DbLock._(this.key, this.onRelease) {
    _openIsolatePort();
  }

  void close() {
    _closeIsolatePort();
  }

  static Future<String> get path async => _path ??=
      join((await getApplicationSupportDirectory()).path, 'db-i.lock');

  static Future<File> get file async =>
      _file ??= FileSystemInjector.get().file(await path);

  void _openIsolatePort() {
    final receivePort = ReceivePort();
    IsolateNameServer.registerPortWithName(receivePort.sendPort, portName);
    receivePort.listen(
        (message) => _onIsolateMessage(message as Map<String, dynamic>));
  }

  void _closeIsolatePort() {
    IsolateNameServer.removePortNameMapping(portName);
  }

  /// Verify that we have db lock before proceeding with a db operation.
  /// Simultaneous calls to verify will await initial result if pending.
  Future<void> verify() async {
    logger.t('db-lock $key Verifying...');

    if (_verifyCompleter != null) {
      await _verifyCompleter!.future;
      return;
    }

    if (_lockTime != null) {
      _lockTime = Clock().now();
      return;
    }

    _verifyCompleter = Completer();
    try {
      await _maybeObtainLock();
      _verifyCompleter!.complete();
      _verifyCompleter = null;
    } catch (e, s) {
      _verifyCompleter!.completeError(e, s);
      _verifyCompleter = null;
      rethrow;
    }
  }

  Future<void> _maybeObtainLock() async {
    _log('Obtaining lock...');
    final f = await file;
    var tryCount = 0;

    while (true) {
      final currentLockInFile = await _getLockInFile(f);
      if ((currentLockInFile?.isEmpty ?? true) ||
          currentLockInFile == portName) {
        _log('No lock in file.');
        _lock(f);
        return;
      }

      await _maybeSnatchLock(f, currentLockInFile!);

      if (_lockTime != null) {
        return;
      }

      if (++tryCount == 5) {
        throw 'db-lock $key Failed to obtain lock after $tryCount tries.';
      }
    }
  }

  Future<void> _maybeSnatchLock(File file, String currentLock) async {
    final port = IsolateNameServer.lookupPortByName(currentLock);
    if (port == null) {
      _log('Port not found.');
      _lock(file);
      return;
    }

    final messageKey = Clock().now().microsecondsSinceEpoch;
    final message = {
      _kMessageDictMessageKey: messageKey,
      _kMessageDictSender: portName,
      _kMessageDictType: _kMessageTypeRequest,
      _kMessageDictPriority: key.priority,
    };
    final completer = Completer();
    Duration? pendingWaitDuration;

    _portListeners[messageKey] = (response) async {
      if (completer.isCompleted) {
        // FIXME Isolate took too long to respond, send signal to close db?
        return;
      }

      final waitDuration = response[_kMessageDictWaitDuration];
      pendingWaitDuration =
          waitDuration == null ? null : Duration(milliseconds: waitDuration!);

      if (_lockTime == null && (response[_kMessageDictLockGranted] == true)) {
        _log(
          'Lock granted.'
          '${waitDuration != null ? ' Waiting $pendingWaitDuration' : ''}',
        );
        _lock(file);
      }

      if (pendingWaitDuration == null) {
        completer.complete();
      }
    };
    port.send(message);

    Future.delayed(const Duration(milliseconds: 100), () {
      if (completer.isCompleted) {
        return;
      }

      if (pendingWaitDuration == null) {
        _log('No response from port.');
        completer.complete(null);
        _lock(file);
        return;
      }

      Future.delayed(pendingWaitDuration!, () {
        if (completer.isCompleted) {
          return;
        }

        completer.complete(null);
      });
    });

    await completer.future;
    _portListeners.remove(messageKey);
  }

  void _onIsolateMessage(Map<String, dynamic> message) {
    _log('Incoming message $message');

    if (message[_kMessageDictType] == _kMessageTypeRequest) {
      _onLockRequest(message);
      return;
    }

    _portListeners[message[_kMessageDictMessageKey]]!(message);
  }

  void _onLockRequest(Map<String, dynamic> message) async {
    final port =
        IsolateNameServer.lookupPortByName(message[_kMessageDictSender]);

    if (port == null) {
      _log('Port not found, ignoring request.');
      return;
    }

    final messageKey = message[_kMessageDictMessageKey];
    if (_lockTime == null) {
      _log('Lock not held.');
      _respondLockRequest(port, messageKey, false, const Duration(seconds: 1));
      return;
    }

    final requestPriority = message[_kMessageDictPriority] as int;
    final wait = _getWaitDuration();
    final shouldGrantLock =
        requestPriority >= key.priority || wait == Duration.zero;

    if (!shouldGrantLock) {
      _log('Lock not granted, priority is smaller and locked for $wait.');
      _respondLockRequest(port, messageKey, false, wait);
      return;
    }


    _log('Granting lock [pending] ($wait + 3s)');
    _respondLockRequest(
      port,
      messageKey,
      true,
      wait + const Duration(seconds: 3),
    );

    await Future.delayed(wait);
    await onRelease();

    _log('Granting lock [confirmed]');
    _respondLockRequest(port, messageKey, true);
  }

  void _respondLockRequest(
    SendPort port,
    int messageKey,
    bool lockGranted, [
    Duration? waitDuration,
  ]) {
    final message = {
      _kMessageDictMessageKey: messageKey,
      _kMessageDictSender: portName,
      _kMessageDictType: _kMessageTypeResult,
      _kMessageDictLockGranted: lockGranted,
      if (waitDuration != null)
        _kMessageDictWaitDuration: waitDuration.inMilliseconds,
    };
    port.send(message);
  }

  void _lock(File file) {
    _lockTime = Clock().now();
    _log('Locking $_lockTime.');
    file.writeAsString(portName);
  }

  Duration _getWaitDuration() {
    final timeElapsedSinceLock = Clock().now().difference(_lockTime!);
    final lockDuration = _getLockDuration();
    if (timeElapsedSinceLock >= lockDuration) {
      return Duration.zero;
    }

    return lockDuration - timeElapsedSinceLock;
  }

  static Future<String?> _getLockInFile(File file) async {
    if (!(await file.exists())) {
      return null;
    }

    const numRetries = 5;
    for (var i = 0; i < numRetries; i++) {
      try {
        if (i > 0) {
          logger.f(
            'db-lock Failed to read lock'
            '${i == numRetries - 1 ? '.' : ', retrying...'}',
          );
          await FutureInjector().delayed(const Duration(milliseconds: 250));
        }
        final bytes = await file.readAsString();
        return bytes;
      } catch (_) {}
    }

    return null;
  }

  Duration _getLockDuration() => key.priority >= DbLockKey.foreground.priority
      ? kHiPriLockDuration
      : kLoPriLockDuration;

  void _log(String s) {
    logger.i('$key [$portName] -> $s');
  }
}
