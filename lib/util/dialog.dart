import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';

void showDestructiveDialog({
  required BuildContext context,
  required String title,
  required String message,
  required String destroyText,
  required VoidCallback destroyer,
  String? cancelText,
  VoidCallback? onCancel,
}) {
  showPlatformDialog(
    context: context,
    builder: (modalContext) => PlatformAlertDialog(
      title: Text(title),
      content: Text(message),
      actions: [
        PlatformDialogAction(
          onPressed: () {
            Navigator.of(modalContext).pop();
            if (onCancel != null) {
              onCancel();
            }
          },
          child:
              PlatformText(cancelText ?? AppLocalizationsResolver.get().cancel),
        ),
        PlatformDialogAction(
          cupertino: (_, __) =>
              CupertinoDialogActionData(isDestructiveAction: true),
          onPressed: () {
            Navigator.of(modalContext).pop();
            destroyer();
          },
          child: PlatformText(destroyText),
        ),
      ],
    ),
  );
}
