import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_db_contract.dart';

class OutgoingMutationDbTranslator implements DbTranslator<OutgoingMutation> {
  const OutgoingMutationDbTranslator();

  @override
  Set<Field> get nestedModelFields => outgoingMutationNestedModelFields;

  @override
  Future<OutgoingMutation> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return OutgoingMutation(
      id: fields[OutgoingMutationField.id]?.value(map),
      key: fields[OutgoingMutationField.key]?.value(map),
      statusCode: fields[OutgoingMutationField.statusCode]?.value(map),
      failedAttempts: fields[OutgoingMutationField.failedAttempts]?.value(map),
      mutationType: fields[OutgoingMutationField.mutationType]?.value(map),
      model: await fields[OutgoingMutationField.model]?.nested(context, map),
      organization: await fields[OutgoingMutationField.organization]
          ?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, OutgoingMutation outgoingMutation) async {
    final map = <String, dynamic>{};
    const contract = OutgoingMutationDbContract();
    addField(map, contract.id, outgoingMutation.id);
    addField(map, contract.key, outgoingMutation.key);
    addField(map, contract.statusCode, outgoingMutation.statusCode);
    addField(map, contract.failedAttempts, outgoingMutation.failedAttempts);
    addField(map, contract.mutationType, outgoingMutation.mutationType);

    final model = outgoingMutation.model;
    if (model != null) {
      map[contract.modelType] = model.type.dbValue;
      if (outgoingMutation.mutationType == MutationType.delete) {
        map[contract.modelRemoteId] = model.value.remoteId!.dbValue;
      } else {
        map[contract.modelId] = model.value.id!.dbValue;
      }
    }

    await saveNestedModel<Organization>(context, map, contract.organizationId,
        context.db.organization, outgoingMutation.organization);

    return map;
  }
}
