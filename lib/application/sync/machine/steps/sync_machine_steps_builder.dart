import 'package:bitacora/application/sync/machine/steps/access/sync_machine_step_access.dart';
import 'package:bitacora/application/sync/machine/steps/cleanup/sync_machine_step_cleanup.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/custom_field/sync_collection_custom_field_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/entry_groups/sync_collection_entry_group_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/feed_post/sync_collection_feed_post_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/person_details/sync_collection_person_detail_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/qr_code/sync_collection_qr_code_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/resource/sync_collection_resource_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/resource_aggregation/sync_collection_resource_aggregation_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/resource_category/sync_collection_resource_category_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/signature/sync_collection_signature_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_machine_step_collection_download.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/template/sync_collection_template_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/template_condition/sync_collection_template_condition_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/user/sync_collection_user_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/download/entry/sync_machine_step_entry_download.dart';
import 'package:bitacora/application/sync/machine/steps/flags/sync_machine_step_flags_downloader.dart';
import 'package:bitacora/application/sync/machine/steps/head/sync_machine_step_head.dart';
import 'package:bitacora/application/sync/machine/steps/upload/sync_machine_step_upload.dart';
import 'package:bitacora/application/sync/machine/sync_machine_multi_step.dart';
import 'package:bitacora/application/sync/machine/sync_machine_params.dart';
import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/application/sync/sync_utils.dart';
import 'package:bitacora/util/inject/inject.dart';

class SyncMachineStepsBuilder {
  factory SyncMachineStepsBuilder() =>
      inject(() => const SyncMachineStepsBuilder._());

  const SyncMachineStepsBuilder._();

  List<SyncMachineStep> buildForForegroundSync(SyncMachineParams params) {
    return _buildForFullSync(params);
  }

  Future<List<SyncMachineStep>> buildForBackgroundSync(
      SyncMachineParams params) async {
    final mode = await SyncUtils().getPendingSyncTriggerMode();

    switch (mode) {
      case SyncTriggerMode.normal:
        return <SyncMachineStep>[SyncMachineStepUpload(params)];
      case SyncTriggerMode.fullSync:
      case SyncTriggerMode.superSync:
        return _buildForFullSync(params);
    }
  }

  List<SyncMachineStep> _buildForFullSync(SyncMachineParams params) {
    return <SyncMachineStep>[
      SyncMachineMultiStep(params, 'multi', [
        SyncMachineMultiStep(params, 'multi-head-flags', [
          SyncMachineStepHead(params),
          SyncMachineStepFlagsDownloader(params),
        ]),
        SyncMachineStepAccess(params),
        SyncMachineMultiStep(
          params,
          'multi-collection-download',
          [
            SyncMachineStepCollectionDownload(params, 'collection-download-1', [
              SyncCollectionUserDownloader(params),
              SyncCollectionCustomFieldDownloader(params),
              SyncCollectionQrCodeDownloader(params),
              SyncCollectionResourceDownloader(params),
              SyncCollectionFeedPostDownloader(params),
            ]),
            SyncMachineStepCollectionDownload(params, 'collection-download-2', [
              SyncCollectionTemplateDownloader(params),
              SyncCollectionPersonDetailDownloader(params),
              SyncCollectionResourceCategoryDownloader(params),
              SyncCollectionResourceAggregationDownloader(params),
            ]),
          ],
          isParallel: false,
          isCritical: false,
        )
      ]),
      SyncMachineStepUpload(params),
      SyncMachineStepEntryDownload(params),
      SyncMachineStepCollectionDownload(params, 'collection-download-3', [
        SyncCollectionSignatureDownloader(params),
        SyncCollectionEntryGroupDownloader(params),
        SyncCollectionTemplateConditionDownloader(params),
      ]),
      SyncMachineStepCleanup(params),
    ];
  }
}
