import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/report_template/report_template.dart';

class ReportFormTemplatesRepositoryQuery
    extends RepositoryQuery<List<ReportTemplate>> {
  const ReportFormTemplatesRepositoryQuery();

  @override
  Future<List<ReportTemplate>> run(RepositoryQueryContext context) =>
      context.db.reportTemplate.findAll(context);

  @override
  Fields fields(Repository db) =>
      db.reportTemplate.fieldsBuilder.active().description().option().build();
}
