import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_label.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_multi_value_form_controller.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../domain/custom_field/mocks.dart';
import '../../../../../mocktail_fallback_values.dart';
import '_robots/template_block_multi_value_form_controller_test_robot.dart';

void main() {
  group('$TemplateBlockMultiValueFormController tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Create controller', () {
      final robot = TemplateBlockMultiValueFormControllerTestRobot();

      robot.build<String>();

      robot.verifyInstance<String>();
      robot.dispose();
    });

    test('Read with null metadata', () {
      final robot = TemplateBlockMultiValueFormControllerTestRobot();

      robot.build<String>();
      robot.read<String>(null, null, '');

      robot.verifyValue<String>([]);
      robot.dispose();
    });

    test('Read String values', () {
      final robot = TemplateBlockMultiValueFormControllerTestRobot();
      final customField = mockCustomField();

      final metadata1 = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue('value1'),
      );

      final metadata2 = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue('value2'),
      );

      robot.setFieldsMetadata([metadata1, metadata2]);
      robot.build<String>();
      robot.read<String>(metadata1, null, '');

      robot.verifyValue<String>(['value1', 'value2']);
      robot.dispose();
    });

    test('Read CustomFieldAllowedValue values', () {
      final robot = TemplateBlockMultiValueFormControllerTestRobot();
      final customField = mockCustomField();

      final allowedValues = [
        CustomFieldAllowedValue(
          id: LocalId(1),
          value: CustomFieldAllowedValueValue('value1'),
          label: CustomFieldAllowedValueLabel('Label 1'),
        ),
        CustomFieldAllowedValue(
          id: LocalId(2),
          value: CustomFieldAllowedValueValue('value2'),
          label: CustomFieldAllowedValueLabel('Label 2'),
        ),
      ];

      final metadata1 = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue('value1'),
      );

      final metadata2 = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue('value2'),
      );

      robot.setFieldsMetadata([metadata1, metadata2]);
      robot.build<CustomFieldAllowedValue>(allowedValues: allowedValues);
      robot.read<CustomFieldAllowedValue>(metadata1, null, '');

      robot.verifyValue<CustomFieldAllowedValue>(allowedValues);
      robot.dispose();
    });

    test('Read with last metadata', () {
      final robot = TemplateBlockMultiValueFormControllerTestRobot();
      final customField = mockCustomField();
      final lastCustomField = mockCustomField();

      final metadata = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue('value1'),
      );

      final lastMetadata = CustomFieldMetadata(
        customField: lastCustomField,
        value: CustomFieldMetadataValue('lastValue'),
      );

      robot.setFieldsMetadata([
        metadata,
      ]);

      robot.build<String>();
      robot.read<String>(metadata, lastMetadata, '');

      robot.verifyValue<String>(['value1']);
      robot.dispose();
    });

    test('Process generic type values', () {
      final robot = TemplateBlockMultiValueFormControllerTestRobot();
      final customField = mockCustomField();

      final metadata1 = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue(123),
      );

      final metadata2 = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue(456),
      );

      robot.setFieldsMetadata([metadata1, metadata2]);
      robot.build<int>();
      robot.read<int>(metadata1, null, '');

      robot.verifyValue<int>([123, 456]);
      robot.dispose();
    });

    test('Handle invalid type conversion', () {
      final robot = TemplateBlockMultiValueFormControllerTestRobot();
      final customField = mockCustomField();

      final metadata1 = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue('not an int'),
      );

      final metadata2 = CustomFieldMetadata(
        customField: customField,
        value: CustomFieldMetadataValue(456),
      );

      robot.setFieldsMetadata([metadata1, metadata2]);
      robot.build<int>();
      robot.read<int>(metadata1, null, '');

      robot.verifyValue<int>([456]);
      robot.dispose();
    });
  });
}
