import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/signature/signature.dart';

class SignatureForOutgoingMutationRepositoryQuery
    extends RepositoryQuery<Signature?> {
  final LocalId signatureId;

  SignatureForOutgoingMutationRepositoryQuery({required this.signatureId});

  @override
  Future<Signature?> run(RepositoryQueryContext context) {
    return context.db.signature.find(context, signatureId);
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) {
    final project = db.project.fieldsBuilder
        .organization(db.organization.fieldsBuilder.remoteId().build())
        .build();

    return db.signature.fieldsBuilder
        .remoteId()
        .entry(db.entry.fieldsBuilder
            .remoteId()
            .worklog(db.worklog.fieldsBuilder.project(project).build())
            .inventorylog(db.inventorylog.fieldsBuilder
                .destProject(project)
                .sourceProject(project)
                .build())
            .personnellog(
                db.personnellog.fieldsBuilder.project(project).build())
            .build())
        .build();
  }
}
