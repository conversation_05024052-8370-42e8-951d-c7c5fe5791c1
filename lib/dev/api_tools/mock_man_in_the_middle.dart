import 'package:bitacora/dev/api_tools/mock_responses/access_responses.dart';
import 'package:bitacora/dev/api_tools/mock_responses/login_response.dart';
import 'package:bitacora/dev/api_tools/mock_responses/sync_collection_responses.dart';
import 'package:bitacora/dev/api_tools/mock_responses/sync_responses.dart';
import 'package:collection/collection.dart';
import 'package:dio/dio.dart';

class MockManInTheMiddle extends InterceptorsWrapper {
  MockManInTheMiddle()
      : super(
          onRequest: (
            RequestOptions options,
            RequestInterceptorHandler handler,
          ) async {
            final response = mockResponse(options);

            if (response == null) {
              return handler.reject(DioException(
                requestOptions: RequestOptions(path: options.path),
                error: _MockRequestError(options),
              ));
            }

            handler.resolve(response);
          },
        );

  static Response? mockResponse(RequestOptions options) {
    for (var response in _kMockResponseCandidates) {
      if (response.requestOptions.path == options.path &&
          response.requestOptions.method == options.method &&
          _isSameQueryParameters(options, response) &&
          _isSameData(options, response)) {
        return response;
      }
    }
    return null;
  }

  static bool _isSameQueryParameters(
      RequestOptions options, Response response) {
    if (options.queryParameters.length !=
        response.requestOptions.queryParameters.length) {
      return false;
    }
    for (var key in options.queryParameters.keys) {
      if (response.requestOptions.queryParameters[key] !=
          options.queryParameters[key]) {
        return false;
      }
    }
    return true;
  }

  static bool _isSameData(RequestOptions options, Response response) {
    if (response.requestOptions.data == null) {
      return true;
    }
    return const DeepCollectionEquality()
        .equals(response.requestOptions.data, options.data);
  }
}

final _kMockResponseCandidates = [
  kMockResponseLogin,
  kMockResponseSyncHead,
  kMockResponseSyncAccess,
  kMockResponseSyncProject221821_1,
  kMockResponseSyncProject221821_2,
  kMockResponseSyncProject216878,
  kMockResponseSyncCollectionDownload1_1,
  kMockResponseSyncCollectionDownload1_2,
  kMockResponseSyncCollectionDownload2_1,
  kMockResponseSyncCollectionDownload2_2,
  kMockResponseSyncCollectionDownload2_3,
  kMockResponseSyncCollectionDownload3_1,
];

class _MockRequestError extends Error {
  final RequestOptions options;

  _MockRequestError(this.options);

  @override
  String toString() {
    return 'Mock ${options.method} ${options.path} ${options.queryParameters}'
        ' is not setup';
  }
}
