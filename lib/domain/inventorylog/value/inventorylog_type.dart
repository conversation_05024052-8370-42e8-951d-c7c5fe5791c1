import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:bitacora/l10n/app_localizations.dart';

enum InventorylogTypeValue {
  incoming,
  movement,
  outgoing,
}

class InventorylogType extends ValueObject<InventorylogTypeValue> {
  static const InventorylogType incoming =
      InventorylogType(InventorylogTypeValue.incoming);
  static const InventorylogType movement =
      InventorylogType(InventorylogTypeValue.movement);
  static const InventorylogType outgoing =
      InventorylogType(InventorylogTypeValue.outgoing);
  static const List<InventorylogType> values = [incoming, movement, outgoing];

  const InventorylogType(super.value);

  factory InventorylogType.fromDbValue(int dbValue) => values[dbValue - 1];

  @override
  int get dbValue {
    switch (value) {
      case InventorylogTypeValue.incoming:
        return 1;
      case InventorylogTypeValue.movement:
        return 2;
      case InventorylogTypeValue.outgoing:
        return 3;
      }
  }

  Color? get color {
    switch (value) {
      case InventorylogTypeValue.incoming:
        return bitacoraGreen[500]!;
      case InventorylogTypeValue.movement:
        return null;
      case InventorylogTypeValue.outgoing:
        return bitacoraRed[500]!;
    }
  }

  @override
  int get apiValue => dbValue;

  String localizedDisplayValue(BuildContext context) {
    switch (value) {
      case InventorylogTypeValue.incoming:
        return AppLocalizations.of(context)!.incoming;
      case InventorylogTypeValue.movement:
        return AppLocalizations.of(context)!.movement;
      case InventorylogTypeValue.outgoing:
        return AppLocalizations.of(context)!.outgoing;
      }
  }
}
