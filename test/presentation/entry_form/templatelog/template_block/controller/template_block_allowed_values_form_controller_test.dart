import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_allowed_values_form_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:injectzone/injectzone.dart';

import '../../../../../mocktail_fallback_values.dart';
import '../../../../../util/focusNode/mocks.dart';
import '_robots/template_block_allowed_values_form_controller_test_robot.dart';

void main() {
  group('$TemplateBlockAllowedValuesFormController tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Create controller without parent', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();

      robot.build();

      robot.verifyInstance();
      robot.dispose();
    });

    test('Create controller with parent', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();

      robot.buildParent();
      robot.build();

      robot.verifyInstance();
      robot.dispose();
    });

    test('Filter by parent', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();
      robot.buildParent(allowedValues: [values[0], values[1]]);
      robot.build(allowedValues: [values[2], values[3], values[4]]);

      robot.setParentValue(values[1]);

      robot.verifySuggestedValue(allowedValues: [values[4]]);
      robot.dispose();
    });

    test('Filter by text', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();
      final hasFocus = ValueNotifier(true);
      final mockedFocusNode = MockedFocusNode(hasFocus: hasFocus);

      Injectzone().withInjected(
          [ValueInjector.inject<FocusNode>(mockedFocusNode.mock)], () {
        robot.buildParent(allowedValues: [values[0], values[1]]);
        robot.build(allowedValues: [values[2], values[3], values[4]]);

        robot.setParentValue(values[0]);
        robot.setText('b');

        robot.verifySuggestedValue(allowedValues: [values[3]]);
        robot.dispose();
      });
    });

    test('Set value null when unfocus', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();
      final hasFocus = ValueNotifier(true);
      final mockedFocusNode = MockedFocusNode(hasFocus: hasFocus);

      Injectzone().withInjected(
        [ValueInjector.inject<FocusNode>(mockedFocusNode.mock)],
        () {
          robot.build(allowedValues: [values[0], values[1]]);
          robot.setValue(values[0]);
          robot.setText('C');
          hasFocus.value = false;

          robot.verifyValue(null);
          robot.dispose();
        },
      );
    });

    test('Update text when value not mutated and has focus', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();
      final hasFocus = ValueNotifier(true);
      final mockedFocusNode = MockedFocusNode(hasFocus: hasFocus);

      Injectzone().withInjected(
        [ValueInjector.inject<FocusNode>(mockedFocusNode.mock)],
        () {
          robot.build(allowedValues: [values[0], values[1]]);
          robot.setValue(values[0]);
          robot.setText('Z');
          robot.setValue(values[0]);
          robot.verifyText('A');
          robot.dispose();
        },
      );
    });

    test('Update text when value not mutated and not has focus', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();
      final hasFocus = ValueNotifier(true);
      final mockedFocusNode = MockedFocusNode(hasFocus: hasFocus);

      Injectzone().withInjected(
        [ValueInjector.inject<FocusNode>(mockedFocusNode.mock)],
        () {
          robot.build(allowedValues: [values[0], values[1]]);
          robot.setValue(values[0]);
          robot.setText('Z');
          hasFocus.value = false;
          robot.verifyText('');
          robot.dispose();
        },
      );
    });

    test('Read with allowedValue ', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();

      robot.build(allowedValues: [values[0], values[1]]);
      robot.read(CustomFieldMetadata(allowedValue: values[0]), null, '');

      robot.verifyText('A');
      robot.dispose();
    });

    test('Read allowedValue null', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();

      robot.build(allowedValues: [values[0], values[1]]);
      robot.read(null, null, '');

      robot.verifyText('');
      robot.dispose();
    });

    test('Read from allowed string', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();

      robot.build(allowedValues: [values[0], values[1]]);
      robot.read(
          CustomFieldMetadata(value: CustomFieldMetadataValue('A')), null, '');

      robot.verifyText('A');
      robot.dispose();
    });

    test('Read from not allowed string', () {
      final robot = TemplateBlockAllowedValuesFormControllerTestRobot();

      robot.build(allowedValues: [values[0], values[1]]);
      robot.read(
          CustomFieldMetadata(value: CustomFieldMetadataValue('Z')), null, '');

      robot.verifyText('');
      robot.dispose();
    });
  });
}

final values = [
  CustomFieldAllowedValue(
    id: LocalId(1),
    value: CustomFieldAllowedValueValue('A'),
  ),
  CustomFieldAllowedValue(
    id: LocalId(2),
    value: CustomFieldAllowedValueValue('B'),
  ),
  CustomFieldAllowedValue(
      id: LocalId(3),
      value: CustomFieldAllowedValueValue('A.a'),
      parent: CustomFieldAllowedValue(id: LocalId(1))),
  CustomFieldAllowedValue(
      id: LocalId(4),
      value: CustomFieldAllowedValueValue('A.b'),
      parent: CustomFieldAllowedValue(id: LocalId(1))),
  CustomFieldAllowedValue(
      id: LocalId(5),
      value: CustomFieldAllowedValueValue('B.a'),
      parent: CustomFieldAllowedValue(id: LocalId(2)))
];
