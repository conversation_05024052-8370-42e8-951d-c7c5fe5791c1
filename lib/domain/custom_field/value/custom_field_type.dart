import 'package:bitacora/domain/common/value_object/value_object.dart';

enum CustomFieldTypeValue {
  text,
  textArea,
  intNumber,
  floatNumber,
  dateTime,
  date,
  time,
  radio,
  select,
  checkbox,
  project,
  user,
  checkboxGroup;

  static CustomFieldTypeValue fromApiValue(String value) {
    switch (value) {
      case 'TEXT':
        return CustomFieldTypeValue.text;
      case 'TEXT_AREA':
        return CustomFieldTypeValue.textArea;
      case 'INTEGER':
        return CustomFieldTypeValue.intNumber;
      case 'FLOAT':
        return CustomFieldTypeValue.floatNumber;
      case 'DATE_TIME':
        return CustomFieldTypeValue.dateTime;
      case 'DATE':
        return CustomFieldTypeValue.date;
      case 'TIME':
        return CustomFieldTypeValue.time;
      case 'RADIO':
        return CustomFieldTypeValue.radio;
      case 'SELECT':
        return CustomFieldTypeValue.select;
      case 'CHECKBOX':
        return CustomFieldTypeValue.checkbox;
      case 'PROJECT':
        return CustomFieldTypeValue.project;
      case 'USER':
        return CustomFieldTypeValue.user;
      case 'CHECKBOX_GROUP':
        return CustomFieldTypeValue.checkboxGroup;
      default:
        throw 'CustomFieldTypeValue: value[$value] not supported';
    }
  }
}

class CustomFieldType extends ValueObject<CustomFieldTypeValue> {
  CustomFieldType(super.value);

  static CustomFieldType get text => CustomFieldType(CustomFieldTypeValue.text);

  static CustomFieldType get textArea =>
      CustomFieldType(CustomFieldTypeValue.textArea);

  static CustomFieldType get intNumber =>
      CustomFieldType(CustomFieldTypeValue.intNumber);

  static CustomFieldType get floatNumber =>
      CustomFieldType(CustomFieldTypeValue.floatNumber);

  static CustomFieldType get dateTime =>
      CustomFieldType(CustomFieldTypeValue.dateTime);

  static CustomFieldType get date => CustomFieldType(CustomFieldTypeValue.date);

  static CustomFieldType get time => CustomFieldType(CustomFieldTypeValue.time);

  static CustomFieldType get checkBox =>
      CustomFieldType(CustomFieldTypeValue.checkbox);

  static CustomFieldType get radio =>
      CustomFieldType(CustomFieldTypeValue.radio);

  static CustomFieldType get select =>
      CustomFieldType(CustomFieldTypeValue.select);

  static CustomFieldType get project =>
      CustomFieldType(CustomFieldTypeValue.project);

  static CustomFieldType get checkboxGroup =>
      CustomFieldType(CustomFieldTypeValue.checkboxGroup);

  static CustomFieldType get user => CustomFieldType(CustomFieldTypeValue.user);

  @override
  int get dbValue => value.index;

  static CustomFieldType fromDbValue(int dbValue) {
    return CustomFieldType(CustomFieldTypeValue.values[dbValue]);
  }

  static CustomFieldType fromApiValue(String apiValue) {
    return CustomFieldType(CustomFieldTypeValue.fromApiValue(apiValue));
  }
}
