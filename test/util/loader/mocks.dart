import 'package:bitacora/util/loader/loader.dart';
import 'package:mocktail/mocktail.dart';

class MockLoader<T> extends Mock implements Loader<T> {}

Loader<T> mockLoader<T>({
  T? value,
  bool hasLoaded = true,
  bool isLoading = false,
}) {
  final mock = MockLoader<T>();
  when(() => mock.value).thenAnswer((_) => value);
  when(() => mock.hasLoaded).thenReturn(hasLoaded);
  when(() => mock.isLoading).thenReturn(isLoading);
  return mock;
}
