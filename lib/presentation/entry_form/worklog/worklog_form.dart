import 'package:bitacora/presentation/entry_form/util/centesimal_text_input_formatter.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:bitacora/presentation/entry_form/worklog/worklog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/worklog/worklog_title_suggestion_repository_query.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class WorklogForm extends StatefulWidget {
  final WorklogFormController controller;

  const WorklogForm({super.key, required this.controller});

  @override
  State<WorklogForm> createState() => _WorklogFormState();
}

class _WorklogFormState extends State<WorklogForm> {
  final Key _titleKey = GlobalKey();

  final FocusNode _quantityFocus = FocusNode();
  final FocusNode _titleFocus = FocusNode();
  final FocusNode _projectNameFocus = FocusNode();
  final FocusNode _sublocationFocus = FocusNode();
  final FocusNode _commentsFocus = FocusNode();

  @override
  void dispose() {
    _quantityFocus.dispose();
    _titleFocus.dispose();
    _projectNameFocus.dispose();
    _sublocationFocus.dispose();
    _commentsFocus.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _WorklogFormSkeleton(
      quantity: wrapWidgetForMeasure(TextFormField(
        controller: widget.controller.quantity,
        focusNode: _quantityFocus,
        textInputAction: TextInputAction.next,
        // Text capitalization set here due to iOS bug where the
        // capitalization prop is cached from previous textfields.
        textCapitalization: TextCapitalization.sentences,
        keyboardType:
            const TextInputType.numberWithOptions(decimal: true, signed: true),
        inputFormatters: [CentesimalTextInputFormatter()],
        decoration: InputDecoration(
            labelText: AppLocalizations.of(context)!.formQuantity),
        onFieldSubmitted: (value) =>
            maybeRequestFocus(_titleFocus, widget.controller.title.text, true),
      )),
      title: NewableFieldSuggestionTypeahead(
        key: _titleKey,
        suggestionsQuery: const WorklogTitleSuggestionRepositoryQuery(),
        queryScope: getOrgScope(context),
        onSelect: (_) => maybeRequestFocus(
            _projectNameFocus, widget.controller.projectName.text, false),
        textFieldConfiguration: TypeaheadTextFieldConfiguration(
          focusNode: _titleFocus,
          controller: widget.controller.title,
          textInputAction: TextInputAction.next,
          textCapitalization: TextCapitalization.sentences,
          decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.formTitle),
          onSubmitted: (value) => maybeRequestFocus(
              _projectNameFocus, widget.controller.projectName.text, true),
          validator: getRequiredFieldValidator(),
        ),
      ),
      projectSublocation: getProjectSublocationRow(
        context: context,
        projectFocusNode: _projectNameFocus,
        projectController: widget.controller.projectName,
        sublocationFocusNode: _sublocationFocus,
        sublocationController: widget.controller.sublocation,
      ),
      comments: getCommentsRow(
        context: context,
        focusNode: _commentsFocus,
        controller: widget.controller,
      ),
    );
  }
}

class NonEditableWorklogForm extends StatelessWidget {
  final WorklogFormController controller;

  const NonEditableWorklogForm({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      child: Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: const InputDecorationTheme(
            contentPadding: EdgeInsets.only(),
            border: InputBorder.none,
            isDense: true,
          ),
        ),
        child: _WorklogFormSkeleton(
          isEditable: false,
          quantity: TextFormField(
            controller: controller.quantity,
            decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.formQuantity),
          ),
          title: TextFormField(
            controller: controller.title,
            decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.formTitle),
          ),
          projectSublocation: getNonEditableProjectSublocationRow(
            context: context,
            projectController: controller.projectName,
            sublocationController: controller.sublocation,
          ),
          comments: controller.comments.text.isEmpty
              ? null
              : TextFormField(
                  controller: controller.comments,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.formComments),
                ),
        ),
      ),
    );
  }
}

class _WorklogFormSkeleton extends StatelessWidget {
  final Widget quantity;
  final Widget title;
  final Widget projectSublocation;
  final Widget? comments;
  final bool isEditable;

  const _WorklogFormSkeleton({
    required this.quantity,
    required this.title,
    required this.projectSublocation,
    required this.comments,
    this.isEditable = true,
  });

  @override
  Widget build(BuildContext context) {
    final children = [
      Row(
        children: [
          Expanded(flex: 3, child: quantity),
          const SizedBox(width: kFormHorizontalSpacing),
          Expanded(flex: 7, child: title),
        ],
      ),
      const SizedBox(height: kFormVerticalSpacing),
      projectSublocation,
      if (comments != null) ...[
        const SizedBox(height: kFormVerticalSpacing),
        comments!,
      ]
    ];

    return isEditable
        ? SliverList(delegate: SliverChildListDelegate.fixed(children))
        : Column(children: children);
  }
}
