import 'dart:async';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/ascii/header_printer.dart';
import 'package:bit/bit_build/bit_build.dart';
import 'package:bit/bit_coverage/bit_coverage.dart';
import 'package:bit/bit_db_speed_test/bit_db_speed_test.dart';
import 'package:bit/bit_debug/bit_debug.dart';
import 'package:bit/bit_dump/bit_dump.dart';
import 'package:bit/bit_emulator/bit_emulator.dart';
import 'package:bit/bit_git/bit_git.dart';
import 'package:bit/bit_git/bit_git_branch.dart';
import 'package:bit/bit_integration_test/bit_integration_test.dart';
import 'package:bit/bit_perf_test/bit_perf_test.dart';
import 'package:bit/bit_reload_console/bit_reload_console.dart';
import 'package:bit/bit_test/bit_test.dart';
import 'package:bit/bit_version_bump/bit_version_bump.dart';
import 'package:bit/command_line.dart';

Future<int> bitConsole([List<String> args = const <String>[]]) async {
  await printHeader(animate: true);

  await CommandLine(
    {
      'test': bitTest,
      'integration-test': bitIntegrationTest,
      'perf-test': bitPerfTest,
      'branch': bitGitBranch,
      'debug': bitDebug,
      'build': bitBuild,
      'coverage': bitCoverage,
      'db-speed-test': bitDbSpeedTest,
      'dump': bitDump,
      'git': bitGit,
      'emulator': bitEmulator,
      'install': bitInstall,
      'profile': bitProfile,
      'reload-console': bitReloadConsole,
      'version-bump': bitVersionBump,
      'version-list': () => bitVersionBump(['--list']),
    },
    isSticky: true,
  ).present();
  writeln('Bye-bye.', TextStyle.error);
  return 0;
}
