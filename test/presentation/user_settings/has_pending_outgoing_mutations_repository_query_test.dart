import 'package:bitacora/presentation/user_settings/has_pending_outgoing_mutations_repository_query.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../domain/common/mocks.dart';
import '../../domain/outgoing_mutation/mocks.dart';
import '../../mocktail_fallback_values.dart';

void main() {
  group('$HasPendingOutgoingMutationsRepositoryQuery tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('No pending changes', () async {
      final outgoingMutationDbTable = MockOutgoingMutationRepository();
      when(() => outgoingMutationDbTable.hasPendingChanges(any(), any()))
          .thenAnswer((invocation) => Future.value(false));
      final db = MockRepository();
      when(() => db.outgoingMutation).thenReturn(outgoingMutationDbTable);
      final context = MockRepositoryQueryContext();
      when(() => db.context(queryScope: any(named: 'queryScope')))
          .thenReturn(context);
      when(() => context.db).thenReturn(db);
      const query =
          HasPendingOutgoingMutationsRepositoryQuery(ignoreRetryLimit: true);

      final hasPending = await query.query(context);

      expect(hasPending, false);
    });

    test('Has pending changes', () async {
      final outgoingMutationDbTable = MockOutgoingMutationRepository();
      when(() => outgoingMutationDbTable.hasPendingChanges(any(), any()))
          .thenAnswer((invocation) => Future.value(true));
      final db = MockRepository();
      when(() => db.outgoingMutation).thenReturn(outgoingMutationDbTable);
      final context = MockRepositoryQueryContext();
      when(() => db.context(queryScope: any(named: 'queryScope')))
          .thenReturn(context);
      when(() => context.db).thenReturn(db);
      const query =
          HasPendingOutgoingMutationsRepositoryQuery(ignoreRetryLimit: true);

      final hasPending = await query.query(context);

      expect(hasPending, true);
    });
  });
}
