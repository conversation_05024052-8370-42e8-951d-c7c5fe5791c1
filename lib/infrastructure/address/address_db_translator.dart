import 'package:bitacora/domain/address/address.dart';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';
import 'package:bitacora/domain/phone/phone.dart';
import 'package:bitacora/infrastructure/address/address_db_contract.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';

class AddressDbTranslator implements DbTranslator<Address> {
  const AddressDbTranslator();

  @override
  Set<Field> get nestedModelFields => phoneNestedModelFields;

  @override
  Future<Address> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Address(
      id: fields[AddressField.id]?.value(map),
      remoteId: fields[AddressField.remoteId]?.value(map),
      value: fields[AddressField.value]?.value(map),
      type: fields[AddressField.type]?.value(map),
      updatedAt: fields[AddressField.updatedAt]?.value(map),
      createdAt: fields[AddressField.createdAt]?.value(map),
      personDetail:
          await fields[AddressField.personDetail]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Address model) async {
    final map = <String, dynamic>{};
    const contract = AddressDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.value, model.value);
    addField(map, contract.type, model.type);
    addField(map, contract.updatedAt, model.updatedAt);
    addField(map, contract.createdAt, model.createdAt);

    await saveNestedModel<PersonDetail>(context, map, contract.personDetailId,
        context.db.personDetail, model.personDetail);
    return map;
  }
}
