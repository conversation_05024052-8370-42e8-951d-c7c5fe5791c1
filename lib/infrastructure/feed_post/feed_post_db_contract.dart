import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class FeedPostDbContract extends DbContract {
  static const String _ = 'fp_';
  static const String _tableName = 'feedPost';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String title = '${_}title';
  final String content = '${_}content';
  final String pinned = '${_}pinned';
  final String readAt = '${_}readAt';
  final String updatedAt = '${_}updatedAt';
  final String createdAt = '${_}createdAt';
  final String authorId = '${_}authorId';
  final String organizationId = '${_}organizationId';

  const FeedPostDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithFeedTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $title TEXT NOT NULL,
    $content TEXT NOT NULL,
    $pinned INTEGER NOT NULL,
    $readAt INTEGER,
    $updatedAt INTEGER NOT NULL,
    $createdAt INTEGER NOT NULL,
    $authorId INTEGER NOT NULL,
    $organizationId INTEGER NOT NULL
  )
  ''';
}
