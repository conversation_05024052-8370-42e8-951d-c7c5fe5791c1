import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqlite_api.dart';

class OrganizationDbContract extends DbContract {
  static const String _ = 'o_';
  static const String _tableName = 'organization';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String createdAt = '${_}createdAt';
  final String updatedAt = '${_}updatedAt';
  final String name = '${_}name';
  final String activePlan = '${_}activePlan';
  final String color = '${_}color';
  final String userHasSeen = '${_}userHasSeen';
  final String ownerId = '${_}ownerId';

  const OrganizationDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersion;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $createdAt INTEGER NOT NULL,
    $updatedAt INTEGER NOT NULL,
    $name TEXT NOT NULL,
    $activePlan INTEGER NOT NULL,
    $color INTEGER NOT NULL,
    $userHasSeen INTEGER NOT NULL DEFAULT 0,
    $ownerId INTEGER NOT NULL
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithOrganizationActivePlan) {
      await db.execute(
        'ALTER TABLE $tableName ADD $activePlan INTEGER NOT NULL DEFAULT 1',
      );
    }

    // FIXME: Drop proStatus (for Android).

    if (oldVersion < kDbVersionWithOrganizationHasUserSeen) {
      await db.execute(
        'ALTER TABLE $tableName ADD $userHasSeen INTEGER NOT NULL DEFAULT 1',
      );
    }
  }
}
