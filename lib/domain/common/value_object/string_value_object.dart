import 'package:bitacora/domain/common/value_object/value_object.dart';

class StringValueObject extends ValueObject<String?> {
  const StringValueObject(super.value);

  @override
  String? get dbValue => value;

  @override
  String? get apiValue => dbValue;
}

class EmptyStringIsNullValueObject extends StringValueObject {
  const EmptyStringIsNullValueObject(String? value)
      : super(value == null || value.length == 0 ? null : value);
}

class NonNullableStringValueObject extends ValueObject<String> {
  const NonNullableStringValueObject(super.value);

  @override
  String get dbValue => value;

  @override
  String get apiValue => dbValue;
}

class NonEmptyStringValueObject extends NonNullableStringValueObject {
  const NonEmptyStringValueObject(super.value) : assert(value.length > 0);
}
