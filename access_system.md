# Bitacora Access System

A guide.

_The problem_  
We want to grant a user powers within an organization. Specifically, read, write, admin and owner privileges on
resources.

_The solution_  
The Access System.

The API provides with a list of access grants through `/access/`. Each grant consists of a permission level, and the
associated resource id and type.  
i.e.`{resourceId:1, resourceType:Organization, permission:31}`

The server sends this information for two purposes:

1. Logic in the client that depends on access.  
   i.e, dis/allowing a `save` button through `write` access.
2. Managing the state of its caches.  
   It is the responsibility of the client to maintain its caches in sync with access grants changes, i.e., losing access
   to a resource should delete it's related data.  
   *Due to legacy reasons (broken sync model), we don't actually delete the data, we mark it as dirty instead.

### Access Levels

The access-level of a resource is a union of privileges that can be represented using the first 7 bits of an int, each
bit consisting of a privilege (or set of).

- `limited(1, ------l)`  
  Doesn't actually grant any specific privilege. Instead, it indicates that the user has access to some resource within
  its scope.  
  i.e., a user with `limited` access to an `organization` might have `read` access to a `tag` or `write` access to
  a `project` within the `organization`.
- `read(2, -----r-)`
- `write(4, ----w--)`
- `admin(8, ---a---)`
- `owner(16, --o----)`
- `readOwn(32, -R-----)`
- `writeOwn(64, W------)`

#### Typical User Roles

- `limited(1, ----l)`
- `guest(3, ---rl)`
- `member(7, --wrl)`
- `admin(15, -awrl)`
- `owner(31, oawrl)`  
  *The set of valid roles is a superset of these typical roles.

#### Working with Access Levels

Use bitwise AND (&) and compare to the required privilege for equality. These operators can be used in sqlite queries.

`access & requiredLevel == requiredLevel`

i.e.

_Scenario A_

Consider a `member(7, --wrl, 00111)` access

- `member(7) & limited(1) = limited(1)` **Has** `limited` access  
  `(00111 & 00001 = 00001)`
- `member(7) & read(2) = read(2)` **Has** `read` access  
  `(00111 & 00010 = 00010)`
- `member(7) & write(4) = write(4)` **Has** `write` access
- `member(7) & admin(8) = 0` **No** `admin` access
- `member(7) & owner(16) = 0` **No** `owner` access

_Scenario B_

Consider a`guest(3), ---rl, 00011)` access

- `guest(3) & limited(1) = limited(1)` **Has** `limited` access
- `guest(3) & read(2) = read(2)` **Has** `read` access
- `guest(3) & write(4) = 0` **No** `write` access
- `guest(3) & admin(8) = 0` **No** `admin` access
- `guest(3) & owner(16) = 0` **No** `owner` access

### Resource Types

The list of possible resources for which you can have an access grant.

- `organization`  
  Grants access to all contained `entries`, `projects` and `tags`.  
  If not `limited`, all `projects` are syncable.
- `project`  
  Grants access to all associated `entries`.
- `tag`  
  Grants access to all associated `entries`.
- `projectEntry`  
  Grants access to a specific `entry`.
- `organizationEntries`  
  DEPRECATED (previously used for readOwn,writeOwn privileges).

#### Working with Inherited Access by Resource

The access level of a resource can be computed as the union of privileges granted on parents of said resource.

_Scenario A_

Consider `entryA` is associated to `projectA`, `projectB`, `tagA`,
`organizationA`, with the following grants:  
`limited(1)` by `organizationA`  
`member(5)` by `projectA`    
`member(5)` by `projectB`    
`guest(3)` by `tagA`  
`owner(16)` by `entryA`  
The access level for the `entry` would be `owner(23)`  
`00001 | 00101 | 00101 | 00011 | 10000 = 10111 = 23`

_Scenario B_

Consider `entryB` is associated to `projectA`, `tagA`, `organizationA`, with the following grants:  
`limited(1)` by `organizationA`  
`none` by `projectA`  
`guest(3)` by `tagA`  
The access level for the `entry` would be `guest(3)`  
`00001 | 00011 = 00011 = guest(3)`

_Scenario C_

Consider `projectA` is associated to `organizationA`, with the following grants:  
`guest(3)` by `organizationA`  
`none` by `projectA`  
The access level for the `project` would be `guest(3)` access.

## Defining User Roles in Practice

Bitácora | Staff (web) https://app.bitacora.io/staff

This UI is available to users with `admin` privilege.  
It allows them to define the roles for the rest of the `organization`'s `users` from the following list:

#### Superusers

- **`owner(31)`**(non-editable, only one `owner` per `organization`)
- **`admin(15)`**

#### Members

- **`member(7)` See and write all logs.**  
  `read` and `write` access across the whole `organization`.
- **`member(97)` See and write own logs.**  
  An atypical `member`, as they lack `read` and `write` access. Instead, has `readOwn` `writeOwn`.

#### Guests

- **`guest(3)` See all logs.**  
  `read` access across the whole `organization`
- **`guest(33)` See own logs.**  
  An atypical `guest`, as they lack `read` access. Instead, has `readOwn`.

#### Limited

By `project`

- **`limited(1), project member(7)` See and write all logs**  
  `read` and `write` access across the whole `project`.
- **`limited(1), project member(97)` See and write own logs**  
  An atypical `project member`, as they lack `read` access.  
  They can `writeOwn` `entries` to the specified `projects`, and are only able to `read` their own logs.
- **`limited(1), project guest(3)` See all logs**
  `read` access across the whole `project`.
- **`limited(1), project limited(33)` See own logs**  
  An atypical `project guest`, as they lack `read` access.  
  They can only read their own `entries` in the specified `projects`.

By `tag`

- **`limited (1), tag guest(3)` See all logs**  
  `read` access on the specified `tags` list (`entries` with any of the specified `tags`)
- **`limited (1), tag limited(33)` See own logs**  
  An atypical `tag guest`, as they lack `read` access.

When selecting an option from the 'Limited' dropdown, a list of
`projects` or `tags` must be specified. The access level applies to the entire list. For now, there is no mechanism for
defining granular levels per resource (`guest` on ProjectA , `member` on ProjectB, etc.).

## FAQ

**_1 - What are considered 'My own logs'?_**

These are `entries` authored by the user or assigned to them. Users lose access to their own `entries` when they are
given `limited` access in an organization.

**_2 - How are `projectEntry` / `organizationEntries` resource types used?_**

`projectEntry` seems to be unused.  
It was originally meant for granting access to a specific entry.  
May have been replaced by `entry` assignments (`assignee` field).

`organizationEntries` is used in `bitacora-android`. When present, the user's own `entries` are visible to them:

```
boolean shouldShowOwn = accessTable
  .hasResourcePermission(
    "OrganizationEntries",
    Bitacora.configuration.getOrganizationId(), 
    Access.LIMITED,
  );
  
if (shouldShowOwn) {
  rawQuery += 
    "AND (e." + DbContract.ProjectEntry.COLUMN_AUTHOR_ID + " = ? "
      "OR e." + DbContract.ProjectEntry.COLUMN_LAST_EDIT_AUTHOR_ID + " = ? "
      "OR e." + DbContract.ProjectEntry.COLUMN_ASSIGNEE_ID + " = ?"
    ")";
}
```

**_3 - What are the `admin` and `owner` access levels?_**

Special privileges like adding `users`, deleting `projects`, defining `access`, etc. These are not currently relevant in
`bitacora-flutter`.

## Access System in `bitacora-flutter`

Work is in progress for integrating the access system into `bitacora-flutter`.

### API

`bitacora_flutter` is the first system that makes use of the `readOwn` `writeOwn`. Since we handle this differently in
bitacora_android, we use different endpoints to fetch slightly different versions of the access models.

### Implementation Details

Sqlite tables:

- `access` (holds list of access as received from API)  
  i.e., {resourceId:1(remote), resourceType:1, permission:31}
- `accessEntry` (caches computed access level for each `entry` in db)
  i.e., {entryId:1, permission:31}

Whenever we save an `entry` to db, we compute the access level based on the contents of `access` table, and store the
result in the
`accessEntry` table. The computed access level is the union (`permissionA | permissionB | ... `) of all permissions
related to the `entry`. You should always expect this to be set. If not, it should be computed before querying,
otherwise results will be incomplete.

Whenever we `sync` the `access` table updates. If there were changes, the `accessEntry` table flushes and re-computes.

Db queries on `entries` should typically join with `accessEntry` to verify access to an entry before presenting to the
user.
