import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';

class CustomFieldMetadataDbTranslator
    implements DbTranslator<CustomFieldMetadata> {
  const CustomFieldMetadataDbTranslator();

  @override
  Set<Field> get nestedModelFields => customFieldMetadataNestedModelFields;

  @override
  Future<CustomFieldMetadata> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return CustomFieldMetadata(
      id: fields[CustomFieldMetadataField.id]?.value(map),
      remoteId: fields[CustomFieldMetadataField.remoteId]?.value(map),
      value: await fields[CustomFieldMetadataField.value]?.nested(context, map),
      fieldName: fields[CustomFieldMetadataField.fieldName]?.value(map),
      updatedAt: fields[CustomFieldMetadataField.updatedAt]?.value(map),
      createdAt: fields[CustomFieldMetadataField.createdAt]?.value(map),
      user: await fields[CustomFieldMetadataField.user]?.nested(context, map),
      project:
          await fields[CustomFieldMetadataField.project]?.nested(context, map),
      customField: await fields[CustomFieldMetadataField.customField]
          ?.nested(context, map),
      allowedValue: await fields[CustomFieldMetadataField.allowedValue]
          ?.nested(context, map),
      templatelog: await fields[CustomFieldMetadataField.templatelog]
          ?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, CustomFieldMetadata model) async {
    final map = <String, dynamic>{};
    const contract = CustomFieldMetadataDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.value, model.value);
    addField(map, contract.fieldName, model.fieldName);
    addField(map, contract.updatedAt, model.updatedAt);
    addField(map, contract.createdAt, model.createdAt);

    if (model.user != null) {
      await saveNestedModel<User>(
          context, map, contract.userId, context.db.user, model.user);
    }
    if (model.project != null) {
      await saveNestedModel<Project>(
          context, map, contract.projectId, context.db.project, model.project);
    }
    await saveNestedModel<CustomField>(context, map, contract.customFieldId,
        context.db.customField, model.customField);
    await saveNestedModel<Templatelog>(context, map, contract.templatelogId,
        context.db.templatelog, model.templatelog);
    await saveNestedModel<CustomFieldAllowedValue>(
        context,
        map,
        contract.allowedValueId,
        context.db.customFieldAllowedValue,
        model.allowedValue);
    return map;
  }
}
