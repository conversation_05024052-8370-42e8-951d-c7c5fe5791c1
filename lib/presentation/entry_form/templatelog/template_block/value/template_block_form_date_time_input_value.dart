import 'package:bitacora/presentation/entry_form/templatelog/template_block/value/template_block_form_value.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TemplateBlockFormDateTimeInputValue
    extends TemplateBlockFormValue<DateTime?> {
  final DateFormat _dateTimeFormat;
  final TextEditingController inputController = TextEditingController();

  TemplateBlockFormDateTimeInputValue(super.value, this._dateTimeFormat) {
    inputController.addListener(_updateValue);
  }

  void _updateValue() {
    value = inputController.text.isEmpty
        ? null
        : _dateTimeFormat.parse(inputController.text);
  }

  @override
  void dispose() {
    inputController.removeListener(_updateValue);
    super.dispose();
  }
}
