import 'dart:async';

import 'package:audioplayers/audioplayers.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/audio/attachment_audio_player.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/audio/audio_player_widget.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import '../../../../../mocktail_fallback_values.dart';
import '../../../../../test_util.dart';
import '../../../../daylog/audio/mocks.dart';

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
  });

  group('$AttachmentAudioPlayer', () {
    testWidgets('On selected', (tester) async {
      withIOS(() async {
        final audioPlayer = mockAudioPlayer();
        final selectedAttachmentPath = ValueNotifier<AttachmentPath?>(null);
        final attachment = mockAttachment();
        await withInjected<AudioPlayer>(audioPlayer, () async {
          await tester.pumpWidget(testAttachmentAudioPlayer(
            selectedAttachmentPath: selectedAttachmentPath,
            attachment: attachment,
          ));

          await tester.tap(find.byType(AnimatedIcon));
          await tester.pumpAndSettle();
        });

        expect(find.byType(AudioPlayerWidget), findsOneWidget);
        expect(selectedAttachmentPath.value!.value, attachment.path!.value);
      });
    });

    testWidgets('On completed', (tester) async {
      final stateStreamController = StreamController<PlayerState>.broadcast();

      final audioPlayer = mockAudioPlayer(
        onPlayerStateChanged: stateStreamController.stream,
      );
      final selectedAttachmentPath = ValueNotifier<AttachmentPath?>(null);
      final attachment = mockAttachment();
      await withInjected<AudioPlayer>(audioPlayer, () async {
        await tester.pumpWidget(testAttachmentAudioPlayer(
          selectedAttachmentPath: selectedAttachmentPath,
          attachment: attachment,
        ));
        await tester.tap(find.byType(AnimatedIcon));
        await tester.pumpAndSettle();

        stateStreamController.add(PlayerState.completed);
        await tester.pumpAndSettle();
      });

      expect(find.byType(AudioPlayerWidget), findsOneWidget);
      expect(selectedAttachmentPath.value, null);
    });
  });
}

Widget testAttachmentAudioPlayer({
  String? absolutePath,
  ValueNotifier<AttachmentPath?>? selectedAttachmentPath,
  Attachment? attachment,
}) {
  return ChangeNotifierProvider<ValueNotifier<AttachmentPath?>>.value(
    value: selectedAttachmentPath ?? ValueNotifier(null),
    child: MaterialApp(
      home: Scaffold(
        body: Center(
          child: AttachmentAudioPlayer(
            absolutePath: absolutePath ?? '',
            attachment: attachment ?? mockAttachment(),
          ),
        ),
      ),
    ),
  );
}
