import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/feature_flag/value/feature_flag_key.dart';
import 'package:bitacora/domain/feature_flag/value/feature_flag_type.dart';
import 'package:bitacora/domain/feature_flag/value/feature_flag_value.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/user/user.dart';

export 'package:bitacora/domain/feature_flag/value/feature_flag_key.dart';
export 'package:bitacora/domain/feature_flag/value/feature_flag_type.dart';
export 'package:bitacora/domain/feature_flag/value/feature_flag_value.dart';

class FeatureFlag extends Model {
  final FeatureFlagKey? key;
  final FeatureFlagValue? value;
  final FeatureFlagType? type;
  final Organization? organization;
  final User? user;

  const FeatureFlag({
    super.id,
    super.remoteId,
    this.key,
    this.value,
    this.type,
    this.organization,
    this.user,
  });

  @override
  Map<Field, dynamic> get fields => {
        FeatureFlagField.id: id,
        FeatureFlagField.remoteId: remoteId,
        FeatureFlagField.key: key,
        FeatureFlagField.value: value,
        FeatureFlagField.type: type,
        FeatureFlagField.organization: organization,
        FeatureFlagField.user: user,
      };
}

enum FeatureFlagField with Field {
  id,
  remoteId,
  key,
  value,
  type,
  organization,
  user,
}

const featureFlagNestedModelFields = <FeatureFlagField>{
  FeatureFlagField.organization,
  FeatureFlagField.user,
};
