import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class EntryGroupEntryDbContract extends DbContract {
  static const String _ = 'ege_';
  static const String _tableName = 'entryGroupEntry';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String entryGroupId = '${_}entryGroupId';
  final String entryId = '${_}entryId';
  final String createFromId = '${_}createFromId';

  const EntryGroupEntryDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithEntryGroupTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $entryGroupId INTEGER NOT NULL,
    $entryId INTEGER NOT NULL,
    $createFromId INTEGER,
    UNIQUE ($entryGroupId, $entryId)
  )
  ''';
}
