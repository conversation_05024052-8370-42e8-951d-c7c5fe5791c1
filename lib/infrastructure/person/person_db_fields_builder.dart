import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/person/person.dart';
import 'package:bitacora/domain/person/person_fields_builder.dart';
import 'package:bitacora/domain/person/value/person_birthdate.dart';
import 'package:bitacora/domain/person/value/person_first_name.dart';
import 'package:bitacora/domain/person/value/person_last_name.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/person/person_db_contract.dart';

class PersonDbFieldsBuilder extends DbFieldsBuilder
    implements PersonFieldsBuilder {
  PersonDbFieldsBuilder() {
    _id();
  }

  PersonDbContract get contract => const PersonDbContract();

  PersonDbFieldsBuilder _id() {
    addField(
      PersonField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  PersonDbFieldsBuilder remoteId() {
    addField(
      PersonField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  PersonDbFieldsBuilder firstName() {
    addField(
      PersonField.firstName,
      DbField(
        column: contract.firstName,
        valueBuilder: (v) => PersonFirstName(v),
      ),
    );
    return this;
  }

  @override
  PersonDbFieldsBuilder lastName() {
    addField(
      PersonField.lastName,
      DbField(
        column: contract.lastName,
        valueBuilder: (v) => PersonLastName(v),
      ),
    );
    return this;
  }

  @override
  PersonDbFieldsBuilder birthDate() {
    addField(
      PersonField.birthDate,
      DbField(
        column: contract.birthDate,
        valueBuilder: (v) => PersonBirthDate(
            v == null ? null : DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  PersonDbFieldsBuilder createdAt() {
    addField(
      PersonField.birthDate,
      DbField(
        column: contract.birthDate,
        valueBuilder: (v) =>
            PersonBirthDate(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  PersonDbFieldsBuilder updatedAt() {
    addField(
      PersonField.birthDate,
      DbField(
        column: contract.birthDate,
        valueBuilder: (v) =>
            PersonBirthDate(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  PersonDbFieldsBuilder user(Fields fields) {
    addField(
      PersonField.user,
      DbField(
        key: PersonField.user,
        column: contract.userId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.user.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  PersonDbFieldsBuilder detail(Fields fields) {
    addField(
      PersonField.detail,
      DbField(
        key: PersonField.detail,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext.db.personDetail
            .findByPersonId(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
