import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';

class MockTextEditingController extends Mock implements TextEditingController {}

class MockedTextEditingController {
  late MockTextEditingController mock;
  void Function()? callback;

  MockedTextEditingController({required ValueNotifier<String> text}) {
    mock = MockTextEditingController();
    when(() => mock.text).thenAnswer((invocation) {
      return text.value;
    });
    when(() => mock.addListener(any())).thenAnswer((invocation) {
      final callback = invocation.positionalArguments[0];
      text.addListener(callback);
    });
  }
}
