import 'dart:io';

import 'package:bitacora/util/logger/logger.dart';

///
/// Lock a file so that it's not deleted immediately after an attachment is
/// deleted.
///
/// Files will be deleted if there's no references in db, but there could be
/// references in memory.
///
/// Progresslog scenario:
/// 1) Create open entry with attachment and save.
/// 2) Create progresslog with same attachment (don't save yet).
/// 3) Navigate to open entry from progresslog form.
/// 4) Delete attachment from open entry. (file would normally be deleted)
/// 5) Go back to progresslog. (file reference should still be valid)
///
/// Why not RandomAccessFile.lock?
/// Tried but got
/// _RandomAccessFile.lockSync (dart:io/file_impl.dart:1034:7)
/// Unhandled Exception: FileSystemException: lock failed, path = '...'
/// (OS Error: Bad file descriptor, errno = 9)
///
class FileLock {
  static FileLock? _instance;

  final Map<String, int> _lockedFilePaths = <String, int>{};

  FileLock._internal();

  factory FileLock() {
    _instance ??= FileLock._internal();
    return _instance!;
  }

  void lock(File file) {
    _lockedFilePaths[file.path] = (_lockedFilePaths[file.path] ?? 0) + 1;
  }

  void release(File file) {
    _lockedFilePaths[file.path] = (_lockedFilePaths[file.path] ?? 1) - 1;
    if (_lockedFilePaths[file.path] == 0) {
      _lockedFilePaths.remove(file.path);
    }
  }

  bool isLocked(File file) {
    return _lockedFilePaths.containsKey(file.path);
  }
}

Future<void> maybeDeleteFile(File file) async {
  if (FileLock().isLocked(file)) {
    // FIXME: set up maintenance... Trigger for later, maybe the file is unlocked
    return;
  }
  try {
    await file.delete();
  } catch (e) {
    logger.i('file:delete Failed with exception $e');
  }
}
