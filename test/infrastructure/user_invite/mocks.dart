import 'dart:async';

import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/user_invite/user_invite.dart';
import 'package:bitacora/infrastructure/user_invite/user_invite_db_table.dart';
import 'package:mocktail/mocktail.dart';

class MockUserInviteDbTable extends Mock implements UserInviteDbTable {}

class MockUserInviteModelTranslator extends Mock
    implements ModelTranslator<UserInvite> {}

UserInviteDbTable mockUserInviteDbTable({
  StreamController<Mutation<UserInvite>>? controller,
}) {
  final mock = MockUserInviteDbTable();
  when(() => mock.getMutations()).thenAnswer((_) {
    final streamController =
        controller ?? StreamController<Mutation<UserInvite>>.broadcast();
    return streamController.stream;
  });
  when(() => mock.save(any(), any()))
      .thenAnswer((_) => Future.value(const LocalId(1)));
  when(() => mock.delete(any(), any())).thenAnswer((_) => Future.value(1));
  when(() => mock.deleteUserInvite(any(), any()))
      .thenAnswer((_) => Future.value());

  return mock;
}
