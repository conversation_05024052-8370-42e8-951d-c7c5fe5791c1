import 'package:bitacora/domain/common/person_detail_contact_type.dart';
import 'package:bitacora/domain/common/value_object/log_day.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/presentation/widgets/user_avatar/user_avatar.dart';
import 'package:bitacora/util/bitacora_icons.dart';
import 'package:bitacora/util/clipboard.dart';
import 'package:bitacora/util/url_launcher.dart';
import 'package:bitacora/util/web_external_launcher.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:url_launcher/url_launcher.dart';

class UserDetailBottomSheet extends StatelessWidget {
  final User user;

  const UserDetailBottomSheet({
    super.key,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const SizedBox(height: 16.0),
          UserAvatar(user: user, radius: 64),
          const SizedBox(height: 16.0),
          TextButton(
            onPressed: () {
              Clipboard().copyText(user.person!.fullName!);
            },
            child: Text(
              user.person!.fullName!,
              style: Theme.of(context).textTheme.titleMedium,
            ),
          ),
          if (user.person?.detail?.description?.value != null) ...[
            const SizedBox(height: 8.0),
            Text(
              user.person?.detail?.description?.displayValue ?? '-',
              style: Theme.of(context).textTheme.labelMedium,
            ),
          ],
          const SizedBox(height: 32.0),
          if (user.person!.detail!.company?.value != null)
            _UserDetailTile(
              icon: Icons.domain,
              label: AppLocalizations.of(context)!.company,
              title: user.person!.detail!.company!.displayValue,
              onTap: () {
                Clipboard()
                    .copyText(user.person!.detail!.company!.displayValue);
              },
            ),
          if (user.person!.detail!.area?.value != null)
            _UserDetailTile(
              icon: Icons.business_center_outlined,
              label: AppLocalizations.of(context)!.area,
              title: user.person!.detail!.area!.displayValue,
              onTap: () {
                Clipboard().copyText(user.person!.detail!.area!.displayValue);
              },
            ),
          _UserDetailSection(
            tiles: user.person!.detail!.phones!.map(
              (e) => _UserDetailTile(
                title: e.number!.displayValue,
                label: e.type!.displayValue,
                icon: Icons.phone_outlined,
                trailing: IconButton(
                  icon: const Icon(BitacoraIcons.whatsapp),
                  onPressed: () {
                    WebExternalLauncher()
                        .launchWhatsapp(e.number!.urlValue, '');
                  },
                ),
                onTap: () {
                  UrlLauncher().launch(
                    'tel:${e.number!.value}',
                    mode: LaunchMode.platformDefault,
                  );
                },
              ),
            ),
            label: AppLocalizations.of(context)!.phoneNumbers,
          ),
          _UserDetailSection(
            tiles: user.person!.detail!.emails!.isEmpty
                ? [
                    _UserDetailTile(
                      title: user.email!.displayValue,
                      label: PersonDetailContactType.main.displayValue,
                      icon: Icons.email_outlined,
                      onTap: () {
                        UrlLauncher().launch(
                          'mailto:${user.email!.value!}',
                          mode: LaunchMode.platformDefault,
                        );
                      },
                    ),
                  ]
                : user.person!.detail!.emails!.map(
                    (e) => _UserDetailTile(
                      title: e.value!.displayValue,
                      label: e.type!.displayValue,
                      icon: Icons.email_outlined,
                      onTap: () {
                        UrlLauncher().launch(
                          'mailto:${e.value!.value}',
                          mode: LaunchMode.platformDefault,
                        );
                      },
                    ),
                  ),
            label: AppLocalizations.of(context)!.emails,
          ),
          _UserDetailSection(
            tiles: user.person!.detail!.addresses!.map(
              (e) => _UserDetailTile(
                title: e.value!.displayValue,
                label: e.type!.displayValue,
                icon: Icons.location_on_outlined,
                onTap: () {
                  Clipboard().copyText(e.value!.value);
                },
              ),
            ),
            label: AppLocalizations.of(context)!.addresses,
          ),
          if (user.person!.birthDate!.value != null)
            _UserDetailTile(
              icon: Icons.cake_outlined,
              label: AppLocalizations.of(context)!.birthday,
              title: kLogDayDisplayDateFormat
                  .format(user.person!.birthDate!.value!),
              onTap: () {},
            ),
          SizedBox(height: MediaQuery.paddingOf(context).bottom),
        ],
      ),
    );
  }
}

class _UserDetailTile extends StatelessWidget {
  final String title;
  final String label;
  final IconData icon;
  final VoidCallback onTap;
  final Widget? trailing;

  const _UserDetailTile({
    required this.title,
    required this.label,
    required this.icon,
    required this.onTap,
    this.trailing,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: Icon(
        icon,
        color: Theme.of(context).colorScheme.onSecondaryContainer,
      ),
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.labelSmall,
          ),
          Text(title),
        ],
      ),
      onTap: onTap,
      trailing: trailing,
    );
  }
}

class _UserDetailSection extends StatelessWidget {
  final String label;
  final Iterable<_UserDetailTile> tiles;

  const _UserDetailSection({required this.label, required this.tiles});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (tiles.isNotEmpty) ...[
          const SizedBox(height: 16),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              label,
              style: Theme.of(context).textTheme.labelMedium,
            ),
          ),
          ...tiles,
          const SizedBox(height: 16),
        ],
      ],
    );
  }
}
