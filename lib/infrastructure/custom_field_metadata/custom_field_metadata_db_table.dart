import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata_repository.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field/custom_field_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_fields_builder.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_translator.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_db_contract.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/template/template_db_contract.dart';
import 'package:bitacora/infrastructure/template_block/template_block_db_contract.dart';
import 'package:bitacora/infrastructure/template_group/template_group_db_contract.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_db_contract.dart';
import 'package:bitacora/util/logger/logger.dart';

class CustomFieldMetadataDbTable
    extends DbTable<CustomFieldMetadata, CustomFieldMetadataDbFieldsBuilder>
    implements
        CustomFieldMetadataRepository<DbContext,
            CustomFieldMetadataDbFieldsBuilder> {
  @override
  CustomFieldMetadataDbContract get contract =>
      const CustomFieldMetadataDbContract();

  @override
  DbTranslator<CustomFieldMetadata> get translator =>
      const CustomFieldMetadataDbTranslator();

  @override
  CustomFieldMetadataDbFieldsBuilder get fieldsBuilder =>
      CustomFieldMetadataDbFieldsBuilder();

  @override
  Future<List<CustomFieldMetadata>> findAll(
      DbContext context, LocalId templatelogId) async {
    const customField = CustomFieldDbContract();
    const customFieldOptions = CustomFieldOptionsDbContract();
    const templateBlock = TemplateBlockDbContract();
    const templateGroup = TemplateGroupDbContract();
    const templatelog = TemplatelogDbContract();
    const template = TemplateDbContract();

    final sqlQuery = '''
      SELECT ${columnsForSelect(context.fields!)}
        FROM $tableName
      LEFT JOIN ${customField.tableName}
        ON ${contract.customFieldId} = ${customField.id}
      LEFT JOIN ${customFieldOptions.tableName}
        ON  ${customField.id} = ${customFieldOptions.customFieldId}
      LEFT JOIN ${templateBlock.tableName}
        ON ${customFieldOptions.id} = ${templateBlock.customFieldOptionsId}
      LEFT JOIN ${templateGroup.tableName}
        ON ${templateBlock.templateGroupId} = ${templateGroup.id}
      LEFT JOIN ${template.tableName}
        ON ${templateGroup.templateId} = ${template.id}
      LEFT JOIN ${templatelog.tableName}
        ON ${template.id} = ${templatelog.templateId}
      WHERE ${templatelog.id} = ?
        AND ${contract.templatelogId} = ?
      ORDER BY
        ${contract.createdAt} DESC,
        ${templateGroup.order} ASC,
        ${templateBlock.row} ASC,
        ${templateBlock.order} ASC
    ''';

    return await rawQuery(context, sqlQuery, [
      templatelogId.dbValue,
      templatelogId.dbValue,
    ]);
  }

  @override
  Future<List<CustomFieldMetadata>> findValuesByCustomFieldId(
    DbContext context,
    LocalId customFieldId,
  ) async {
    final queryScope = context.queryScope!;
    const customField = CustomFieldDbContract();
    const templatelog = TemplatelogDbContract();
    const access = AccessEntryDbContract();
    const entry = EntryDbContract();

    final sqlQuery = '''
      SELECT ${columnsForSelect(context.fields!)}
      FROM $tableName
      LEFT JOIN ${customField.tableName} 
        ON ${customField.id} = ${contract.customFieldId}
      LEFT JOIN ${templatelog.tableName}
        ON ${templatelog.id} = ${contract.templatelogId}  
      LEFT JOIN ${entry.tableName}
        ON ${entry.extensionId} = ${templatelog.id}
      LEFT JOIN ${access.tableName}
        ON ${entry.id} = ${access.entryId}
      WHERE ${entry.extensionType} = ? 
        AND ${contract.customFieldId} = ?
        AND ${contract.value} != ''
        AND (${access.permission} & $kAccessRead = $kAccessRead
          OR (
            ${access.permission} & $kAccessReadOwn = $kAccessReadOwn 
            AND (
              ${entry.authorId} = ${queryScope.userId!.dbValue}
              OR ${entry.assigneeId} = ${queryScope.userId!.dbValue}
            ) 
          )
        )
      GROUP BY ${contract.value}
      ORDER BY ${contract.value} DESC
    ''';

    return await rawQuery(context, sqlQuery, [
      ExtensionType.templatelog.dbValue,
      customFieldId.dbValue,
    ]);
  }

  @override
  Future<void> saveAll(
      DbContext context, Mutation<Templatelog> templatelogMutation) async {
    assert(context.txn != null);

    final customFieldsMetadata = templatelogMutation.model!.fieldsMetadata!;
    final templatelogId = templatelogMutation.id!;
    logger.i(
        'custom-field-metadata-db-table:saveAll templatelog[$templatelogId] ');

    final savedCustomFieldsMetadataIds = <LocalId>{};
    for (final customFieldMetadata in customFieldsMetadata) {
      assert(customFieldMetadata.templatelog?.id == null ||
          customFieldMetadata.templatelog?.id == templatelogId);
      final savedId = await save(
        context,
        customFieldMetadata.templatelog?.id == null
            ? customFieldMetadata.copyWith(
                templatelog: Templatelog(id: templatelogId))
            : customFieldMetadata,
      );
      savedCustomFieldsMetadataIds.add(savedId ?? customFieldMetadata.id!);
    }

    logger.i('custom-field-metadata-db-table:saveAll '
        '[${savedCustomFieldsMetadataIds.map((e) => e.value).join(',')}] ');
    if (templatelogMutation.type != MutationType.insert) {
      await _deleteLeftoverCustomFieldsMetadata(
          context, templatelogId, savedCustomFieldsMetadataIds);
    }
  }

  Future<void> _deleteLeftoverCustomFieldsMetadata(
    DbContext context,
    LocalId templatelogId,
    Set<LocalId> updatedCustomFieldsMetadataIds,
  ) async {
    final customFieldsMetadata = await findAll(
      context.copyWith(
          fields: context.db.customFieldMetadata.fieldsBuilder.build()),
      templatelogId,
    );
    logger
        .i('custom-field-metadata-db-table:_deleteLeftoverCustomFieldsMetadata'
            '[${customFieldsMetadata.map((e) => e.id?.value).join(',')}] ');
    for (final customFieldMetadata in customFieldsMetadata) {
      if (!updatedCustomFieldsMetadataIds.contains(customFieldMetadata.id)) {
        await delete(context, customFieldMetadata.id!);
      }
    }
  }

  @override
  Future<void> deleteAll(DbContext context, LocalId extensionId) async {
    final fieldsMetadata = await findAll(
      context.copyWith(
          fields: context.db.customFieldMetadata.fieldsBuilder.build()),
      extensionId,
    );

    for (final fieldMetadata in fieldsMetadata) {
      await delete(context, fieldMetadata.id!);
    }
  }
}
