import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';

class ReportFormPageContextSnapshot extends ContextSnapshot {
  ReportFormPageContextSnapshot(super.context);

  @override
  List<ValueShot> get valueShots => [
        ValueShot.navigator(),
        ValueShot.provider<Repository>(),
        ValueShot.provider<ActiveOrganization>(),
      ];
}
