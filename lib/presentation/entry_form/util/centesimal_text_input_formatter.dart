import 'package:flutter/services.dart';
import 'package:intl/intl.dart';

class CentesimalTextInputFormatter extends TextInputFormatter {
  // Leaving just in case it's handy
  // static final RegExp _regExp = RegExp(r'(^\d*(?:[\.\,]\d{0,2})?)$');

  // FIXME: Europe
  static final NumberFormat _numberFormat = NumberFormat('###,###.##');

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // Allow empty
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // Check it's a number
    num n;
    try {
      n = _numberFormat.parse(newValue.text);
    } catch (e) {
      return oldValue;
    }

    // Two digits
    if (_numberFormat.parse(n.toStringAsFixed(2)) !=
        _numberFormat.parse('$n')) {
      return oldValue;
    }

    // Add possible trail.
    // i.e. User might be typing 1.0, we shouldn't reformat it to 1 in case
    // they were trying to reach 1.01
    final split = newValue.text.split('.');
    String? trail;
    if (split.length == 2) {
      trail = split[1];
      if (trail.length > 2) {
        trail = trail.substring(0, 2);
      }
    }

    // Reformat it, but keep the trail for now. This adds commas: 1000 -> 1,000
    final fixedNewValue = '${_numberFormat.format(n.toInt())}'
        '${trail != null ? '.$trail' : ''}';
    final lengthDelta = fixedNewValue.length - newValue.text.length;
    return TextEditingValue(
      text: fixedNewValue,
      selection: newValue.selection.copyWith(
        baseOffset: newValue.selection.baseOffset + lengthDelta,
        extentOffset: newValue.selection.extentOffset + lengthDelta,
      ),
      composing: newValue.composing,
    );
  }
}
