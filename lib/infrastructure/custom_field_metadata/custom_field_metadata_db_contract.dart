import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class CustomFieldMetadataDbContract extends DbContract {
  static const String _ = 'cfm_';
  static const String _tableName = 'customFieldMetadata';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String value = '${_}value';
  final String fieldName = '${_}fieldName';
  final String updatedAt = '${_}updatedAt';
  final String createdAt = '${_}createdAt';
  final String userId = '${_}userId';
  final String projectId = '${_}projectId';
  final String allowedValueId = '${_}allowedValueId';
  final String customFieldId = '${_}customFieldId';
  final String templatelogId = '${_}templatelogId';

  const CustomFieldMetadataDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithTemplatelogTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $value,
    $fieldName TEXT NOT NULL,
    $updatedAt INTEGER NOT NULL,
    $createdAt INTEGER NOT NULL,
    $userId INTEGER,
    $projectId INTEGER,
    $allowedValueId INTEGER,
    $customFieldId INTEGER NOT NULL,
    $templatelogId INTEGER NOT NULL
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithTemplatelogTables) {
      return db.execute(create);
    }

    if (oldVersion < kDbVersionWithMetadataAllowedValueId) {
      await db.execute('ALTER TABLE $tableName ADD $allowedValueId INTEGER');
    }
  }
}
