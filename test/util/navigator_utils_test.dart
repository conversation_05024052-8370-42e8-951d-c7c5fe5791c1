import 'dart:async';

import 'package:bitacora/util/navigator_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  final contexts = <String, NavigatorUtilsContextSnapshot>{};

  Widget testApp() {
    return MaterialApp(
      onGenerateRoute: (settings) => MaterialPageRoute(builder: (context) {
        contexts[settings.name!] = _takeContextSnapshot(context);
        return Text(settings.name!);
      }),
      home: Builder(builder: (context) {
        contexts['/'] = _takeContextSnapshot(context);
        return const Text('First');
      }),
    );
  }

  group('$NavigatorUtils tests', () {
    test('Injects same ', () {
      expect(NavigatorUtils(), NavigatorUtils());
    });

    testWidgets('Does not pop first route', (tester) async {
      contexts.clear();
      await tester.pumpWidget(testApp());

      NavigatorUtils().popUntilParentRoute(contexts['/']!);
      await tester.pumpAndSettle();

      expect(find.text('First'), findsOneWidget);
    });

    testWidgets('Pops single route', (tester) async {
      contexts.clear();
      await tester.pumpWidget(testApp());
      unawaited(contexts['/']!.read<NavigatorState>().pushNamed('Second'));
      await tester.pumpAndSettle();

      NavigatorUtils().popUntilParentRoute(contexts['Second']!);
      await tester.pumpAndSettle();

      expect(find.text('First'), findsOneWidget);
      expect(find.text('Second'), findsNothing);
    });

    testWidgets('Pops multiple above, stays in middle', (tester) async {
      contexts.clear();
      await tester.pumpWidget(testApp());
      for (var i = 2; i < 5; i++) {
        unawaited(contexts[i == 2 ? '/' : '${i - 1}th']!
            .read<NavigatorState>()
            .pushNamed('${i}th'));
        await tester.pump();
      }

      NavigatorUtils().popUntilParentRoute(contexts['3th']!);
      await tester.pumpAndSettle();

      expect(find.text('First'), findsNothing);
      expect(find.text('2th'), findsOneWidget);
      expect(find.text('3th'), findsNothing);
      expect(find.text('4th'), findsNothing);
    });

    testWidgets('Pop Until Root', (tester) async {
      contexts.clear();
      await tester.pumpWidget(testApp());
      for (var i = 2; i < 6; i++) {
        unawaited(contexts[i == 2 ? '/' : '${i - 1}th']!
            .read<NavigatorState>()
            .pushNamed('${i}th'));
        await tester.pump();
      }

      NavigatorUtils().popUntilRoot(contexts['5th']!.read<NavigatorState>());
      await tester.pumpAndSettle();

      expect(find.text('First'), findsOneWidget);
    });
  });
}

NavigatorUtilsContextSnapshot _takeContextSnapshot(BuildContext context) {
  return NavigatorUtilsContextSnapshot(context);
}
