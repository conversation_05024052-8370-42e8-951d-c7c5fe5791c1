import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_source/entry_source.dart';
import 'package:bitacora/domain/entry_source/entry_source_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_source/entry_source_db_contract.dart';

class EntrySourceDbFieldsBuilder extends DbFieldsBuilder
    implements EntrySourceFieldsBuilder {
  EntrySourceDbFieldsBuilder() {
    _id();
  }

  EntrySourceDbContract get contract => const EntrySourceDbContract();

  EntrySourceDbFieldsBuilder _id() {
    addField(
      EntrySourceField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  EntrySourceFieldsBuilder type() {
    addField(
      EntrySourceField.type,
      DbField(
        column: contract.type,
        valueBuilder: (v) => EntrySourceType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  EntrySourceFieldsBuilder metadata() {
    addField(
      EntrySourceField.metadata,
      DbField(
        column: contract.metadata,
        valueBuilder: (v) => EntrySourceMetadata(v),
      ),
    );
    return this;
  }

  @override
  EntrySourceFieldsBuilder entry(Fields fields) {
    addField(
      EntrySourceField.entry,
      DbField(
        key: EntrySourceField.entry,
        column: contract.entryId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.entry.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
