import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class FeatureFlagDbContract extends DbContract {
  static const String _ = 'ff_';
  static const String _tableName = 'featureFlag';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String key = '${_}key';
  final String value = '${_}value';
  final String type = '${_}type';
  final String modelId = '${_}modelId';

  const FeatureFlagDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithFeatureFlags;

  @override
  String get create => '''
    CREATE TABLE $tableName (
      $id INTEGER PRIMARY KEY AUTOINCREMENT,
      $remoteId INTEGER UNIQUE,
      $key TEXT NOT NULL,
      $value INTEGER NOT NULL,
      $type INTEGER NOT NULL,
      $modelId INTEGER NOT NULL
    )
  ''';
}
