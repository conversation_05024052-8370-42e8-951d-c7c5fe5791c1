import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:latlong2/latlong.dart';

class ProjectApiTranslator implements ModelTranslator<Project> {
  const ProjectApiTranslator();

  @override
  Project fromMap(Map<String, dynamic> data) {
    return Project(
      remoteId: RemoteId(data['id']),
      name: ProjectName(data['name']),
      description: ProjectDescription(data['description']),
      address: ProjectAddress(data['address']),
      location: data['longitude'] == null || data['latitude'] == null
          ? const LatLngValueObject(null)
          : LatLngValueObject(LatLng(data['latitude'], data['longitude'])),
      type: ProjectType(data['project_type']),
      syncLastEntryUpdatedAt: ProjectSyncLastEntryUpdatedAt(
          getDateTimeFromApi(data['last_entry_updated_at'])),
      // FIXME: could this field be null? test: create project, delete entry, sync...
      organization: Organization(remoteId: RemoteId(data['organization_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(Project model) {
    return toMapWithPrefix(model);
  }

  Map<String, dynamic> toMapWithPrefix(Project model, {String prefix = ''}) {
    final map = <String, dynamic>{};
    if (model.remoteId!.value != null) {
      map['${prefix}id'] = model.remoteId!.apiValue;
    } else {
      map['${prefix}name'] = model.name!.apiValue;
      map['organization_id'] = model.organization!.remoteId!.apiValue;
    }
    return map;
  }
}
