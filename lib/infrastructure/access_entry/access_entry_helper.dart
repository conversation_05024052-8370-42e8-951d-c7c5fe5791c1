import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_repository_query.dart';
import 'package:bitacora/infrastructure/access_entry/access_list_for_entry_db_query.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/util/logger/logger.dart';

Future<int?> getPermissionForEntry(DbContext context, LocalId entryId) async {
  final entry = await context.db.query(
    AccessEntryRepositoryQuery(id: entryId),
    context: context,
  );

  logger.i('access-entry-helper:getPermissionForEntry: ${entry?.id}');

  /// FIXME: Investigate why extension can become NULL at this point
  if (entry?.extension == null) {
    logger.f(
      'access-entry-helper:getPermissionForEntry:'
      'entry[${entry?.id?.value}] NULL or extension NULL',
    );
    return null;
  }

  try {
    final accesses = await context.db
        .query(AccessListForEntryDbQuery(entry: entry!), context: context);
    return _getPermission(accesses);
  } catch (e, s) {
    logger.f(
      'access-entry-helper:getPermissionForEntry:'
      'cannot get access to entry[${entry?.id?.value}]',
    );
    logger.f(e);
    logger.f(s);
    return null;
  }
}

int _getPermission(List<Access> accesses) {
  var permission = 0;
  for (final access in accesses) {
    permission |= access.permission!.value;
  }
  return permission;
}
