import 'dart:async';

import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_contract.dart';

class ProgresslogDbTranslator implements DbTranslator<Progresslog> {
  const ProgresslogDbTranslator();

  @override
  Set<Field> get nestedModelFields => progresslogNestedModelFields;

  @override
  Future<Progresslog> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Progresslog(
      id: fields[ProgresslogField.id]?.value(map),
      remoteId: fields[ProgresslogField.remoteId]?.value(map),
      progress: fields[ProgresslogField.progress]?.value(map),
      entry: await fields[ProgresslogField.entry]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, Progresslog model) async {
    final map = <String, dynamic>{};
    const contract = ProgresslogDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.progress, model.progress);

    await _maybeAddLegacyProgressDelta(context, map);

    final parentEntryId = await _resolveParentEntryId(context, model.entry!);
    addField(map, contract.entryId, parentEntryId);

    return map;
  }

  FutureOr<LocalId> _resolveParentEntryId(
      DbContext context, Entry entry) async {
    if (entry.id != null) {
      return entry.id!;
    }

    final entryWithId = await context.db.entry.findByRemoteId(
        context.copyWith(fields: context.db.entry.fieldsBuilder.build()),
        entry.remoteId!);
    return entryWithId!.id!;
  }

  Future<void> _maybeAddLegacyProgressDelta(
      DbContext context, Map<String, dynamic> map) async {
    // FIXME: Determine if column exists, and cache result
    // i.e.
    //
    // final executor = await context.executor;
    // final tableInfo = await executor.rawQuery(
    //   'PRAGMA table_info(${ProgresslogDbContract().tableName})',
    // );
    // var columnExists = false;
    // for (final columnInfo in tableInfo) {
    //   if (columnInfo['name'] == 'pr_progressDelta') {
    //     columnExists = true;
    //     break
    //   }
    // }
    // Missing steps:
    // 1. Drop column in contract (without altering current db, as it's not
    //   supported on android.
    // 2. Cache result of columnExists, so we don't read from disk each time.
    // 3. Clear cache when db is nuked
    map[(const ProgresslogDbContract()).progressDelta] = 0;
  }
}
