import 'package:bitacora/infrastructure/open_state/open_state_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$OpenStateDbContract tests', () {
    test('Create $OpenStateDbContract', () {
      expectRemovingSpaces(
        const OpenStateDbContract().create,
        '''
        CREATE TABLE openState (
          os_id INTEGER PRIMARY KEY AUTOINCREMENT,
          os_progressive INTEGER NOT NULL,
          os_progress INTEGER NOT NULL,
          os_endDay INTEGER NOT NULL
        )
        ''',
      );
    });
  });
}
