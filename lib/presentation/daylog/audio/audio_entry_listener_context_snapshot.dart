import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/application/cache/template/template_cache.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_navigator_props.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';

class AudioEntryListenerControllerContextSnapshot extends ContextSnapshot {
  AudioEntryListenerControllerContextSnapshot(super.context);

  @override
  List<ValueShot> get valueShots => [
        ValueShot.navigator(),
        ValueShot.provider<Repository>(),
        ValueShot.provider<ActiveLogDay>(),
        ValueShot.provider<ActiveOrganization>(),
        ValueShot.provider<ActiveSession>(),
        ValueShot.provider<ActiveProject>(),
        ValueShot.provider<AnalyticsLogger>(),
        ValueShot.provider<ApiHelper>(),
        ValueShot.provider<ApiTranslator>(),
        ValueShot.provider<TemplateCache>(),
        ValueShot.provider<ProjectCache>(),
      ];

  EntryFormPageContextSnapshot toEntryFormPageContextSnapshot() {
    return EntryFormPageContextSnapshot.fromContextSnapshot(this);
  }
}
