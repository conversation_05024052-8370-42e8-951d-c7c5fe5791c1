# A user opens the app. There's stuff we want to keep in sync. Let's sync.

query Sync(
    $orgId: ID,
    $projectId: ID,
    $shouldQueryProjects: Boolean!,
    $projectsAfter: String,
    ... # Same stuff (shouldQueryX, xCursor) for other org-project-level entities (i.e. users, tags, etc.)...
    ) {

  # Maybe an option for getting the most 'relevant' projects first (MRU?)
  # If cursor is not empty, it either contains the data from the last page, or the last cursor from previous pagination
  # This might be called on initial sync.
  # This might be called on each sync query, if the client wishes to keep this specific connection in sync.
  projectsConnection(orgId:$orgId, first:20, after:$projectsAfter) @include(if: $shouldQueryProjects) {
    totalCount
    node {
      ...projectFields
    }
    pageInfo {
      endCursor
      hasNextPage
    }
  }

  ... # Same stuff for all other org-level entities
  entriesConnection(orgId:$orgId, first:20, after:$entriesAfter) @include(if: $shouldQueryEntries) { ... }
  ...
}

fragment projectFields on Project {
  id
  name
  isDeleted
}
