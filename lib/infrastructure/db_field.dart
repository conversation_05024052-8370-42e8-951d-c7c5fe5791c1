import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';

class DbField<T> {
  final Field? _key;
  final String? _column;
  final Function(List<String> columns)? _columnAdder;

  final T Function(dynamic value)? _valueBuilder;
  final T Function(Map<String, dynamic> props)? _valueFromPropsBuilder;
  final T Function(Map<String, dynamic> values)? _multiValueBuilder;

  final Future<T?> Function(DbContext nestedContext, dynamic value)?
      _nestedBuilder;
  final DbFields? _nestedFields;

  final Future<T?> Function(DbContext context, Map<String, dynamic> values,
      Map<String, dynamic>? props)? _multiNestedBuilder;

  DbField({
    Field? key,
    String? column,
    Function(List<String> columns)? columnAdder,
    T Function(dynamic value)? valueBuilder,
    T Function(Map<String, dynamic> props)? valueFromPropsBuilder,
    T Function(Map<String, dynamic> values)? multiValueBuilder,
    DbFields? nestedFields,
    Future<T?> Function(DbContext nestedContext, dynamic value)? nestedBuilder,
    Future<T?> Function(DbContext context, Map<String, dynamic> values,
            Map<String, dynamic>? props)?
        multiNestedBuilder,
  })  : _key = key,
        _column = column,
        _columnAdder = columnAdder,
        _valueBuilder = valueBuilder,
        _valueFromPropsBuilder = valueFromPropsBuilder,
        _multiValueBuilder = multiValueBuilder,
        _nestedFields = nestedFields,
        _nestedBuilder = nestedBuilder,
        _multiNestedBuilder = multiNestedBuilder;

  DbFields? get nestedFields => _nestedFields;

  T value(Map<String, dynamic> values) {
    if (_valueBuilder != null) {
      return _valueBuilder(values[_column]);
    }
    return _multiValueBuilder!(values);
  }

  T valueFromProps(Map<String, dynamic> props) {
    return _valueFromPropsBuilder!(props);
  }

  Future<T?> nested(DbContext context, Map<String, dynamic> values,
      {Map<String, dynamic>? props}) async {
    if (_nestedBuilder != null && _column != null && _key != null) {
      return values[_column] == null
          ? null
          : _nestedBuilder(
              context.nested(_key, props: props)!,
              values[_column],
            );
    }

    if (_multiNestedBuilder != null) {
      return _multiNestedBuilder(context, values, props);
    }
    throw Exception('No nested builder found for key $_key');
  }
}

List<String> columnsFromFields(Iterable<DbField> fields) {
  final columns = <String>[];
  for (final field in fields) {
    if (field._column != null) columns.add(field._column);
    if (field._columnAdder != null) field._columnAdder(columns);
  }
  return columns.toSet().toList();
}

String columnsForSelect(DbFields fields) {
  return columnsFromFields(fields.map.values).join(',');
}
