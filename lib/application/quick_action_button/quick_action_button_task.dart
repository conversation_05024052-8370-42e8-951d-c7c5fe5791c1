import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/template/template_cache.dart';
import 'package:bitacora/application/location_tracking/entry/location_tracking_owner_strategy_entry.dart';
import 'package:bitacora/application/location_tracking/location_tracking_handler.dart';
import 'package:bitacora/application/location_tracking/location_tracking_owner_strategy.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field/value/custom_field_type.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/quick_action_button/quick_action_button.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/template_block/template_block.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/templatelog/value/templatelog_template_group_name.dart';
import 'package:bitacora/domain/worklog/worklog.dart';
import 'package:bitacora/presentation/location_tracking/tracking_max_duration_option.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/clock.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class QuickActionButtonTask {
  final QuickActionButton button;

  QuickActionButtonTask({required this.button});

  void run(BuildContext context) {
    switch (button.type!.value) {
      case QuickActionButtonTypeValue.createEntry:
        if (button.payload!.isEntryTracked ?? false) {
          _createLocationTracking(context);
        }
        break;
    }
  }

  void _createLocationTracking(BuildContext context) async {
    final ownerStrategy = _determineLocationTrackingOwnerStrategy(context);
    if (ownerStrategy == null) {
      return;
    }

    await const LocationTrackingHandler().start(context, ownerStrategy);

    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
      SharedPreferencesKeys.maxTrackingDuration,
      MaxTrackingDuration.eightHours.dbValue,
    );
  }

  LocationTrackingOwnerStrategy? _determineLocationTrackingOwnerStrategy(
    BuildContext context,
  ) {
    switch (button.payload!.extensionType!) {
      case ExtensionType.worklog:
        return LocationTrackingOwnerStrategyEntry(
          extension: Worklog(
            title: WorklogTitle(button.name!.value),
            type: WorklogType.values.first,
            project: context.read<ActiveProject>().value ??
                Project(
                  name: const ProjectName(kDefaultProjectName),
                  organization: context.read<ActiveOrganization>().value!,
                  isSyncable: ProjectIsSyncable(true),
                ),
          ),
        );
      case ExtensionType.templatelog:
        final templates = context.read<TemplateCache>().value!;
        final template = templates.firstWhereOrNull((e) {
          return e.remoteId! == button.payload!.templateRemoteId!;
        });

        if (template == null) {
          return null;
        }

        return LocationTrackingOwnerStrategyEntry(
          extension: Templatelog(
            template: template,
            templateName: TemplatelogTemplateName(template.name!.value),
            fieldsMetadata: _buildFieldsMetadata(template),
          ),
        );
      default:
        throw 'QuickActionButtonTask:ExtensionType '
            '${button.payload!.extensionType} not supported';
    }
  }

  List<CustomFieldMetadata> _buildFieldsMetadata(Template template) {
    final fieldsMetadata = <CustomFieldMetadata>[];
    for (final group in template.groups!) {
      for (final block in group.blocks!) {
        if (block.customFieldOptions!.isRequired!.value!) {
          throw 'Unable to create a templatelog entry with required fields';
        }

        final now = Clock().now();
        final fieldType = block.customFieldOptions!.customField!.type!.value;
        switch (fieldType) {
          case CustomFieldTypeValue.text:
          case CustomFieldTypeValue.textArea:
            fieldsMetadata.add(CustomFieldMetadata(
              value: CustomFieldMetadataValue(
                block.role?.value == TemplateBlockRoleValue.mainText
                    ? button.name!.value
                    : null,
              ),
              fieldName: CustomFieldMetadataFieldName(
                  block.customFieldOptions!.customField!.name!.value),
              customField: block.customFieldOptions!.customField!,
              createdAt: CustomFieldMetadataCreatedAt(now),
              updatedAt: CustomFieldMetadataUpdatedAt(now),
            ));
            break;
          case CustomFieldTypeValue.dateTime:
          case CustomFieldTypeValue.date:
          case CustomFieldTypeValue.time:
          case CustomFieldTypeValue.floatNumber:
          case CustomFieldTypeValue.intNumber:
          case CustomFieldTypeValue.user:
          case CustomFieldTypeValue.project:
            fieldsMetadata.add(CustomFieldMetadata(
              value: CustomFieldMetadataValue(null),
              fieldName: CustomFieldMetadataFieldName(
                  block.customFieldOptions!.customField!.name!.value),
              customField: block.customFieldOptions!.customField!,
              createdAt: CustomFieldMetadataCreatedAt(now),
              updatedAt: CustomFieldMetadataUpdatedAt(now),
            ));
            break;
          case CustomFieldTypeValue.checkbox:
            fieldsMetadata.add(CustomFieldMetadata(
              value: CustomFieldMetadataValue(false),
              fieldName: CustomFieldMetadataFieldName(
                  block.customFieldOptions!.customField!.name!.value),
              customField: block.customFieldOptions!.customField!,
              createdAt: CustomFieldMetadataCreatedAt(now),
              updatedAt: CustomFieldMetadataUpdatedAt(now),
            ));
            break;
          case CustomFieldTypeValue.radio:
          case CustomFieldTypeValue.select:
            fieldsMetadata.add(CustomFieldMetadata(
              allowedValue: CustomFieldAllowedValue(
                id: block
                    .customFieldOptions!.customField!.allowedValues!.first.id,
              ),
              fieldName: CustomFieldMetadataFieldName(
                  block.customFieldOptions!.customField!.name!.value),
              customField: block.customFieldOptions!.customField!,
              createdAt: CustomFieldMetadataCreatedAt(now),
              updatedAt: CustomFieldMetadataUpdatedAt(now),
            ));
            break;
          case CustomFieldTypeValue.checkboxGroup:
            fieldsMetadata.add(CustomFieldMetadata(
              allowedValue: CustomFieldAllowedValue(
                id: block
                    .customFieldOptions!.customField!.allowedValues!.first.id,
              ),
              fieldName: CustomFieldMetadataFieldName(
                  block.customFieldOptions!.customField!.name!.value),
              customField: block.customFieldOptions!.customField!,
              createdAt: CustomFieldMetadataCreatedAt(now),
              updatedAt: CustomFieldMetadataUpdatedAt(now),
            ));
        }
      }
    }
    return fieldsMetadata;
  }
}
