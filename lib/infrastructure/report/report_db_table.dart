import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/domain/report/report_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/report/report_db_contract.dart';
import 'package:bitacora/infrastructure/report/report_db_fields_builder.dart';
import 'package:bitacora/infrastructure/report/report_db_translator.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:flutter/foundation.dart';

class ReportDbTable extends DbTable<Report, ReportDbFieldsBuilder>
    implements ReportRepository<DbContext, ReportDbFieldsBuilder> {
  ReportDbTable() : super(isSyncable: false);

  @override
  ReportDbContract get contract => const ReportDbContract();

  @override
  DbTranslator<Report> get translator => const ReportDbTranslator();

  @override
  ReportDbFieldsBuilder get fieldsBuilder => ReportDbFieldsBuilder();

  @override
  Future<Report?> findLast(DbContext context) {
    return takeFirst(query(
      context,
      orderBy: '${contract.createdAt} DESC',
      limit: 1,
    ));
  }

  @override
  Future<List<Report>> findAll(DbContext context) {
    return query(
      context,
      where: '${contract.organizationId} = ?',
      whereArgs: [context.queryScope!.orgId!.dbValue],
      orderBy: '${contract.createdAt} DESC',
    );
  }

  @override
  Future<List<Report>> findAllWithPath(DbContext context) {
    return query(
      context,
      where: '${contract.organizationId} = ? AND ${contract.path} IS NOT NULL',
      whereArgs: [context.queryScope!.orgId!.dbValue],
      orderBy: '${contract.createdAt} DESC',
    );
  }

  @override
  Future<List<Report>> findAllDownloaded(DbContext context) {
    return query(
      context,
      where: '${contract.organizationId} = ? AND '
          '${contract.path} IS NOT NULL AND '
          '${contract.isDownloaded} = ?',
      limit: context.cursor?.limit,
      offset: context.cursor?.offset,
      whereArgs: [context.queryScope!.orgId!.dbValue, 1],
      orderBy: '${contract.createdAt} DESC',
    );
  }

  @override
  @protected
  Future<void> onPreDelete(DbContext context, LocalId id) async {
    final report = await find(
      context.copyWith(fields: fieldsBuilder.path().build()),
      id,
    );

    try {
      final path = await StorageUtils()
          .getAbsolutePath(StorageSubdirectory.reports, report!.path!);
      await FileSystemInjector.get().file(path).delete();
    } catch (_) {}
  }
}
