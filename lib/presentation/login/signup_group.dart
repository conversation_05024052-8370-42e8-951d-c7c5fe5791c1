import 'package:bitacora/application/auth_app_service.dart';
import 'package:bitacora/presentation/login/validators.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:country_codes/country_codes.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:mask_text_input_formatter/mask_text_input_formatter.dart';
import 'package:provider/provider.dart';

class SignupGroup extends StatefulWidget {
  final VoidCallback onCancel;

  const SignupGroup({super.key, required this.onCancel});

  @override
  State<SignupGroup> createState() => _SignupGroupState();
}

class _SignupGroupState extends State<SignupGroup> {
  static const String _defaultPhoneDigits = '####################';
  static const String _defaultPhoneMask = '+$_defaultPhoneDigits';

  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _phoneNumberController = TextEditingController();
  final _phoneFocusNode = FocusNode();

  String? _dialCode;
  bool _isSigningUp = false;
  bool _passwordVisible = false;
  String? _errorMessage;

  MaskTextInputFormatter? _phoneMaskFormatter;

  @override
  void initState() {
    super.initState();
    _initLocale();
  }

  void _initLocale() async {
    await CountryCodes.init();
    final CountryDetails details = CountryCodes.detailsForLocale();

    _dialCode = details.dialCode;

    setState(() {
      _phoneMaskFormatter = MaskTextInputFormatter(
        mask:
            '+${List.generate(_dialCode!.length - 1, (_) => '#').join()} $_defaultPhoneDigits',
        filter: {"#": RegExp(r'[0-9]')},
        type: MaskAutoCompletionType.lazy,
        initialText: _dialCode,
      );
    });

    _phoneNumberController.text = _dialCode!;
  }

  @override
  void dispose() {
    _nameController.dispose();
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final secondaryOptionsLeadStyle = Theme.of(context)
        .textTheme
        .labelLarge!
        .copyWith(fontWeight: FontWeight.w300);

    return Form(
      key: _formKey,
      child: AutofillGroup(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            TextFormField(
              autofocus: true,
              autocorrect: false,
              keyboardType: TextInputType.name,
              textCapitalization: TextCapitalization.words,
              textInputAction: TextInputAction.next,
              decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.name),
              controller: _nameController,
              validator: (v) => validateName(context, v),
              autofillHints: const [AutofillHints.name],
              onChanged: (_) => setState(() {
                _errorMessage = null;
              }),
            ),
            const SizedBox(height: 8.0),
            TextFormField(
              autocorrect: false,
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.next,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.email,
              ),
              controller: _emailController,
              validator: (v) => validateEmail(context, v),
              autofillHints: const [AutofillHints.email],
              onChanged: (_) => setState(() {
                _errorMessage = null;
              }),
            ),
            const SizedBox(height: 8.0),
            TextFormField(
              obscureText: !_passwordVisible,
              autocorrect: false,
              keyboardType: TextInputType.text,
              textInputAction: TextInputAction.next,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.password,
                contentPadding: const EdgeInsets.all(kInputDecorationPadding),
                suffixIconColor:
                    Theme.of(context).colorScheme.onSurface.withAlpha(150),
                suffixIconConstraints:
                    const BoxConstraints(maxHeight: 36, maxWidth: 36),
                suffixIcon: Padding(
                  padding: const EdgeInsetsDirectional.only(end: 10.0),
                  child: IconButton(
                    iconSize: 16,
                    padding: EdgeInsets.zero,
                    icon: Icon(
                      _passwordVisible
                          ? Icons.visibility
                          : Icons.visibility_off,
                    ),
                    onPressed: () {
                      setState(() {
                        _passwordVisible = !_passwordVisible;
                      });
                    },
                  ),
                ),
              ),
              controller: _passwordController,
              validator: (v) => validatePassword(context, v),
              autofillHints: const [AutofillHints.newPassword],
              onChanged: (_) => setState(() {
                _errorMessage = null;
              }),
              onFieldSubmitted: (_) {
                FocusScope.of(context).requestFocus(_phoneFocusNode);
              },
            ),
            const SizedBox(height: 32.0),
            TextFormField(
              focusNode: _phoneFocusNode,
              autocorrect: false,
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.done,
              decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.phoneNumber,
              ),
              controller: _phoneNumberController,
              inputFormatters:
                  _phoneMaskFormatter == null ? [] : [_phoneMaskFormatter!],
              validator: (v) => validatePhoneNumber(context, v, _dialCode),
              scrollPadding: const EdgeInsets.only(bottom: 200),
              autofillHints: const [AutofillHints.telephoneNumber],
              onChanged: (value) => setState(() {
                _errorMessage = null;

                if (value.isEmpty) {
                  _phoneNumberController.value =
                      _phoneMaskFormatter!.updateMask(mask: _defaultPhoneMask);
                }
              }),
              onFieldSubmitted: _isSigningUp ? null : (_) => _signup(context),
            ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 8.0),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  _errorMessage!,
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium!
                      .copyWith(color: Theme.of(context).colorScheme.error),
                ),
              ),
            ],
            const SizedBox(height: 4),
            Text(
              AppLocalizations.of(context)!.phoneInputCaption,
              style: Theme.of(context).textTheme.bodySmall,
            ),
            const SizedBox(height: 32),
            SizedBox(
              width: double.infinity,
              child: FilledButton(
                onPressed: _isSigningUp ? null : () => _signup(context),
                child: Text(AppLocalizations.of(context)!.signup),
              ),
            ),
            const SizedBox(height: 8.0),
            TextButton(
              onPressed: _isSigningUp
                  ? null
                  : () {
                      FocusScope.of(context).unfocus();
                      widget.onCancel();
                    },
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    AppLocalizations.of(context)!.cancel,
                    style: secondaryOptionsLeadStyle,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _signup(BuildContext context) {
    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      return;
    }

    FocusScope.of(context).unfocus();

    setState(() {
      _isSigningUp = true;
      _errorMessage = null;
    });

    final name = _nameController.text;
    final email = _emailController.text;
    final password = _passwordController.text;

    final phoneNumber = _phoneNumberController.text == _dialCode
        ? null
        : _phoneNumberController.text;

    context
        .read<AuthAppService>()
        .signup(
          context: context,
          locale: Localizations.localeOf(context).languageCode,
          name: name,
          email: email,
          password: password,
          phoneNumber: phoneNumber,
        )
        .catchError(
          (error) => setState(() {
            _isSigningUp = false;
            _errorMessage = error.toString();
          }),
          test: (e) => e is AuthError,
        )
        .catchError(
          (error) => setState(() {
            _isSigningUp = false;
            _errorMessage = AppLocalizations.of(context)!.somethingWentWrong;
          }),
        );
  }
}
