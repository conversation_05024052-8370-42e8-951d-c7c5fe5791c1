import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:bitacora/application/bug_report/bug_report_notification_action_handler.dart';
import 'package:bitacora/application/notification/daily_reminder/daily_reminder_notification_scheduler.dart';
import 'package:bitacora/application/notification/open_entries/open_state_notification_scheduler.dart';
import 'package:bitacora/util/awesome_notifications/awesome_notifications_channels.dart';
import 'package:flutter/material.dart';

typedef NotificationActionHandlerCallback = void Function(
    BuildContext context, ReceivedAction action);

class AwesomeNotificationsActionHandler {
  final Map<String, NotificationActionHandlerCallback> _handlers = {};
  BuildContext? _appContext;

  static final _instance = AwesomeNotificationsActionHandler._();

  factory AwesomeNotificationsActionHandler() => _instance;

  AwesomeNotificationsActionHandler._();

  void registerHandlers(BuildContext context) async {
    _appContext = context;

    _handlers[NotificationChannelGroups.openStateEntries.key] =
        OpenStateNotificationScheduler().onNotificationAction;
    _handlers[NotificationChannels.dailyReminder.key] =
        DailyReminderNotificationScheduler().onNotificationAction;
    _handlers[NotificationChannels.bugReport.key] =
        BugReportNotificationActionHandler().onNotificationAction;

    await AwesomeNotifications()
        .setListeners(onActionReceivedMethod: onActionReceivedMethod);
  }

  void _handleAction(String handlerKey, ReceivedAction receivedAction) {
    _handlers[handlerKey]!(_appContext!, receivedAction);
  }

  @pragma("vm:entry-point")
  static Future<void> onActionReceivedMethod(
      ReceivedAction receivedAction) async {
    AwesomeNotificationsActionHandler()._handleAction(
        receivedAction.groupKey ?? receivedAction.channelKey!, receivedAction);
  }

  void nuke() {
    _handlers.clear();
  }
}
