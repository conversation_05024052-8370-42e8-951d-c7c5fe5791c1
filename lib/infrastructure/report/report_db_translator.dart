import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/report/report_db_contract.dart';

class ReportDbTranslator implements DbTranslator<Report> {
  const ReportDbTranslator();

  @override
  Set<Field> get nestedModelFields => reportNestedModelFields;

  @override
  Future<Report> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Report(
      id: fields[ReportField.id]?.value(map),
      uuid: fields[ReportField.uuid]?.value(map),
      path: fields[ReportField.path]?.value(map),
      isDownloaded: fields[ReportField.isDownloaded]?.value(map),
      createdAt: fields[ReportField.createdAt]?.value(map),
      postParams: fields[ReportField.postParams]?.value(map),
      getParams: fields[ReportField.getParams]?.value(map),
      organization:
          await fields[ReportField.organization]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Report model) async {
    final map = <String, dynamic>{};
    const contract = ReportDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.uuid, model.uuid);
    addField(map, contract.path, model.path);
    addField(map, contract.isDownloaded, model.isDownloaded);
    addField(map, contract.createdAt, model.createdAt);
    addField(map, contract.postParams, model.postParams);
    addField(map, contract.getParams, model.getParams);
    await saveNestedModel<Organization>(context, map, contract.organizationId,
        context.db.organization, model.organization);
    return map;
  }
}
