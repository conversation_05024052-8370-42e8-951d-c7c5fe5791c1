import 'package:bitacora/presentation/user_settings/location_settings.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '_robots/location_settings_test_robot.dart';

void main() {
  group('$LocationSettings tests', () {
    testWidgets('Has Ui', (tester) async {
      SharedPreferences.setMockInitialValues({});
      final robot = LocationSettingsTestRobot(tester);

      await robot.pumpWidget();

      robot.verifyUi();
    });

    testWidgets('Location disabled', (tester) async {
      const sttEnabled = false;
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: sttEnabled,
      });
      final robot = LocationSettingsTestRobot(tester);

      await robot.pumpWidget();

      await robot.verifyLocationSetting(sttEnabled);
    });

    testWidgets('Location enabled', (tester) async {
      const sttEnabled = true;
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: sttEnabled,
      });
      final robot = LocationSettingsTestRobot(tester);

      await robot.pumpWidget();

      await robot.verifyLocationSetting(sttEnabled);
    });

    testWidgets('Switch On Location', (tester) async {
      const sttEnabled = false;
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: sttEnabled,
      });
      final robot = LocationSettingsTestRobot(tester);

      await robot.pumpWidget();

      await robot.tapSwitch();

      await robot.verifyLocationSetting(true);
    });

    testWidgets('Switch Off Location', (tester) async {
      const sttEnabled = false;
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.entriesLocationEnabled: sttEnabled,
      });
      final robot = LocationSettingsTestRobot(tester);

      await robot.pumpWidget();

      await robot.tapSwitch();

      await robot.verifyLocationSetting(true);
    });
  });
}
