import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/router.dart';
import 'package:bitacora/application/sync/resync/entry/entry_resync.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/qr_code/qr_code.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/entry/entry_api_translator.dart';
import 'package:bitacora/presentation/daylog/location_tracking/location_tracking_top_snack_bar.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller_builder.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_load_repository_query.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_log_day.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_menu_button.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_navigator_props.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_save_permission.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_template_repository_query.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_metadata_notification.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/entry_group/entry_form_page_entry_group_breadcrumb.dart';
import 'package:bitacora/presentation/entry_form/new_fields/new_fields_label.dart';
import 'package:bitacora/presentation/entry_form/new_fields/new_fields_notifier.dart';
import 'package:bitacora/presentation/entry_form/simplelog/simplelog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/type_selector/entry_form_page_type.dart';
import 'package:bitacora/presentation/entry_form/type_selector/entry_form_page_type_selector.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/presentation/widgets/app_bar_action_button.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/access/save_entry_permission.dart';
import 'package:bitacora/util/dialog.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/navigator_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class EntryFormPage extends StatefulWidget {
  final Entry? inputEntry;
  final Entry? relatedEntry;
  final EntryFormControllerBuilder entryFormControllerBuilder;
  final QrCode? qrCode;

  const EntryFormPage({
    super.key,
    this.inputEntry,
    this.relatedEntry,
    this.entryFormControllerBuilder = const EntryFormControllerBuilder(),
    this.qrCode,
  });

  @override
  State<EntryFormPage> createState() => _EntryFormPageState();

  static bool _isNavigating = false;

  static Future<void> navigate(
    EntryFormPageNavigatorProps props, {
    QrCode? qrCode,
    bool replaceRoute = false,
  }) async {
    logger.i(
      'entry-form-page:onTap '
      'isNavigating[$_isNavigating]',
    );
    if (_isNavigating) {
      logger.i(
        'entry-form-page:onTap is already navigating',
      );
      return;
    }

    final isProgresslog = props.entry!.extension is Progresslog;
    _isNavigating = true && !isProgresslog;

    logger.i('entry-form-page:onTap navigating');

    final entry = props.entry;

    final isFromQr = qrCode != null;

    final inputEntry =
        entry?.id == null || isFromQr ? entry : await _loadEntry(props);
    if (inputEntry == null) {
      logger.f('Entry not found when attempting to navigate to form');
      _isNavigating = false;
      return;
    }
    final currentVersion = inputEntry.syncVersion?.value;
    if ((currentVersion ?? kEntrySyncVersion) < kEntrySyncVersion) {
      final entryApiTranslator = props.contextSnapshot
          .read<ApiTranslator>()
          .entry as EntryApiTranslator;
      unawaited(
        // FIXME: Avoid resync if entry is a outgoingMutations table
        EntryResyncInjector()
            .get(
              props.contextSnapshot.read<Repository>(),
              props.contextSnapshot.read<ApiHelper>(),
              entryApiTranslator,
            )
            .resyncEntry(inputEntry),
      );
    }

    if (replaceRoute) {
      await props.contextSnapshot
          .read<NavigatorState>()
          .pushNamedAndRemoveUntil(
        isFromQr ? kRouteEntryFormQrFill : kRouteEntryForm,
        (route) => route.settings.name == '/',
        arguments: [inputEntry],
      );
    } else {
      await props.contextSnapshot.read<NavigatorState>().pushNamed(
        isFromQr ? kRouteEntryFormQrFill : kRouteEntryForm,
        arguments: [
          inputEntry,
          if (isFromQr) qrCode,
        ],
      );
    }

    _isNavigating = false;
  }

  static Future<void> navigateFromRelatedEntry(
    EntryFormPageNavigatorProps props,
  ) async {
    final relatedEntry = await _loadEntry(props);

    if (relatedEntry == null) {
      logger.f('Entry related not found when attempting to navigate to form');
      return;
    }

    await props.contextSnapshot
        .read<NavigatorState>()
        .pushNamed(kRouteEntryFormRelatedFill, arguments: relatedEntry);
  }

  static Future<Entry?> _loadEntry(EntryFormPageNavigatorProps props) {
    final db = props.contextSnapshot.read<Repository>();
    return db
        .query(EntryFormPageLoadRepositoryQuery(entryId: props.entry!.id!));
  }
}

class _EntryFormPageState extends State<EntryFormPage> {
  final NewFieldsNotifier _newFieldsNotifier = NewFieldsNotifier();
  final GlobalKey<FormState> _entryFormKey = GlobalKey<FormState>();

  final ScrollController _scrollController = ScrollController();
  final ValueNotifier<bool> _isScrollIsFromGesture = ValueNotifier(false);

  EntryFormPageType? _entryFormPageType;
  EntryFormController? currentController;
  final _controllers = <EntryFormPageType, EntryFormController>{};
  bool _didSave = false;

  bool get isFromQr => widget.qrCode != null;

  @override
  void dispose() {
    if (!_didSave) {
      currentController?.cancel();
    }
    _newFieldsNotifier.dispose();
    _controllers.forEach((_, e) => e.dispose());
    _scrollController.dispose();
    _isScrollIsFromGesture.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    _initExtensionType();
  }

  Future<void> _initExtensionType() async {
    final sourceEntry = widget.inputEntry ?? widget.relatedEntry;
    if (sourceEntry != null) {
      if (sourceEntry.hasSimplelog) {
        _entryFormPageType = EntryFormPageType.simplelog();
        return;
      }

      if (sourceEntry.extension!.extensionType == ExtensionType.templatelog) {
        _entryFormPageType = EntryFormPageType.templatelog(
          sourceEntry.templatelog!.template!,
        );
        return;
      }
      _entryFormPageType = EntryFormPageType(
          extensionType: sourceEntry.extension!.extensionType);
      return;
    }

    final prefs = await SharedPreferences.getInstance();
    final type = prefs.getInt(SharedPreferencesKeys.defaultExtensionType);

    if (type == null) {
      _entryFormPageType = EntryFormPageType.worklog();
    } else {
      final extensionType = ExtensionType.fromDbValue(type);
      if (extensionType != ExtensionType.templatelog) {
        _entryFormPageType = EntryFormPageType(extensionType: extensionType);
      } else {
        _entryFormPageType =
            await _initTemplateType() ?? EntryFormPageType.worklog();
      }
    }

    setState(() {});
  }

  Future<EntryFormPageType?> _initTemplateType() async {
    final db = context.read<Repository>();
    final activeOrg = context.read<ActiveOrganization>();

    final prefs = await SharedPreferences.getInstance();
    final templateId = prefs.getInt(SharedPreferencesKeys.defaultTemplate);

    final template = await db.query(
        EntryFormPageTemplateRepositoryQuery(LocalId(templateId ?? 0)),
        context: db
            .context()
            .copyWith(queryScope: QueryScope(orgId: activeOrg.value!.id)));

    if (template == null) {
      return null;
    }
    return EntryFormPageType.templatelog(template);
  }

  void initFormController(BuildContext context) {
    if (_entryFormPageType == null) {
      return;
    }
    final props = EntryFormProps(
      inputEntry: widget.inputEntry,
      relatedEntry: widget.relatedEntry,
      logTime: currentController?.logTime.value,
      isSignatureAllowed: widget.inputEntry?.remoteId?.value != null &&
          context
              .read<ActiveOrganization>()
              .value!
              .activePlan!
              .isEntrySignatureAllowed,
      qrCode: widget.qrCode,
    );
    if (!_controllers.containsKey(_entryFormPageType!)) {
      final controller = widget.entryFormControllerBuilder.build(
        context,
        _entryFormPageType!,
        props,
      );
      if (controller is SimplelogFormController) {
        controller.onConvertToWorklog = () {
          setState(() {
            _entryFormPageType = EntryFormPageType.worklog();
            _newFieldsNotifier.clear();
          });
        };
      }
      _controllers[_entryFormPageType!] = controller;
      if (widget.inputEntry != null) {
        // Detect entry deletion to pop navigation.
        controller.liveEntry.addListener(() {
          if (controller.liveEntry.value == null && mounted) {
            NavigatorUtils()
                .popUntilParentRoute(NavigatorUtilsContextSnapshot(context));
          }
        });
      }
    }
    currentController = _controllers[_entryFormPageType!];
  }

  @override
  Widget build(BuildContext context) {
    initFormController(context);
    if (_entryFormPageType == null || currentController == null) {
      return const SizedBox();
    }

    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (didPop, _) {
        if (didPop) {
          return;
        }

        final hasChanges = currentController!.hasChanges.value;
        if (hasChanges &&
            currentController!.savePermission.value ==
                SaveEntryPermission.allowed) {
          _showMaybePopDialog(context);
          return;
        }

        Navigator.pop(context);
      },
      child: ClipRRect(
        borderRadius:
            isFromQr ? BorderRadius.circular(16.0) : BorderRadius.zero,
        child: GestureDetector(
          onTap: FocusScope.of(context).unfocus,
          child: Scaffold(
            appBar: AppBar(
              centerTitle: true,
              title: EntryFormPageLogDay(
                inputEntry: widget.inputEntry,
                logTime: currentController!.logTime,
              ),
              leading: !isFromQr && widget.relatedEntry == null
                  ? null
                  : IconButton(
                      icon: const Icon(Icons.close),
                      onPressed: () {
                        final hasChanges = currentController!.hasChanges.value;
                        if (!hasChanges) {
                          Navigator.of(context).pop();
                        } else {
                          _showMaybePopDialog(context);
                        }
                      },
                    ),
              actions: [
                EntryFormPageMenuButton(inputEntry: widget.inputEntry),
                ValueListenableBuilder<bool>(
                  valueListenable: currentController!.hasChanges,
                  builder: (context, hasChanges, _) {
                    return ValueListenableBuilder(
                      valueListenable: currentController!.savePermission,
                      builder: (context, permission, __) {
                        if (permission != SaveEntryPermission.allowed) {
                          return const SizedBox();
                        }

                        final opacity = hasChanges || isFromQr ? 1.0 : 0.33;
                        return AnimatedOpacity(
                          opacity: opacity,
                          duration: const Duration(milliseconds: 300),
                          child: AppBarActionButton(
                            text: AppLocalizations.of(context)!.save,
                            icon: Icons.save,
                            onPressed: () => _save(context),
                            withPaddingRight: true,
                          ),
                        );
                      },
                    );
                  },
                ),
              ],
            ),
            body: NotificationListener<ScrollNotification>(
              onNotification: (ScrollNotification notification) {
                if (notification is ScrollStartNotification) {
                  if (notification.dragDetails != null) {
                    _isScrollIsFromGesture.value = true;
                  }
                } else if (notification is ScrollEndNotification) {
                  _isScrollIsFromGesture.value = false;
                }
                return false;
              },
              child: MultiProvider(
                providers: [
                  ChangeNotifierProvider<NewFieldsNotifier>.value(
                    value: _newFieldsNotifier,
                  ),
                  ChangeNotifierProvider<ScrollController>.value(
                    value: _scrollController,
                  ),
                  ChangeNotifierProvider<ValueNotifier<bool>>.value(
                    value: _isScrollIsFromGesture,
                  )
                ],
                child: Form(
                  key: _entryFormKey,
                  child: CustomScrollView(
                    controller: _scrollController,
                    shrinkWrap: true,
                    slivers: [
                      SliverToBoxAdapter(
                        child: EntryFormPageSavePermission(
                          listenable: currentController!.savePermission,
                        ),
                      ),
                      if (widget.inputEntry?.locationTracking != null)
                        SliverToBoxAdapter(
                          child: LocationTrackingTopSnackbar(
                            scopeTrackingId:
                                widget.inputEntry!.locationTracking!.id!,
                          ),
                        ),
                      if (widget.inputEntry != null)
                        SliverToBoxAdapter(
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: 8.0,
                              horizontal: 16.0,
                            ),
                            child: EntryFormPageMetadataNotification(
                              entry: widget.inputEntry!,
                            ),
                          ),
                        ),
                      SliverToBoxAdapter(
                        child: Padding(
                          padding: kPageHorizontalInsets,
                          child: Stack(
                            alignment: Alignment.bottomCenter,
                            children: [
                              Divider(
                                thickness: 3,
                                color: Theme.of(context).colorScheme.primary,
                              ),
                              Row(
                                textBaseline: TextBaseline.alphabetic,
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: SizedBox(
                                      height: 50,
                                      child: Row(
                                        children: [
                                          EntryFormPageEntryGroupBreadcrumb(
                                            liveEntry:
                                                currentController!.liveEntry,
                                            relatedEntry: widget.relatedEntry,
                                          ),
                                          Expanded(
                                            child: widget.inputEntry != null
                                                ? currentController!
                                                    .header(context)
                                                : EntryFormTypePageSelector(
                                                    currentType:
                                                        _entryFormPageType!,
                                                    onSelected: (value) =>
                                                        setState(() {
                                                      _entryFormPageType =
                                                          value;
                                                      _newFieldsNotifier
                                                          .clear();
                                                      _saveExtensionTypeDefault(
                                                          value);
                                                    }),
                                                  ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                  currentController!.headerOption(context),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SliverToBoxAdapter(
                        child: Padding(
                          padding: kPageHorizontalInsets,
                          child: NewFieldsLabel(),
                        ),
                      ),
                      SliverPadding(
                        padding: kPageHorizontalInsets,
                        sliver: currentController!.showForm(),
                      ),
                      SliverToBoxAdapter(
                          child: currentController!.formOptions()),
                      SliverToBoxAdapter(
                        child: SizedBox(
                            height: MediaQuery.sizeOf(context).height * 0.3),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _saveExtensionTypeDefault(EntryFormPageType type) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
      SharedPreferencesKeys.defaultExtensionType,
      type.extensionType.dbValue,
    );

    if (type.template != null) {
      await prefs.setInt(
        SharedPreferencesKeys.defaultTemplate,
        type.template!.id!.dbValue,
      );
    }
  }

  void _save(BuildContext context) async {
    if (_entryFormKey.currentState == null ||
        !_entryFormKey.currentState!.validate()) {
      return;
    }

    final navigator = Navigator.of(context);
    final analyticsLogger = context.read<AnalyticsLogger>();
    final saveSnapshot = EntryFormControllerSaveContextSnapshot(context);

    await currentController!.savePermission.check();
    if (currentController!.savePermission.value !=
        SaveEntryPermission.allowed) {
      return;
    }

    _didSave = true;
    unawaited(currentController!.save(saveSnapshot).then((entries) {
      if (entries.length > 1) {
        analyticsLogger.logEvent(AnalyticsEvent.clickSaveEntries, props: {
          kAnalyticsPropLocalIds: entries.map((e) => e.id?.value).join(','),
          kAnalyticsPropRemoteIds:
              entries.map((e) => e.remoteId?.value).join(','),
        });
      } else {
        final entry = entries.first;
        analyticsLogger.logEvent(AnalyticsEvent.clickSaveEntry, props: {
          if (entry.id?.value != null)
            kAnalyticsPropLocalId: '${entry.id?.value}',
          if (entry.remoteId?.value != null)
            kAnalyticsPropRemoteId: '${entry.remoteId?.value}',
        });
      }
    }));

    if (isFromQr) {
      NavigatorUtils().popUntilRoot(navigator);
    } else {
      navigator.pop();
    }
  }

  void _showMaybePopDialog(BuildContext context) {
    showDestructiveDialog(
      context: context,
      title: AppLocalizations.of(context)!.entryNotSaved,
      message: AppLocalizations.of(context)!.willLoseData,
      destroyText: AppLocalizations.of(context)!.discard,
      destroyer: () => Navigator.of(context).pop(),
      cancelText: AppLocalizations.of(context)!.save,
      onCancel: () => _save(context),
    );
  }
}
