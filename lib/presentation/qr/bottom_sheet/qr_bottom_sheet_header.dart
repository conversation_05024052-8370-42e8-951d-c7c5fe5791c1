import 'package:bitacora/domain/common/value_object/log_day.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/qr/bottom_sheet/qr_bottom_sheet_controller.dart';
import 'package:bitacora/presentation/widgets/app_bar_action_button.dart';
import 'package:bitacora/presentation/widgets/log_date/fancy_date.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class QrBottomSheetHeader extends StatelessWidget {
  final QrBottomSheetController controller;

  const QrBottomSheetHeader({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        if (controller.state.value == QrCodeDecodingState.failed ||
            controller.entryFormController != null ||
            controller.identifier != null)
          IconButton(
            icon: Icon(
              Icons.close,
              color: Theme.of(context).primaryColor,
            ),
            onPressed: () {
              controller.onClosePanel();
              Navigator.of(context).pop();
            },
          ),
        if (controller.entryFormController != null) ...[
          FancyDate(
            logDay: LogDay(getLogDayForToday()),
            logTime: controller.entryFormController!.logTime.value,
          ),
          controller.isEditable
              ? AppBarActionButton(
                  text: AppLocalizations.of(context)!.edit,
                  icon: Icons.edit,
                  onPressed: () => controller.editEntry(context),
                  withPaddingRight: true,
                )
              : AppBarActionButton(
                  text: AppLocalizations.of(context)!.save,
                  icon: Icons.save,
                  onPressed: () =>
                      _onSavePressed(context, controller.entryFormController!),
                  withPaddingRight: true,
                ),
        ]
      ],
    );
  }

  void _onSavePressed(BuildContext context, EntryFormController controller) {
    controller.save(EntryFormControllerSaveContextSnapshot(context));
    Navigator.of(context).pop();
  }
}
