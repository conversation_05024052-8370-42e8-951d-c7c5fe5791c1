import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/infrastructure/attachment/attachment_db_contract.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_contract.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_contract.dart';
import 'package:bitacora/infrastructure/worklog/worklog_db_contract.dart';

class EntryExtensionSearchableFields {
  final DbRepository db;
  final _contract = const EntryDbContract();
  final _worklogContract = const WorklogDbContract();
  final _inventoryContract = const InventorylogDbContract();
  final _personnelContract = const PersonnellogDbContract();
  final _progressContract = const ProgresslogDbContract();
  final _attachmentContract = const AttachmentDbContract();

  EntryExtensionSearchableFields(this.db);

  String extensionJoin() {
    return '''
      LEFT JOIN ${_worklogContract.tableName}
      ON (
          ${_contract.extensionId} = ${_worklogContract.id}
          AND ${_contract.extensionType} = ${ExtensionType.worklog.dbValue}
      )
      LEFT JOIN ${_inventoryContract.tableName}
      ON (
          ${_contract.extensionId} = ${_inventoryContract.id}
          AND ${_contract.extensionType} = ${ExtensionType.inventorylog.dbValue}
      )
      LEFT JOIN ${_personnelContract.tableName}
      ON (
          ${_contract.extensionId} = ${_personnelContract.id}
          AND ${_contract.extensionType} = ${ExtensionType.personnellog.dbValue}
      )
      LEFT JOIN ${_progressContract.tableName}
      ON (
          ${_contract.extensionId} = ${_progressContract.id}
          AND ${_contract.extensionType} = ${ExtensionType.progresslog.dbValue}
      )
      LEFT JOIN ${_attachmentContract.tableName}
        ON ${_contract.id} = ${_attachmentContract.entryId}
    ''';
  }

  String extensionColumns(DbFields entryColumns) {
    return columnsFromFields([
      ...entryColumns.map.values,
      ..._worklogSearchableColumns.map.values,
      ..._inventorySearchableColumns.map.values,
      ..._personnelSearchableColumns.map.values,
      ..._progressSearchableColumns.map.values,
      ..._projectSearchableColumns.map.values,
      ..._attachmentSearchableColumns.map.values,
    ]).map((e) => 'coalesce($e, "")').join(' || ');
  }

  DbFields get _worklogSearchableColumns =>
      db.worklog.fieldsBuilder.quantity().title().sublocation().build();

  DbFields get _inventorySearchableColumns => db.inventorylog.fieldsBuilder
      .quantity()
      .itemName()
      .costPrice()
      .provider()
      .sourceSublocation()
      .destSublocation()
      .build();

  DbFields get _personnelSearchableColumns => db.personnellog.fieldsBuilder
      .name()
      .entrance()
      .exit()
      .sublocation()
      .build();

  DbFields get _progressSearchableColumns =>
      db.progresslog.fieldsBuilder.progress().build();

  DbFields get _projectSearchableColumns =>
      db.project.fieldsBuilder.name().build();

  DbFields get _attachmentSearchableColumns =>
      db.attachment.fieldsBuilder.name().comments().build();
}
