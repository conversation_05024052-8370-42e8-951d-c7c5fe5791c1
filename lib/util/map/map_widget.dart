import 'dart:io';

import 'package:bitacora/application/router.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/connectivity/connectivity.dart';
import 'package:bitacora/util/location_utils.dart';
import 'package:bitacora/util/map/map_canvas.dart';
import 'package:bitacora/util/map/map_widget_controller_adapter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:google_api_availability/google_api_availability.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart' as gmaps;
import 'package:latlong2/latlong.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum MapProvider { google, openStreet }

class MapWidget extends StatefulWidget {
  final LatLng? center;
  final LatLng? marker;
  final ValueNotifier<Path<LatLng>>? pathNotifier;
  final double? height;
  final gmaps.MapType? initialMapType;
  final bool isFullScreen;

  const MapWidget({
    super.key,
    this.height,
    this.initialMapType,
    this.marker,
    this.pathNotifier,
    this.isFullScreen = false,
    this.center,
  });

  @override
  State<MapWidget> createState() => _MapWidgetState();
}

class _MapWidgetState extends State<MapWidget> {
  final ValueNotifier<MapWidgetControllerAdapter?> controller =
      ValueNotifier(null);
  ValueNotifier<Path<LatLng>>? smoothPath;
  MapProvider? mapProvider;
  LatLng? center;
  LatLngBounds? bounds;

  @override
  void initState() {
    super.initState();
    _determineMapProvider();
    if (widget.pathNotifier != null) {
      widget.pathNotifier!.addListener(_maybeSmoothPath);
      widget.pathNotifier!.addListener(_determineCenterAndBounds);
      _maybeSmoothPath();
    }
    _determineCenterAndBounds();
  }

  @override
  void dispose() {
    controller.value?.dispose();
    widget.pathNotifier?.removeListener(_maybeSmoothPath);
    widget.pathNotifier?.removeListener(_determineCenterAndBounds);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (mapProvider == null || center == null) {
      return Padding(
        padding: kPageInsets,
        child: Center(child: PlatformCircularProgressIndicator()),
      );
    }

    return ValueListenableBuilder<List<ConnectivityResult?>>(
      valueListenable: Connectivity().notifier,
      builder: (context, connectivityResult, child) =>
          connectivityResult.contains(ConnectivityResult.none)
              ? const SizedBox(height: 0)
              : child!,
      child: SizedBox(
        height: widget.height,
        child: Stack(
          children: [
            MapCanvas(
              mapProvider: mapProvider!,
              center: center!,
              marker: widget.marker,
              path: smoothPath ?? widget.pathNotifier,
              bounds: bounds,
              controller: controller,
              onTap: widget.isFullScreen
                  ? null
                  : () async {
                      await Navigator.of(context).pushNamed(
                        kRouteMap,
                        arguments: [
                          center,
                          widget.marker,
                          widget.pathNotifier,
                          if (mapProvider == MapProvider.google)
                            controller.value?.mapType.value ??
                                widget.initialMapType,
                        ],
                      );
                    },
            ),
            if (mapProvider == MapProvider.google)
              Positioned(
                top: 8,
                left: 8,
                child: _MapLayerButton(
                  onTap: () async {
                    controller.value?.switchLayer();

                    final prefs = await SharedPreferences.getInstance();
                    await prefs.setInt(
                      SharedPreferencesKeys.mapType,
                      (controller.value?.mapType.value ?? widget.initialMapType)
                          .index,
                    );
                  },
                ),
              ),
            Positioned(
              bottom: 8,
              right: 8,
              child: _MapZoomButtons(
                onZoomIn: () => controller.value?.zoomIn(),
                onZoomOut: () => controller.value?.zoomOut(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _determineMapProvider() async {
    if (Platform.isIOS) {
      setState(() {
        mapProvider = MapProvider.google;
      });
      return;
    }

    final googleServicesAvailability = await GoogleApiAvailability.instance
        .checkGooglePlayServicesAvailability();

    setState(() {
      mapProvider = googleServicesAvailability.value == 0
          ? MapProvider.google
          : MapProvider.openStreet;
    });
  }

  void _maybeSmoothPath() {
    const distanceInMeterPerTime = 1.0;
    const stepDistance = distanceInMeterPerTime * 2.0;

    if (widget.pathNotifier!.value.distance < stepDistance) {
      return;
    }

    if (widget.pathNotifier!.value.coordinates.length > 3) {
      if (smoothPath == null) {
        smoothPath = ValueNotifier(widget.pathNotifier!.value
            .equalize(distanceInMeterPerTime, smoothPath: true));
      } else {
        smoothPath!.value = widget.pathNotifier!.value
            .equalize(distanceInMeterPerTime, smoothPath: true);
      }
    }
  }

  void _determineCenterAndBounds() async {
    LatLng? pathCenter;
    if (smoothPath != null) {
      pathCenter = smoothPath!.value.center;
    }

    center = widget.center ??
        widget.marker ??
        pathCenter ??
        (await LocationUtils().determinePosition()).toLatLng();

    if (widget.pathNotifier != null &&
        widget.pathNotifier!.value.coordinates.isNotEmpty) {
      bounds = _getBounds(widget.pathNotifier!.value.coordinates);
    }

    if (mounted) {
      setState(() {});
    }
  }

  LatLngBounds _getBounds(List<LatLng> points) {
    double minLat = points.first.latitude;
    double maxLat = points.first.latitude;
    double minLng = points.first.longitude;
    double maxLng = points.first.longitude;

    for (LatLng point in points) {
      if (point.latitude < minLat) minLat = point.latitude;
      if (point.latitude > maxLat) maxLat = point.latitude;
      if (point.longitude < minLng) minLng = point.longitude;
      if (point.longitude > maxLng) maxLng = point.longitude;
    }

    return LatLngBounds(LatLng(minLat, minLng), LatLng(maxLat, maxLng));
  }
}

class _MapLayerButton extends StatelessWidget {
  final VoidCallback onTap;

  const _MapLayerButton({required this.onTap});

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 4,
      borderRadius: BorderRadius.circular(8),
      color: Theme.of(context).canvasColor,
      child: IconButton(
        padding: const EdgeInsets.all(6.0),
        constraints: const BoxConstraints(),
        onPressed: onTap,
        icon: Icon(
          Icons.layers,
          color: Theme.of(context).colorScheme.onSurface,
        ),
      ),
    );
  }
}

class _MapZoomButtons extends StatelessWidget {
  final VoidCallback onZoomIn;
  final VoidCallback onZoomOut;

  const _MapZoomButtons({required this.onZoomIn, required this.onZoomOut});

  @override
  Widget build(BuildContext context) {
    return Material(
      elevation: 4,
      borderRadius: BorderRadius.circular(8),
      color: Theme.of(context).canvasColor,
      child: Column(
        children: [
          IconButton(
            padding: const EdgeInsets.all(6.0),
            constraints: const BoxConstraints(),
            onPressed: onZoomIn,
            icon: Icon(
              Icons.add,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          IconButton(
            padding: const EdgeInsets.all(6.0),
            constraints: const BoxConstraints(),
            onPressed: onZoomOut,
            icon: Icon(
              Icons.remove,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
        ],
      ),
    );
  }
}
