import 'package:flutter/material.dart';

const _kPlayPauseAnimationDuration = Duration(milliseconds: 200);

class PlayButton extends StatefulWidget {
  final ValueNotifier<bool> isPlaying;
  final VoidCallback onPressed;
  final Color? color;

  const PlayButton({
    super.key,
    required this.isPlaying,
    required this.onPressed,
    required this.color,
  });

  @override
  State<PlayButton> createState() => _PlayButtonState();
}

class _PlayButtonState extends State<PlayButton>
    with SingleTickerProviderStateMixin {
  late final AnimationController _playPauseAnimationController;

  @override
  void initState() {
    super.initState();
    _playPauseAnimationController = AnimationController(
      vsync: this,
      duration: _kPlayPauseAnimationDuration,
    );

    widget.isPlaying.addListener(onPlayingChange);
  }

  @override
  void dispose() {
    widget.isPlaying.removeListener(onPlayingChange);
    super.dispose();
  }

  void onPlayingChange() {
    if (widget.isPlaying.value) {
      _playPauseAnimationController.forward();
    } else {
      _playPauseAnimationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return IconButton(
      icon: AnimatedIcon(
        icon: AnimatedIcons.play_pause,
        progress: _playPauseAnimationController,
        color: widget.color,
      ),
      onPressed: widget.onPressed,
    );
  }
}
