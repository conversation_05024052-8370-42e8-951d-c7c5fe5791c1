import 'dart:math';

import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class MonthlyEntryBarChart extends StatelessWidget {
  final List<int> values;

  const MonthlyEntryBarChart({super.key, required this.values});

  double get _maxValue {
    final maxV = values.reduce(max).toDouble();
    return maxV > 10 ? maxV : 10;
  }

  @override
  Widget build(BuildContext context) {
    return BarChart(_buildBarChartData(context));
  }

  BarChartData _buildBarChartData(BuildContext context) {
    final barGroups = <BarChartGroupData>[];
    for (int i = 0; i < values.length; i++) {
      barGroups.add(BarChartGroupData(
        x: i,
        barRods: [
          BarChartRodData(
            toY: values[i].toDouble(),
            width: 4,
            backDrawRodData: BackgroundBarChartRodData(
              show: true,
              toY: _maxValue,
              color: Colors.transparent,
            ),
            color: Theme.of(context).colorScheme.surface,
          ),
        ],
      ));
    }

    return BarChartData(
      alignment: BarChartAlignment.spaceBetween,
      barGroups: barGroups,
      titlesData: FlTitlesData(
        show: true,
        rightTitles:
            const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        bottomTitles: AxisTitles(
          sideTitles: SideTitles(showTitles: false),
        ),
        leftTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
      ),
      gridData: FlGridData(show: false),
      borderData: FlBorderData(show: false),
      barTouchData: BarTouchData(
        enabled: true,
        touchTooltipData: BarTouchTooltipData(
          tooltipRoundedRadius: 16.0,
          getTooltipColor: (_) {
            return Theme.of(context).colorScheme.surface;
          },
          getTooltipItem: (group, groupIndex, rod, rodIndex) {
            final now = DateTime.now();
            final format = DateFormat('dd/MM/yyyy');
            return BarTooltipItem(
                '${rod.toY.toInt()}',
                TextStyle(
                  color: Theme.of(context).colorScheme.primary,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
                children: [
                  TextSpan(
                    text: '\n${format.format(DateTime(now.year, now.month, group.x))}',
                    style: Theme.of(context).textTheme.bodySmall,
                  )
                ]);
          },
        ),
      ),
    );
  }
}
