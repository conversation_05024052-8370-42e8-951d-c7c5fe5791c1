import 'package:bitacora/domain/common/value_object/value_object.dart';

enum FeatureFlagTypeValue {
  organization,
  user;
}

class FeatureFlagType extends ValueObject<FeatureFlagTypeValue> {
  static const FeatureFlagType organization =
      FeatureFlagType(FeatureFlagTypeValue.organization);
  static const FeatureFlagType user =
      FeatureFlagType(FeatureFlagTypeValue.user);
  static const List<FeatureFlagType> values = [
    organization,
    user,
  ];

  const FeatureFlagType(super.value);

  factory FeatureFlagType.fromDbValue(int dbValue) => values[dbValue];

  @override
  int get dbValue {
    switch (value) {
      case FeatureFlagTypeValue.organization:
        return 0;
      case FeatureFlagTypeValue.user:
        return 1;
    }
  }

  @override
  int get apiValue => dbValue;

  bool get isOrganization => value == FeatureFlagTypeValue.organization;

  bool get isUser => value == FeatureFlagTypeValue.user;
}
