import 'dart:convert';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/base_ai_model_generator.dart';
import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/infrastructure/vertex_ai_generator.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';

abstract class VertexAiModelGenerator<T extends Model>
    implements BaseAiModelGenerator<T, VertexAiGenerator> {
  ModelTranslator<T> get apiTranslator;

  Schema get responseSchema;

  String get systemPrompt;

  @override
  Future<T> generate(
    VertexAiGenerator generator,
    Content input,
    Map<String, dynamic> extraData,
  ) async {
    final combinedParts = <Part>[TextPart(systemPrompt), ...input.parts];

    final response = await generator.model.generateContent(
      [
        Content('user', combinedParts),
        input,
      ],
      generationConfig: GenerationConfig(
        responseSchema: responseSchema,
        responseMimeType: 'application/json',
      ),
    );

    if (response.text == null) {
      return Future.error('Cannot generated content');
    }

    final data = jsonDecode(response.text!)..addAll(extraData);

    return apiTranslator.fromMap(data);
  }
}
