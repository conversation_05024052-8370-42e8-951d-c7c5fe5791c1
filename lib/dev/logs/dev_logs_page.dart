import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/widgets/app_bar_action_button.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/logger/logger_to_file_output.dart';
import 'package:bitacora/util/open_file/open_file_util.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DevLogsPage extends StatefulWidget {
  const DevLogsPage({super.key});

  @override
  State<DevLogsPage> createState() => _DevLogsPageState();
}

class _DevLogsPageState extends State<DevLogsPage> {
  final List<FileSystemEntity> logFiles = [];
  FileSystemEntity? logFile;
  String? foregroundFilename;

  @override
  void initState() {
    super.initState();
    _loadLogFiles();
    _loadForegroundFilename();
  }

  @override
  Widget build(BuildContext context) {
    return Theme(
      data: ThemeData(
        brightness: Brightness.dark,
        primaryColor: bitacoraGreen,
        primarySwatch: bitacoraGreen,
      ),
      child: PopScope(
        canPop: false,
        onPopInvokedWithResult: (didPop, _) {
          if (didPop) {
            return;
          }
          if (logFile != null) {
            setState(() {
              logFile = null;
            });
            return;
          }
          Navigator.pop(context);
        },
        child: Scaffold(
          appBar: AppBar(
            centerTitle: false,
            title: const Text('Logs'),
            actions: [
              if (logFile != null)
                AppBarActionButton(
                  text: 'Open',
                  icon: Icons.open_in_new,
                  onPressed: () => OpenFileUtil().open(logFile!.path),
                )
            ],
          ),
          body: logFile == null
              ? ListView.builder(
                  itemCount: logFiles.length,
                  itemBuilder: (c, i) {
                    return Align(
                      alignment: Alignment.centerLeft,
                      child: TextButton(
                        onPressed: () {
                          setState(() {
                            logFile = logFiles[i];
                          });
                        },
                        child: _buildLogFileButtonText(c, logFiles[i].basename),
                      ),
                    );
                  },
                )
              : SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: SizedBox(
                    width: MediaQuery.sizeOf(context).width * 2,
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          TextButton(
                            onPressed: () {
                              setState(() {
                                logFile = null;
                              });
                            },
                            child: Builder(
                              builder: (c) =>
                                  _buildLogFileButtonText(c, logFile!.basename),
                            ),
                          ),
                          const Divider(height: 2),
                          const SizedBox(height: 10),
                          Text(_getCurrentLog(),
                              textScaler: const TextScaler.linear(0.8)),
                        ],
                      ),
                    ),
                  ),
                ),
        ),
      ),
    );
  }

  Widget _buildLogFileButtonText(BuildContext context, String filename) {
    return Text(
      filename,
      style: TextStyle(
        color: filename == foregroundFilename
            ? Theme.of(context).colorScheme.primary
            : Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  void _loadLogFiles() async {
    final dir = await LoggerToFileOutput.getLogsDirectory();
    setState(() {
      final list = dir.listSync();
      list.sort((a, b) => _fileTime(b) - _fileTime(a));
      logFiles.addAll(list);
    });
  }

  int _fileTime(FileSystemEntity file) {
    final matches = RegExp(kLogFilenameRegexp).allMatches(file.basename);
    return int.parse(matches.first.namedGroup('time')!);
  }

  String _getCurrentLog() {
    return FileSystemInjector.get().file(logFile!.path).readAsStringSync();
  }

  void _loadForegroundFilename() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      foregroundFilename =
          prefs.getString(SharedPreferencesKeys.foregroundLogFilename);
    });
  }
}
