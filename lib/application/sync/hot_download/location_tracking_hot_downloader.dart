import 'package:bitacora/application/location_tracking/location_tracking_api_helper.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/location_point/location_point.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/infrastructure/api_translator.dart';

class LocationTrackingHotDownloader {
  final Repository db;
  final AuthRepository authRepository;
  final ApiTranslator apiTranslator;
  final Organization organization;

  LocationTrackingHotDownloader(
    this.db,
    this.authRepository,
    this.organization,
    this.apiTranslator,
  );

  Future<LocationTracking?> download(
      LocationTrackingUuid locationTrackingUuid) async {
    final apiQuery =
        LocationTrackingApiHelper(authRepository, organization).get(
      '${locationTrackingUuid.apiValue}',
      queryParameters: {'include': 'points'},
    );

    final response = (await apiQuery).data;
    if (response == null || (response is String && response.isEmpty)) {
      return null;
    }

    final locationTracking = apiTranslator.locationTracking.fromMap(response);

    return db.transaction<LocationTracking>((context) async {
      final savedLocationTrackingId =
          await db.locationTracking.save(context, locationTracking);
      final savedPoints = await _savePoints(
          context, locationTracking.points!, savedLocationTrackingId!);

      return locationTracking.copyWith(
        id: savedLocationTrackingId,
        points: savedPoints,
      );
    });
  }

  Future<List<LocationPoint>> _savePoints(
    RepositoryQueryContext dbContext,
    List<LocationPoint> points,
    LocalId locationTrackingId,
  ) async {
    final savedPoints = <LocationPoint>[];
    for (final point in points) {
      final pointToSave =
          point.copyWith(tracking: LocationTracking(id: locationTrackingId));
      final localId = await db.locationPoint.save(dbContext, pointToSave);
      savedPoints.add(pointToSave.copyWith(id: localId!));
    }
    return savedPoints;
  }
}
