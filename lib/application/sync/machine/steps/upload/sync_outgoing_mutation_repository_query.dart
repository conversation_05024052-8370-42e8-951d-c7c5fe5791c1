import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';

class SyncOutgoingMutationRepositoryQuery
    extends RepositoryQuery<OutgoingMutation?> {
  final LocalId? id;
  final bool ignoreRetryLimit;

  const SyncOutgoingMutationRepositoryQuery({
    this.id,
    this.ignoreRetryLimit = false,
  });

  @override
  Future<OutgoingMutation?> run(RepositoryQueryContext context) => id != null
      ? context.db.outgoingMutation.find(context, id!)
      : context.db.outgoingMutation.findNext(context, ignoreRetryLimit);

  @override
  Fields fields(Repository db) {
    final project = db.project.fieldsBuilder
        .remoteId()
        .name()
        .organization(db.organization.fieldsBuilder.remoteId().build())
        .build();

    final locationTrackingFields = db.locationTracking.fieldsBuilder
        .remoteId()
        .uuid()
        .status()
        .createdAt()
        .organization(db.organization.fieldsBuilder.remoteId().build())
        .build();

    final entryFields = db.entry.fieldsBuilder
        .remoteId()
        .day()
        .time()
        .startDate()
        .endDate()
        .startTime()
        .endTime()
        .comments()
        .location()
        .createdAt()
        .assignee(db.user.fieldsBuilder.remoteId().name().build())
        .openState(db.openState.fieldsBuilder
            .progress()
            .progressive()
            .endDay()
            .build())
        .attachments(db.attachment.fieldsBuilder
            .s3Key()
            .name()
            .doodle()
            .comments()
            .isUploaded()
            .build())
        .tags(db.tag.fieldsBuilder.name().build())
        .locationTracking(locationTrackingFields)
        .worklog(db.worklog.fieldsBuilder
            .type()
            .quantity()
            .title()
            .costPrice()
            .salePrice()
            .provider()
            .paymentStatus()
            .priceIsUnit()
            .project(project)
            .sublocation()
            .build())
        .inventorylog(db.inventorylog.fieldsBuilder
            .type()
            .quantity()
            .itemName()
            .priceIsUnit()
            .provider()
            .reason()
            .costPrice()
            .salePrice()
            .paymentStatus()
            .destProject(project)
            .destSublocation()
            .sourceProject(project)
            .sourceSublocation()
            .build())
        .personnellog(db.personnellog.fieldsBuilder
            .name()
            .entrance()
            .exit()
            .minutes()
            .project(project)
            .sublocation()
            .build())
        .progresslog(db.progresslog.fieldsBuilder
            .progress()
            .entry(db.entry.fieldsBuilder.remoteId().build())
            .build())
        .templatelog(db.templatelog.fieldsBuilder
            .template(db.template.fieldsBuilder.remoteId().name().build())
            .templateName()
            .fieldsMetadata(db.customFieldMetadata.fieldsBuilder
                .remoteId()
                .project(project)
                .user(db.user.fieldsBuilder.remoteId().build())
                .value()
                .fieldName()
                .allowedValue(
                  db.customFieldAllowedValue.fieldsBuilder
                      .value()
                      .label()
                      .remoteId()
                      .parent(db.customFieldAllowedValue.fieldsBuilder.build())
                      .build(),
                )
                .customField(
                    db.customField.fieldsBuilder.remoteId().type().build())
                .build())
            .defaultProject(project)
            .build())
        .source(db.entrySource.fieldsBuilder.metadata().type().build())
        .metadata(db.entryMetadata.fieldsBuilder.value().type().build())
        .build();

    final feedPostFields =
        db.feedPost.fieldsBuilder.remoteId().readAt().build();

    final signatureFields = db.signature.fieldsBuilder
        .remoteId()
        .doodle()
        .ownerName()
        .ownerEmail()
        .comments()
        .status()
        .entry(db.entry.fieldsBuilder.remoteId().build())
        .build();

    final entryGroupEntryFields = db.entryGroupEntry.fieldsBuilder
        .remoteId()
        .entry(db.entry.fieldsBuilder.remoteId().build())
        .entryGroup(db.entryGroup.fieldsBuilder.remoteId().build())
        .createFrom(db.entry.fieldsBuilder.remoteId().build())
        .build();

    final entryGroupFields = db.entryGroup.fieldsBuilder
        .remoteId()
        .name()
        .entries(entryGroupEntryFields)
        .createdAt()
        .build();

    return db.outgoingMutation.fieldsBuilder
        .key()
        .mutationType()
        .failedAttempts()
        .organization(db.organization.fieldsBuilder.remoteId().build())
        .model(<OutgoingMutationModelType, Fields>{
      OutgoingMutationModelType.worklog: entryFields,
      OutgoingMutationModelType.inventorylog: entryFields,
      OutgoingMutationModelType.personnellog: entryFields,
      OutgoingMutationModelType.progresslog: entryFields,
      OutgoingMutationModelType.templatelog: entryFields,
      OutgoingMutationModelType.locationTracking: locationTrackingFields,
      OutgoingMutationModelType.feedPostRead: feedPostFields,
      OutgoingMutationModelType.signature: signatureFields,
      OutgoingMutationModelType.entryGroup: entryGroupFields,
      OutgoingMutationModelType.entryGroupEntry: entryGroupEntryFields,
    }).build();
  }
}
