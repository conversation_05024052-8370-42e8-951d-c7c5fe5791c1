import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata_fields_builder.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';

abstract class CustomFieldMetadataRepository<C extends RepositoryQueryContext,
        F extends CustomFieldMetadataFieldsBuilder>
    extends RepositoryTable<CustomFieldMetadata, C, F> {
  Future<List<CustomFieldMetadata>> findAll(C context, LocalId templatelogId);

  Future<List<CustomFieldMetadata>> findValuesByCustomFieldId(
    C context,
    LocalId customFieldId,
  );

  Future<void> saveAll(C context, Mutation<Templatelog> templatelogMutation);

  Future<void> deleteAll(C context, LocalId entryId);
}
