import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/resource_category/resource_category.dart';
import 'package:bitacora/domain/resource_category/value/resource_category_name.dart';
import 'package:bitacora/domain/resource_category/value/resource_category_order.dart';

class ResourceCategoryApiTranslator
    implements ModelTranslator<ResourceCategory> {
  const ResourceCategoryApiTranslator();

  @override
  ResourceCategory fromMap(Map<String, dynamic> data) {
    final organization =
        Organization(remoteId: RemoteId(data['organization_id']));
    return ResourceCategory(
      remoteId: RemoteId(data['id']),
      name: ResourceCategoryName(data['name']),
      order: ResourceCategoryOrder(data['order']),
      organization: organization,
      parent: data['parent_id'] == null
          ? null
          : ResourceCategory(
              remoteId: RemoteId(data['parent_id']),
              organization: organization,
            ),
    );
  }

  @override
  Map<String, dynamic> toMap(ResourceCategory model) {
    return {
      'id': model.remoteId?.apiValue,
      'name': model.name?.apiValue,
      'order': model.order?.apiValue,
      'parent': model.parent?.remoteId!.apiValue,
    };
  }
}
