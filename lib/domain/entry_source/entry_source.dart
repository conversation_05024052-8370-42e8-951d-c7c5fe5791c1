import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/value/entry_source_transcription.dart';
import 'package:bitacora/domain/entry_source/value/entry_source_metadata.dart';
import 'package:bitacora/domain/entry_source/value/entry_source_type.dart';

export 'package:bitacora/domain/entry/value/entry_source_transcription.dart';
export 'package:bitacora/domain/entry_source/value/entry_source_type.dart';
export 'package:bitacora/domain/entry_source/value/entry_source_metadata.dart';

class EntrySource extends Model {
  final EntrySourceType? type;
  final EntrySourceMetadata? metadata;
  final EntrySourceTranscription? transcription;
  final Entry? entry;
  final List<Attachment>? attachments;

  EntrySource({
    super.id,
    this.type,
    this.metadata,
    this.entry,
    this.attachments,
    this.transcription,
  }) : super(remoteId: null);

  @override
  Map<Field, dynamic> get fields => {
        EntrySourceField.type: type,
        EntrySourceField.metadata: metadata,
        EntrySourceField.entry: entry,
        EntrySourceField.attachment: attachments
      };

  EntrySource copyWith({
    LocalId? id,
    EntrySourceType? type,
    EntrySourceMetadata? metadata,
    EntrySourceTranscription? transcription,
    Entry? entry,
    List<Attachment>? attachments,
  }) {
    return EntrySource(
      id: id ?? this.id,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
      transcription: transcription ?? this.transcription,
      entry: entry ?? this.entry,
      attachments: attachments ?? this.attachments,
    );
  }
}

enum EntrySourceField with Field {
  id,
  remoteId,
  type,
  metadata,
  entry,
  attachment,
}

const entrySourceNestedModelFields = {EntrySourceField.entry};
