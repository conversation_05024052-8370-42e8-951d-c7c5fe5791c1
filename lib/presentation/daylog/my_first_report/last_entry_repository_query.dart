import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';

class LastEntryRepositoryQuery extends RepositoryQuery<Entry?> {
  const LastEntryRepositoryQuery();

  @override
  Future<Entry?> run(RepositoryQueryContext context) =>
      context.db.entry.findLast(context);

  @override
  Fields fields(Repository db) {
    return db.entry.fieldsBuilder.remoteId().day().build();
  }
}
