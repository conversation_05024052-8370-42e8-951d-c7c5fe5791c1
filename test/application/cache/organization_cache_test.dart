import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/application/cache/organization/organization_cache_repository_query.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/infrastructure/organization/organization_db_table.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../domain/common/mocks.dart';
import '../../domain/organization/mocks.dart';
import 'auth/mocks.dart';

void main() {
  final db = MockRepository();

  when(() => db.organization).thenReturn(OrganizationDbTable());

  final owner = mockUser();
  final organizationsFromDB = [
    mockOrganization(owner: owner),
    mockOrganization(owner: owner),
    mockOrganization(owner: owner),
    mockOrganization(owner: owner),
    mockOrganization(owner: owner),
  ];

  group('$OrganizationCache tests', () {
    test('Injects new', () {
      final activeSession = mockActiveSession();
      final db = _mockRepository();

      expect(
        OrganizationCache(activeSession, db) !=
            OrganizationCache(activeSession, db),
        true,
      );
    });

    test('Load organizations without session', () {
      final organizationCache = OrganizationCache(MockActiveSession(), db);

      organizationCache.load();

      expect(organizationCache.value, null);
    });

    test('Load organizations', () async {
      final session = Session(token: const SessionToken('aeiou'), user: owner);
      final activeSession = mockActiveSession(session: session);

      when(() => db.query(const OrganizationCacheRepositoryQuery()))
          .thenAnswer((_) => Future.value(organizationsFromDB));

      final organizationCache = OrganizationCache(activeSession, db);
      await organizationCache.load();

      expect(organizationCache.value!.length, organizationsFromDB.length);
    });
  });
}

Repository _mockRepository() {
  final db = MockRepository();
  final organizationRepository = mockOrganizationRepository();
  when(() => db.organization).thenReturn(organizationRepository);
  return db;
}
