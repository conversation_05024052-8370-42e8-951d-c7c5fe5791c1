import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/extension/extension_db_table.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../mocks.dart';

class MockExtensionSuggestionEngine extends Mock
    implements ExtensionSuggestionEngine {}

ExtensionSuggestionEngine mockExtensionSuggestionEngine({
  List<String> result = const <String>[],
}) {
  final mock = MockExtensionSuggestionEngine();
  when(() => mock.suggestion(any(), any(), any(), any(), any())).thenAnswer(
    (_) => Future.value(result),
  );
  return mock;
}

Future<void> testSuggestion(
  ExtensionDbTable dbTable,
  String field,
  Future<List<String>> Function(DbContext context) testFunction,
) async {
  final expectedResult = List.generate(2, (i) => '$i');
  final suggestionEngine =
      mockExtensionSuggestionEngine(result: expectedResult);
  final context = DbContext(db: MockDbRepository());

  final result = await withInjected<ExtensionSuggestionEngine>(
      suggestionEngine, () => testFunction(context));

  expect(result, expectedResult);
  verify(
    () => suggestionEngine.suggestion(
      any(),
      dbTable.tableName,
      dbTable.idColumn,
      dbTable.extensionType,
      field,
    ),
  ).called(1);
}
