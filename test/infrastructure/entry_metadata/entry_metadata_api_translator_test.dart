import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_api_translator.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late EntryMetadataApiTranslator translator;

  setUp(() {
    translator = const EntryMetadataApiTranslator();
  });

  group('EntryMetadataApiTranslator', () {
    test('fromMap should create EntryMetadata from type and value data', () {
      final data = {
        'type': 'sentiment',
        'value': 'Calmness',
      };
      final result = translator.fromMap(data);

      expect(result.type, equals(EntryMetadataType.sentiment));
      expect(result.value?.value, equals('Calmness'));
    });

    test(
        'fromMap should create EntryMetadata from type and value data for keyword',
        () {
      final data = {
        'type': 'keywords',
        'value': 'computadoras',
      };
      final result = translator.fromMap(data);

      expect(result.type, equals(EntryMetadataType.keyword));
      expect(result.value?.value, equals('computadoras'));
    });

    test('toMap should convert EntryMetadata to map with type and value fields',
        () {
      final metadata = EntryMetadata(
        type: EntryMetadataType.sentiment,
        value: EntryMetadataValue('Calmness'),
      );
      final result = translator.toMap(metadata);

      expect(result['type'], equals('sentiment'));
      expect(result['value'], equals('Calmness'));
    });
  });
}
