import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_timer.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/time_utils.dart';
import 'package:datetime_picker_formfield_new/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class EntryFormDateTimeOption extends StatefulWidget {
  final EntryFormDateTimeOptionController controller;
  final bool isOpenStateAllowed;

  const EntryFormDateTimeOption({
    super.key,
    required this.controller,
    this.isOpenStateAllowed = true,
  });

  @override
  State<EntryFormDateTimeOption> createState() =>
      _EntryFormDateTimeOptionState();
}

class _EntryFormDateTimeOptionState extends State<EntryFormDateTimeOption> {
  @override
  void initState() {
    super.initState();
    widget.controller.openStart.addListener(_updateActiveLogDayFromStartDate);
    widget.controller.startDate.addListener(_updateActiveLogDayFromStartDate);
  }

  @override
  void dispose() {
    widget.controller.openStart
        .removeListener(_updateActiveLogDayFromStartDate);
    widget.controller.startDate
        .removeListener(_updateActiveLogDayFromStartDate);
    super.dispose();
  }

  void _updateActiveLogDayFromStartDate() {
    final text = (widget.controller.isScheduleEntry.value
            ? widget.controller.openStart
            : widget.controller.startDate)
        .text;

    if (text.isEmpty) {
      return;
    }

    final logDay =
        LogDay(getLogDayFromDateTime(kLogDayDisplayDateFormat.parse(text)));
    context.read<ActiveLogDay>().set(logDay);
  }

  @override
  Widget build(BuildContext context) {
    // FIXME: consider using showDateRangePicker (material only!)
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: kFormVerticalSpacing,
      ).copyWith(top: kFormVerticalSpacing / 2),
      child: ValueListenableBuilder<Entry?>(
          valueListenable: widget.controller.liveEntry,
          builder: (context, entry, __) {
            if (widget.controller.liveEntry.value?.hasStartedTimer ?? false) {
              return EntryFormTimer(entry: widget.controller.liveEntry.value!);
            }

            return ValueListenableBuilder<bool>(
                valueListenable: widget.controller.isScheduleEntry,
                builder: (context, isSchedule, _) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                            bottom: kFormVerticalSpacing / 2),
                        child: widget.isOpenStateAllowed
                            ? Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Text(
                                    AppLocalizations.of(context)!.scheduleEntry,
                                    style:
                                        Theme.of(context).textTheme.bodySmall,
                                  ),
                                  const SizedBox(width: 4.0),
                                  Switch(
                                    materialTapTargetSize:
                                        MaterialTapTargetSize.shrinkWrap,
                                    value: isSchedule,
                                    onChanged: (bool value) {
                                      widget.controller.isScheduleEntry.value =
                                          value;
                                    },
                                  ),
                                  const SizedBox(width: 4.0),
                                ],
                              )
                            : const SizedBox(),
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: kFormHorizontalSpacing,
                        ),
                        child: AnimatedSwitcher(
                          duration: const Duration(milliseconds: 200),
                          child: isSchedule
                              ? _EntryFormOpenState(
                                  controller: widget.controller)
                              : _EntryFormDateTimeRange(
                                  controller: widget.controller),
                        ),
                      )
                    ],
                  );
                });
          }),
    );
  }
}

class _EntryFormDateTimeRange extends StatelessWidget {
  final EntryFormDateTimeOptionController controller;

  const _EntryFormDateTimeRange({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (_hasFullData()) ...[
          Row(
            children: [
              Text(
                '${AppLocalizations.of(context)!.elapsed} | ',
                style: Theme.of(context).textTheme.bodySmall,
              ),
              Flexible(
                child: Text(
                  _getElapsed(context),
                  style: Theme.of(context)
                      .textTheme
                      .bodySmall!
                      .copyWith(fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10.0),
        ],
        ValueListenableBuilder<double>(
          valueListenable: getFormFieldHeight(context),
          builder: (_, value, child) => SizedBox(
            height: value,
            child: child,
          ),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                child: DateTimeField(
                  controller: controller.startDate,
                  format: kLogDayDisplayDateFormat,
                  resetIcon: const Icon(
                    Icons.close,
                    size: 16,
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.next,
                  onShowPicker: pickDate,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.startDate,
                    contentPadding: getContentPaddingWithSuffix(),
                    errorStyle: const TextStyle(height: 0, fontSize: 0),
                  ),
                ),
              ),
              const SizedBox(width: kFormHorizontalSpacing),
              Flexible(
                flex: 2,
                child: DateTimeField(
                  controller: controller.startTime,
                  format: kTimeOfDayDateFormat,
                  resetIcon: const Icon(
                    Icons.close,
                    size: 16,
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.next,
                  onShowPicker: pickTime,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.startTime,
                    contentPadding: getContentPaddingWithSuffix(),
                  ),
                ),
              )
            ],
          ),
        ),
        const SizedBox(height: kFormVerticalSpacing),
        ValueListenableBuilder<double>(
          valueListenable: getFormFieldHeight(context),
          builder: (_, value, child) => SizedBox(
            height: value,
            child: child,
          ),
          child: Row(
            children: [
              Flexible(
                flex: 3,
                child: DateTimeField(
                  controller: controller.endDate,
                  format: kLogDayDisplayDateFormat,
                  resetIcon: const Icon(
                    Icons.close,
                    size: 16,
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.next,
                  onShowPicker: pickDate,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.endDate,
                    contentPadding: getContentPaddingWithSuffix(),
                    errorStyle: const TextStyle(height: 0, fontSize: 0),
                  ),
                ),
              ),
              const SizedBox(width: kFormHorizontalSpacing),
              Flexible(
                flex: 2,
                child: DateTimeField(
                  controller: controller.endTime,
                  format: kTimeOfDayDateFormat,
                  resetIcon: const Icon(
                    Icons.close,
                    size: 16,
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.next,
                  onShowPicker: pickTime,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.endTime,
                    contentPadding: getContentPaddingWithSuffix(),
                  ),
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  bool _hasFullData() {
    return controller.liveEntry.value?.startDate?.value != null &&
        controller.liveEntry.value?.startDate?.value != null &&
        controller.liveEntry.value?.startTime?.value != null &&
        controller.liveEntry.value?.endTime?.value != null;
  }

  String _getElapsed(BuildContext context) {
    final startDate = getDateTimeFromLogDay(LogDay(
      controller.liveEntry.value!.startDate!.value!,
    ));
    final endDate = getDateTimeFromLogDay(LogDay(
      controller.liveEntry.value!.endDate!.value!,
    ));
    final startTime = controller.liveEntry.value!.startTime!.toDateTime();
    final endTime = controller.liveEntry.value!.endTime!.toDateTime();

    final start = startDate.add(Duration(
        hours: startTime.hour,
        minutes: startTime.minute,
        seconds: startTime.second));
    final end = endDate.add(Duration(
        hours: endTime.hour, minutes: endTime.minute, seconds: endTime.second));

    final elapsedDuration = end.difference(start);
    return displayDuration(elapsedDuration);
  }
}

class _EntryFormOpenState extends StatelessWidget {
  final EntryFormDateTimeOptionController controller;

  const _EntryFormOpenState({required this.controller});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        ValueListenableBuilder<double>(
          valueListenable: getFormFieldHeight(context),
          builder: (_, value, child) => SizedBox(
            height: value,
            child: child,
          ),
          child: Row(
            children: [
              Flexible(
                flex: 1,
                child: Consumer<ActiveLogDay>(
                  builder: (context, value, child) {
                    if (controller.openStart.text.isNotEmpty) {
                      final formatted = kLogDayDisplayDateFormat
                          .format(getDateTimeFromLogDay(value.value!));
                      if (formatted != controller.openStart.text) {
                        SchedulerBinding.instance.addPostFrameCallback((_) {
                          controller.openStart.text = formatted;
                        });
                      }
                    }
                    return child!;
                  },
                  child: DateTimeField(
                    controller: controller.openStart,
                    format: kLogDayDisplayDateFormat,
                    resetIcon: const Icon(
                      Icons.close,
                      size: 18,
                    ),
                    textCapitalization: TextCapitalization.sentences,
                    keyboardType: TextInputType.text,
                    textInputAction: TextInputAction.next,
                    onShowPicker: pickDate,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.startDate,
                      contentPadding: getContentPaddingWithSuffix(),
                      errorStyle: const TextStyle(height: 0, fontSize: 0),
                    ),
                    validator: getRequiredFieldValidator<DateTime>(),
                  ),
                ),
              ),
              const SizedBox(width: kFormHorizontalSpacing),
              Flexible(
                flex: 1,
                child: DateTimeField(
                  controller: controller.openEnd,
                  format: kLogDayDisplayDateFormat,
                  resetIcon: const Icon(
                    Icons.close,
                    size: 18,
                  ),
                  textCapitalization: TextCapitalization.sentences,
                  keyboardType: TextInputType.text,
                  textInputAction: TextInputAction.next,
                  onShowPicker: pickDate,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.endDate,
                    contentPadding: getContentPaddingWithSuffix(),
                  ),
                ),
              )
            ],
          ),
        ),
        const SizedBox(height: kFormVerticalSpacing),
        ValueListenableBuilder<double>(
          valueListenable: getFormFieldHeight(context),
          builder: (_, value, child) => SizedBox(
            height: value,
            child: child,
          ),
          child: DropdownButtonFormField<bool>(
            iconSize: 0,
            decoration:
                InputDecoration(labelText: AppLocalizations.of(context)!.type),
            value: controller.isProgressive.value,
            items: [false, true]
                .map((e) => DropdownMenuItem<bool>(
                    value: e,
                    child: Text(
                      e
                          ? AppLocalizations.of(context)!.progressive
                          : AppLocalizations.of(context)!.complete,
                      style: TextStyle(
                          color: Theme.of(context).colorScheme.primary),
                    )))
                .toList(growable: false),
            onChanged: (value) => controller.isProgressive.value = value!,
          ),
        ),
      ],
    );
  }
}
