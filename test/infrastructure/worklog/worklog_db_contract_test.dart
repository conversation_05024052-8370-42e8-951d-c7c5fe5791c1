import 'package:bitacora/infrastructure/worklog/worklog_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$WorklogDbContract tests', () {
    test('Create $WorklogDbContract', () {
      expectRemovingSpaces(
        const WorklogDbContract().create,
        '''
        CREATE TABLE worklog (
          w_id INTEGER PRIMARY KEY AUTOINCREMENT,
          w_remoteId INTEGER UNIQUE,
          w_projectId INTEGER NOT NULL,
          w_sublocation TEXT,
          w_type INTEGER NOT NULL,
          w_quantity INTEGER,
          w_title TEXT NOT NULL,
          w_costPrice INTEGER,
          w_salePrice INTEGER,
          w_provider TEXT,
          w_paymentStatus INTEGER,
          w_priceIsUnit INTEGER
        )
        ''',
      );
    });
  });
}
