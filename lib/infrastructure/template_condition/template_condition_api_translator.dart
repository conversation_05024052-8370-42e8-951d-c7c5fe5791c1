import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/template_block/template_block.dart';
import 'package:bitacora/domain/template_condition/template_condition.dart';
import 'package:bitacora/domain/template_condition/value/template_condition_operator.dart';
import 'package:bitacora/domain/template_condition/value/template_condition_value.dart';
import 'package:bitacora/domain/template_group/template_group.dart';

const kTemplateConditionParentBlockIdKey = 'template_block_id';
const kTemplateConditionParentGroupIdKey = 'template_group_id';

class TemplateConditionApiTranslator
    implements ModelTranslator<TemplateCondition> {
  const TemplateConditionApiTranslator();

  @override
  TemplateCondition fromMap(Map<String, dynamic> data) {
    return TemplateCondition(
      remoteId: RemoteId(data['id']),
      value: TemplateConditionValue.fromApi(data['value']),
      operator: TemplateConditionOperator(
          TemplateConditionOperatorValue.fromApi(data['operator'])),
      whenBlock: TemplateBlock(remoteId: RemoteId(data['when_id'])),
      templateBlock: data[kTemplateConditionParentBlockIdKey] == null
          ? null
          : TemplateBlock(
              remoteId: RemoteId(data[kTemplateConditionParentBlockIdKey])),
      templateGroup: data[kTemplateConditionParentGroupIdKey] == null
          ? null
          : TemplateGroup(
              remoteId: RemoteId(data[kTemplateConditionParentGroupIdKey])),
    );
  }

  @override
  Map<String, dynamic> toMap(TemplateCondition model) {
    throw UnimplementedError();
  }
}
