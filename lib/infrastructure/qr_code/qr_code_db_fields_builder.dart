import 'dart:convert';

import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/qr_code/qr_code.dart';
import 'package:bitacora/domain/qr_code/qr_code_fields_builder.dart';
import 'package:bitacora/domain/qr_code/value/qr_code_description.dart';
import 'package:bitacora/domain/qr_code/value/qr_code_name.dart';
import 'package:bitacora/domain/qr_code/value/qr_code_payload.dart';
import 'package:bitacora/domain/qr_code/value/qr_code_updated_at.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/qr_code/qr_code_db_contract.dart';

class QrCodeDbFieldsBuilder extends DbFieldsBuilder
    implements QrCodeFieldsBuilder {
  QrCodeDbFieldsBuilder() {
    _id();
  }

  QrCodeDbContract get contract => const QrCodeDbContract();

  QrCodeDbFieldsBuilder _id() {
    addField(
      QrCodeField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  QrCodeDbFieldsBuilder remoteId() {
    addField(
      QrCodeField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  QrCodeDbFieldsBuilder name() {
    addField(
      QrCodeField.name,
      DbField(
        column: contract.name,
        valueBuilder: (v) => QrCodeName(v),
      ),
    );
    return this;
  }

  @override
  QrCodeDbFieldsBuilder description() {
    addField(
      QrCodeField.description,
      DbField(
        column: contract.description,
        valueBuilder: (v) => QrCodeDescription(v),
      ),
    );
    return this;
  }

  @override
  QrCodeDbFieldsBuilder payload() {
    addField(
      QrCodeField.payload,
      DbField(
        column: contract.payload,
        valueBuilder: (v) => QrCodePayload(jsonDecode(v)),
      ),
    );
    return this;
  }

  @override
  QrCodeFieldsBuilder updatedAt() {
    addField(
      QrCodeField.updatedAt,
      DbField(
        column: contract.updatedAt,
        valueBuilder: (v) =>
            QrCodeUpdatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  QrCodeDbFieldsBuilder author(Fields nestedFields) {
    addField(
      QrCodeField.author,
      DbField(
        key: QrCodeField.author,
        column: contract.authorId,
        nestedFields: nestedFields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.user.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  QrCodeDbFieldsBuilder organization(Fields nestedFields) {
    addField(
      QrCodeField.organization,
      DbField(
        key: QrCodeField.organization,
        column: contract.organizationId,
        nestedFields: nestedFields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.organization.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
