import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';

enum WorklogTypeValue {
  offered,
  received,
}

class WorklogType extends ValueObject<WorklogTypeValue> {
  static const WorklogType offered = WorklogType(WorklogTypeValue.offered);
  static const WorklogType received = WorklogType(WorklogTypeValue.received);
  static const List<WorklogType> values = [offered, received];

  const WorklogType(super.value);

  factory WorklogType.fromDbValue(int dbValue) => values[dbValue - 1];

  @override
  bool isSameType(Object other) {
    return other is WorklogType;
  }

  @override
  int get dbValue {
    switch (value) {
      case WorklogTypeValue.offered:
        return 1;
      case WorklogTypeValue.received:
        return 2;
      }
  }

  @override
  int get apiValue => dbValue;

  String get localizedDisplayValue {
    switch (value) {
      case WorklogTypeValue.offered:
        return AppLocalizationsResolver.get().offered;
      case WorklogTypeValue.received:
        return AppLocalizationsResolver.get().received;
      }
  }
}
