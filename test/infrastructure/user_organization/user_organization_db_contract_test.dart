import 'package:bitacora/infrastructure/user_organization/user_organization_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$UserOrganizationDbContract tests', () {
    test('Create $UserOrganizationDbContract', () {
      expectRemovingSpaces(
        const UserOrganizationDbContract().create,
        '''
        CREATE TABLE userOrganization (
          uo_userId INTEGER NOT NULL,
          uo_organizationId INTEGER NOT NULL,
          PRIMARY KEY (uo_userId, uo_organizationId)
        )
        ''',
      );
    });
  });
}
