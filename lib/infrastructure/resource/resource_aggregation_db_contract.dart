import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class ResourceAggregationDbContract extends DbContract {
  static const String _ = 'ra_';
  static const String _tableName = 'resourceAggregation';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String resourceId = '${_}resourceId';
  final String entityId = '${_}entityId';
  final String entityType = '${_}entityType';

  const ResourceAggregationDbContract() : super(_, _tableName);

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $resourceId INTEGER NOT NULL,
    $entityId TEXT NOT NULL,
    $entityType INTEGER NOT NULL
  )
  ''';

  @override
  int get initialDbVersion => kDbVersionWithResourceTables;
}
