import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/organization/organization.dart';

class FindAccessPermissionRepositoryQuery extends RepositoryQuery<Access?> {
  final Organization organization;
  final int permission;
  final OnValidateAccess? validator;

  FindAccessPermissionRepositoryQuery({
    required this.organization,
    required this.permission,
    this.validator,
  });

  @override
  Future<Access?> run(RepositoryQueryContext context) =>
      context.db.access.findPermission(
        context,
        organization,
        permission,
        validator: validator,
      );

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.access.fieldsBuilder.rules().build();
}
