import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/presentation/widgets/circle_check.dart';
import 'package:bitacora/presentation/widgets/menu_list.dart';
import 'package:bitacora/presentation/widgets/menu_list_item.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class ChooseOrganizationsPage extends StatefulWidget {
  const ChooseOrganizationsPage({super.key});

  @override
  State<ChooseOrganizationsPage> createState() =>
      _ChooseOrganizationsPageState();
}

class _ChooseOrganizationsPageState extends State<ChooseOrganizationsPage> {
  LocalId? _selectedOrgId;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.welcome),
      ),
      body: SafeArea(
        child: Consumer<OrganizationCache>(
          builder: (_, organizationCache, __) => MenuList(
            title: AppLocalizations.of(context)!.chooseYourOrg,
            itemCount: organizationCache.value?.length ?? 0,
            itemBuilder: (BuildContext context, int index) {
              final organization = organizationCache.value![index];
              final isSelected = _selectedOrgId == organization.id;

              return MenuListItem(
                title: organization.name!.displayValue,
                isSelected: isSelected,
                selectionColor: organization.color!.value,
                onTap: () {
                  setState(() {
                    _selectedOrgId = organization.id;
                  });
                  final activeOrg = context.read<ActiveOrganization>();
                  Future.delayed(
                    kCircleCheckAnimationDuration +
                        const Duration(milliseconds: 100),
                    () => activeOrg.set(organization),
                  );
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
