import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';

class DaylogScrollCoordinator extends ChangeNotifier {
  double _bottomHeight = 0;

  DaylogScrollCoordinator();

  double get bottomHeight => _bottomHeight;

  void onBottomBuild(double initialHeight) {
    _bottomHeight = initialHeight;
    SchedulerBinding.instance.addPostFrameCallback((_) => notifyListeners());
  }

  void onBottomDragged(double position, double minHeight, double maxHeight) {
    _bottomHeight = minHeight + position * (maxHeight - minHeight);
    notifyListeners();
  }
}
