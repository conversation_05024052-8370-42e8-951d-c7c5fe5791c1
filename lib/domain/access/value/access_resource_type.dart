import 'package:bitacora/domain/common/value_object/value_object.dart';

enum AccessResourceTypeValue {
  organization,
  project,
  tag,
  entry,
  organizationEntries, // FIXME: deprecated?
}

class AccessResourceType extends ValueObject<AccessResourceTypeValue> {
  static const AccessResourceType organization =
      AccessResourceType(AccessResourceTypeValue.organization);
  static const AccessResourceType project =
      AccessResourceType(AccessResourceTypeValue.project);
  static const AccessResourceType tag =
      AccessResourceType(AccessResourceTypeValue.tag);
  static const AccessResourceType entry =
      AccessResourceType(AccessResourceTypeValue.entry);
  static const AccessResourceType organizationEntries =
      AccessResourceType(AccessResourceTypeValue.organizationEntries);
  static const List<AccessResourceType> values = [
    organization,
    project,
    tag,
    entry,
    organizationEntries,
  ];

  const AccessResourceType(super.value);

  factory AccessResourceType.fromDbValue(int dbValue) => values[dbValue - 1];

  @override
  int get dbValue {
    switch (value) {
      case AccessResourceTypeValue.organization:
        return 1;
      case AccessResourceTypeValue.project:
        return 2;
      case AccessResourceTypeValue.tag:
        return 3;
      case AccessResourceTypeValue.entry:
        return 4;
      case AccessResourceTypeValue.organizationEntries:
        return 5;
      }
  }

  @override
  int get apiValue => dbValue;
}
