import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/domain/resource/value/resource_transfer_state.dart';
import 'package:bitacora/domain/resource/value/resource_type.dart';
import 'package:bitacora/presentation/widgets/loading_indicator.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/file_type/file_type.dart';
import 'package:bitacora/util/open_file/open_file_util.dart';
import 'package:bitacora/util/readable_file_size/readable_file_size.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:bitacora/util/url_launcher.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:photo_view/photo_view.dart';
import 'package:url_launcher/url_launcher_string.dart';

const _kNameEdgeInsets = EdgeInsets.all(32.0);

typedef DownloadResourceCallback = void Function(BuildContext, Resource);

class ResourceGalleryPageItem extends StatelessWidget {
  final LiveModel<Resource> liveResource;
  final DownloadResourceCallback onDownloadResource;

  const ResourceGalleryPageItem({
    super.key,
    required this.liveResource,
    required this.onDownloadResource,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder(
      valueListenable: liveResource,
      builder: (BuildContext context, Resource? value, Widget? child) =>
          value == null
              ? const SizedBox()
              : _itemContentFromResource(context, value),
    );
  }

  Widget _itemContentFromResource(BuildContext context, Resource resource) {
    if (!resource.isDownloaded!.value && resource.type != ResourceType.url) {
      return _undownloadedItem(context, resource);
    }

    return FutureBuilder<String?>(
      future: StorageUtils()
          .getAbsolutePath(StorageSubdirectory.resources, resource.path!),
      builder: (context, snapshot) {
        final path = snapshot.data;
        if (path == null) {
          return const SizedBox();
        }

        if (resource.type == ResourceType.url) {
          return _urlItem(context, resource);
        }

        if (FileType.fromPath(path) == FileType.image) {
          return _downloadedImageItem(context, path);
        }

        return _downloadedNonImageItem(context, resource, path);
      },
    );
  }

  Widget _undownloadedItem(BuildContext context, Resource resource) {
    final isInProgress =
        resource.transferState!.value == ResourceTransferStateValue.inProgress;

    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.cloud_outlined),
          Padding(
            padding: _kNameEdgeInsets,
            child: Text(
              resource.name!.displayValue,
              textAlign: TextAlign.center,
            ),
          ),
          if (isInProgress)
            const Padding(
              padding: _kNameEdgeInsets,
              child: LoadingIndicator(),
            ),
          if (!isInProgress)
            TextButton(
              onPressed: () => onDownloadResource(context, resource),
              child: Text(AppLocalizations.of(context)!.download),
            ),
        ],
      ),
    );
  }

  Widget _downloadedNonImageItem(
    BuildContext context,
    Resource resource,
    String absolutePath,
  ) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.file_present),
          Padding(
            padding: _kNameEdgeInsets,
            child: Text(
              resource.name!.displayValue,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ),
          FutureBuilder<int?>(
            future: FileSystemInjector.get().file(absolutePath).length(),
            builder: (context, snapshot) {
              return snapshot.hasData
                  ? Text(readableFileSize(snapshot.data!))
                  : const SizedBox();
            },
          ),
          TextButton(
            onPressed: () async {
              final absolutePath = (await StorageUtils().getAbsolutePath(
                  StorageSubdirectory.resources, resource.path!))!;
              await OpenFileUtil().open(absolutePath);
            },
            child: Text(AppLocalizations.of(context)!.open),
          ),
        ],
      ),
    );
  }

  Widget _downloadedImageItem(
    BuildContext context,
    String absolutePath,
  ) {
    return PhotoViewGestureDetectorScope(
      axis: Axis.horizontal,
      child: PhotoView(
        backgroundDecoration:
            BoxDecoration(color: Theme.of(context).canvasColor),
        imageProvider: FileImage(FileSystemInjector.get().file(absolutePath)),
      ),
    );
  }

  Widget _urlItem(
    BuildContext context,
    Resource resource,
  ) {
    return Center(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Icon(Icons.link),
          Padding(
            padding: _kNameEdgeInsets,
            child: Text(
              resource.name!.displayValue,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
          ),
          Text(
            resource.path!.displayValue,
            style: Theme.of(context).textTheme.bodySmall,
            textAlign: TextAlign.center,
          ),
          TextButton(
            onPressed: () async {
              final url = resource.path!.value!;
              final urlLauncher = UrlLauncher();
              if (await urlLauncher.canLaunch(url)) {
                await urlLauncher.launch(url,
                    mode: LaunchMode.externalApplication);
              }
            },
            child: Text(AppLocalizations.of(context)!.open),
          ),
        ],
      ),
    );
  }
}
