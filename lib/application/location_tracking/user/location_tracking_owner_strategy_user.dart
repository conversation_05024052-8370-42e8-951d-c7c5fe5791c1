import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/location_tracking/location_tracking_owner_strategy.dart';
import 'package:bitacora/application/location_tracking/location_tracking_owner_strategy_context_snapshot.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_owner_type.dart';

class LocationTrackingOwnerStrategyUser
    implements LocationTrackingOwnerStrategy {
  LocationTrackingOwnerStrategyUser();

  @override
  LocationTrackingOwnerType get ownerType => LocationTrackingOwnerType.user;

  @override
  Future<LocalId?> create(
    LocationTrackingOwnerStrategyContextSnapshot contextSnapshot,
    LocationTracking locationTracking,
  ) async {
    final db = contextSnapshot.read<Repository>();
    final user = contextSnapshot.read<ActiveSession>().value!.user;
    return db.transaction((context) async {
      final localId = await db.locationTracking.save(context, locationTracking);
      await db.locationTracking.saveUserRelation(
        context,
        user.id!,
        contextSnapshot.read<ActiveOrganization>().value!.id!,
        localId!,
      );
      return localId;
    });
  }

  @override
  Future<void> preStop(
    Repository db,
    RepositoryQueryContext context,
    LocalId locationTrackingId,
  ) async {}
}
