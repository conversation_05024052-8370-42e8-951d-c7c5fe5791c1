import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_form_header_option.dart';
import 'package:bitacora/presentation/entry_form/util/header_option.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import 'mocks.dart';

void main() {
  group('$InventorylogFormHeaderOption tests', () {
    test('Is Editable by default', () {
      final headerOption = InventorylogFormHeaderOption(
        controller: MockInventorylogFormController(),
      );

      expect(headerOption.isEditable, true);
    });

    testWidgets('Shows HeaderOption', (tester) async {
      final controller = _mockInventorylogFormController();
      await tester.pumpWidget(_buildTestWidget(controller: controller));

      await tester.pumpAndSettle();

      expect(
        find.byWidgetPredicate((widget) {
          if (widget is! HeaderOption<InventorylogType>) {
            return false;
          }
          return widget.isEditable &&
              widget.options == InventorylogType.values &&
              widget.valueNotifier == controller.inventorylogType;
        }),
        findsOneWidget,
      );
    });

    testWidgets('Non-Editable', (tester) async {
      await tester.pumpWidget(_buildTestWidget(isEditable: false));

      await tester.pumpAndSettle();

      expect(
        find.byWidgetPredicate((widget) {
          if (widget is! HeaderOption<InventorylogType>) {
            return false;
          }
          return !widget.isEditable;
        }),
        findsOneWidget,
      );
    });

    testWidgets('Notifies controller', (tester) async {
      final controller =
          _mockInventorylogFormController(InventorylogType.incoming);
      await tester.pumpWidget(_buildTestWidget(controller: controller));
      await tester.pump();
      await tester.tap(find.text('Incoming'));
      await tester.pumpAndSettle();

      await tester.tap(find.text('Movement'));
      await tester.pumpAndSettle();

      expect(controller.inventorylogType.value, InventorylogType.movement);
    });
  });
}

Widget _buildTestWidget({
  InventorylogFormController? controller,
  bool? isEditable,
}) {
  return MaterialApp(
    theme: buildLightThemeData(bitacoraGreen),
    home: Localizations(
      locale: const Locale('en'),
      delegates: AppLocalizations.localizationsDelegates,
      child: Scaffold(
        body: InventorylogFormHeaderOption(
          isEditable: isEditable ?? true,
          controller: controller ?? _mockInventorylogFormController(),
        ),
      ),
    ),
  );
}

InventorylogFormController _mockInventorylogFormController(
    [InventorylogType? type]) {
  final controller = MockInventorylogFormController();
  when(() => controller.inventorylogType)
      .thenReturn(ValueNotifier(type ?? InventorylogType.incoming));
  return controller;
}
