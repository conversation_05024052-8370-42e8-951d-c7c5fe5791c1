import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/util/limit_offset_cursor.dart';

class DaylogAppBarFeedShowIconRepositoryQuery extends RepositoryQuery<bool> {
  const DaylogAppBarFeedShowIconRepositoryQuery();

  @override
  Future<bool> run(RepositoryQueryContext context) async {
    final dbContext = context.copyWith(cursor: const LimitOffsetCursor(1, 0));
    final posts = await context.db.feedPost.findAll(dbContext.copyWith(
      fields: context.db.feedPost.fieldsBuilder.build(),
    ));
    if (posts.isNotEmpty) {
      return true;
    }

    final resources = await context.db.resource.findAll(dbContext.copyWith(
      fields: context.db.resource.fieldsBuilder.build(),
    ));

    if (resources.isNotEmpty) {
      return true;
    }

    return false;
  }
}
