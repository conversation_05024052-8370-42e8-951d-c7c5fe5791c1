import 'package:bitacora/application/cache/template/template_cache.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:mocktail/mocktail.dart';

class MockTemplateCache extends Mock implements TemplateCache {}

TemplateCache mockTemplateCache({
  List<List<Template>?>? templatesList,
  List<Template>? templates,
  bool hasLoaded = true,
  bool isLoading = false,
}) {
  final mock = MockTemplateCache();
  when(() => mock.value).thenAnswer((_) =>
      templates ??
      (templatesList != null
          ? (templatesList.isEmpty ? null : templatesList.removeAt(0))
          : <Template>[]));
  when(() => mock.hasLoaded).thenReturn(hasLoaded);
  when(() => mock.isLoading).thenReturn(isLoading);
  when(() => mock.load()).thenAnswer((_) => Future.value());
  return mock;
}
