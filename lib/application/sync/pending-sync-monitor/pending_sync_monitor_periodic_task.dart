import 'dart:async';

import 'package:bitacora/application/sync/background/background_sync_future_scheduler.dart';
import 'package:bitacora/application/sync/sync_utils.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/util/background_work/background_provider.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/workmanager/workmanager_runnable.dart';
import 'package:bitacora/util/workmanager/workmanager_utils.dart';

class PendingSyncMonitorPeriodicTask implements WorkmanagerRunnable {
  factory PendingSyncMonitorPeriodicTask() =>
      inject(() => const PendingSyncMonitorPeriodicTask._());

  const PendingSyncMonitorPeriodicTask._();

  @override
  Future<bool> run(BackgroundContext context) async {
    logger.i('pending-sync-monitor Starting');
    final session = await context.read<Future<Session?>>();

    if (session == null || !session.isValid) {
      logger.i('pending-sync-monitor Ending. Invalid session.');
      return true;
    }

    if (await SyncUtils().hasPendingSync(context.read<Repository>())) {
      logger.i('pending-sync-monitor Register background sync task');
      // FIXME: iOS, Register only if not registered
      await WorkmanagerUtils().registerOneOffTask(
        WorkmanagerTask.backgroundSync,
        initialDelay: kBackgroundSyncDelay,
      );
    }

    return true;
  }
}
