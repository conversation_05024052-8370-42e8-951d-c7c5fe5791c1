import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/access_entry/access_entry_unset_permission_cache_db_query.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';

class AccessFixUnsetEntryCache {
  static final _instance = AccessFixUnsetEntryCache._();
  final List<(LogDay, LogDay)> _priorityRanges = [];
  bool _isBusy = false;

  AccessFixUnsetEntryCache._();

  factory AccessFixUnsetEntryCache() => inject(() => _instance);

  (LogDay, LogDay)? get _priorityRange => _priorityRanges.firstOrNull;

  void check(DbContext context) async {
    if (_isBusy) {
      return;
    }

    bool didFixEntriesInRange = false;
    _isBusy = true;

    int runCount = 1;
    while (true) {
      final currentRange = _priorityRange;
      logger.i(
        'db:access fix unset '
        '${currentRange == null ? '' : '[${currentRange.$1.value} - ${currentRange.$2.value}]'} '
        'access entry cache $runCount',
      );

      List<Entry>? entries = await _getEntries(context, currentRange);

      if (currentRange != null) {
        if (entries.isEmpty) {
          if (didFixEntriesInRange) {
            didFixEntriesInRange = false;
          }

          _discardCurrentPriorityRange(currentRange);

          continue;
        } else {
          didFixEntriesInRange = true;
        }
      }

      if (entries.isEmpty) {
        break;
      }

      await context.db.entry.setAccessCache(context, entries.map((e) => e.id!));

      if (currentRange != null) {
        _sendEntryMutation(context);
      }
      runCount++;
    }

    _isBusy = false;
  }

  Future<List<Entry>> _getEntries(
    DbContext context,
    (LogDay, LogDay)? range,
  ) async {
    return context.db.query(
      AccessEntryUnsetPermissionCacheDbQuery(
        minDay: range?.$1,
        maxDay: range?.$2,
      ),
    );
  }

  void _sendEntryMutation(DbContext context) {
    context.db.entry.markDirty();
  }

  void addPriorityDayRange({
    required LogDay minDay,
    required LogDay maxDay,
  }) async {
    if (_priorityRanges.length >= 5) {
      _priorityRanges.removeLast();
    }

    _priorityRanges.insert(0, (minDay, maxDay));
  }

  void _discardCurrentPriorityRange((LogDay, LogDay) range) {
    _priorityRanges.removeWhere((e) => e.$1 == range.$1 && e.$2 == range.$2);
  }
}
