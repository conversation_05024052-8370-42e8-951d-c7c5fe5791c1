import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value_fields_builder.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_label.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_value.dart';
import 'package:bitacora/infrastructure/custom_field_allowed_value/custom_field_allowed_value_db_contract.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';

class CustomFieldAllowedValueDbFieldsBuilder extends DbFieldsBuilder
    implements CustomFieldAllowedValueFieldsBuilder {
  CustomFieldAllowedValueDbFieldsBuilder() {
    _id();
  }

  CustomFieldAllowedValueDbContract get contract =>
      const CustomFieldAllowedValueDbContract();

  CustomFieldAllowedValueDbFieldsBuilder _id() {
    addField(
      CustomFieldAllowedValueField.id,
      DbField(
        key: CustomFieldAllowedValueField.id,
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldAllowedValueDbFieldsBuilder remoteId() {
    addField(
      CustomFieldAllowedValueField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldAllowedValueDbFieldsBuilder label() {
    addField(
      CustomFieldAllowedValueField.label,
      DbField(
        key: CustomFieldAllowedValueField.label,
        column: contract.label,
        valueBuilder: (v) => CustomFieldAllowedValueLabel(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldAllowedValueDbFieldsBuilder value() {
    addField(
      CustomFieldAllowedValueField.value,
      DbField(
        key: CustomFieldAllowedValueField.value,
        column: contract.value,
        valueBuilder: (v) => CustomFieldAllowedValueValue(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldAllowedValueDbFieldsBuilder parent(Fields fields) {
    addField(
      CustomFieldAllowedValueField.parent,
      DbField(
          key: CustomFieldAllowedValueField.parent,
          column: contract.parentId,
          nestedFields: fields as DbFields,
          nestedBuilder: (nestedContext, value) => nestedContext
              .db.customFieldAllowedValue
              .find(nestedContext, LocalId(value))),
    );
    return this;
  }
}
