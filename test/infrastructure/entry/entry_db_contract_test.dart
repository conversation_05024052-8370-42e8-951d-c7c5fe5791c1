import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$EntryDbContract tests', () {
    test('Create $EntryDbContract', () {
      expectRemovingSpaces(
        const EntryDbContract().create,
        '''
        CREATE TABLE entry (
          e_id INTEGER PRIMARY KEY AUTOINCREMENT,
          e_remoteId INTEGER UNIQUE,
          e_authorId INTEGER,
          e_assigneeId INTEGER,
          e_day INTEGER NOT NULL,
          e_time INTEGER NOT NULL,
          e_start_date INTEGER,
          e_end_date INTEGER,
          e_start_time INTEGER,
          e_end_time INTEGER,
          e_comments TEXT,
          e_loc_longitude REAL,
          e_loc_latitude REAL,
          e_extensionId INTEGER,
          e_extensionType INTEGER NOT NULL,
          e_openStateId INTEGER,
          e_locationTrackingId INTEGER,
          e_createdAt INTEGER NOT NULL,
          e_updatedAt INTEGER NOT NULL,
          e_syncVersion INTEGER NOT NULL,
          e_timerStatus INTEGER
        )
        ''',
      );
    });
  });
}
