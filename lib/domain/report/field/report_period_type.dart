import 'package:flutter/widgets.dart';
import 'package:bitacora/l10n/app_localizations.dart';

enum ReportPeriodType {
  today,
  sevenDays,
  other,
}

extension DisplayReportPeriodType on ReportPeriodType {
  String getDisplayValue(BuildContext context) {
    switch (this) {
      case ReportPeriodType.today:
        return AppLocalizations.of(context)!.today;
      case ReportPeriodType.sevenDays:
        return AppLocalizations.of(context)!.nDays(7);
      case ReportPeriodType.other:
        return AppLocalizations.of(context)!.otherSingular;
    }
  }
}
