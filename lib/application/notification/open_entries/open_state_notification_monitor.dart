import 'package:bitacora/application/notification/open_entries/entry_from_open_state_for_reschedule_repository_query.dart';
import 'package:bitacora/application/notification/open_entries/open_state_notification_scheduler.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/util/inject/inject.dart';

class OpenStateNotificationMonitorInjector {
  factory OpenStateNotificationMonitorInjector() =>
      inject(() => const OpenStateNotificationMonitorInjector._());

  const OpenStateNotificationMonitorInjector._();

  OpenStateNotificationMonitor get(Repository db, Session session) {
    return inject(() => OpenStateNotificationMonitor._(db, session));
  }
}

class OpenStateNotificationMonitor {
  OpenStateNotificationMonitor._(Repository db, Session session) {
    final scheduler = OpenStateNotificationScheduler();

    db.entry.getMutations().listen((event) {
      if (event.type.value == MutationTypeValue.insert) {
        scheduler.maybeCreateNotifications(
          db,
          event.model!.copyWith(id: event.id),
          session,
        );
      } else if (event.type.value == MutationTypeValue.delete) {
        scheduler.cancelNotifications(event.id!);
      }
    });

    db.openState.getMutations().listen((event) async {
      if (event.type.value == MutationTypeValue.update &&
          event.model?.progress?.value == 100) {
        final entry = await db.query(
            EntryFromOpenStateForRescheduleRepositoryQuery(
                openStateId: event.id!));
        await scheduler.cancelNotifications(entry!.id!);
      }
    });
  }
}
