import 'package:flutter/material.dart';

class ThemeImage extends StatelessWidget {
  final String name;
  final BoxFit? boxFit;
  final double? width;
  final double? height;

  const ThemeImage({
    super.key,
    required this.name,
    this.boxFit,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    final split = name.split('.');
    final imageName = '${split[0]}'
        '${Theme.of(context).brightness == Brightness.dark ? '_dark' : ''}'
        '.${split[1]}';

    return Image(
      image: AssetImage(imageName),
      fit: boxFit,
      width: width,
      height: height,
    );
  }
}
