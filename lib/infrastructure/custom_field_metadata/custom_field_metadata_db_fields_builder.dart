import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field/value/custom_field_type.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata_fields_builder.dart';
import 'package:bitacora/infrastructure/custom_field_metadata/custom_field_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';

class CustomFieldMetadataDbFieldsBuilder extends DbFieldsBuilder
    implements CustomFieldMetadataFieldsBuilder {
  CustomFieldMetadataDbFieldsBuilder() {
    _id();
  }

  CustomFieldMetadataDbContract get contract =>
      const CustomFieldMetadataDbContract();

  CustomFieldMetadataDbFieldsBuilder _id() {
    addField(
      CustomFieldMetadataField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataDbFieldsBuilder remoteId() {
    addField(
      CustomFieldMetadataField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder value() {
    addField(
      CustomFieldMetadataField.value,
      DbField(
        column: contract.value,
        columnAdder: (list) {
          list.add(contract.customFieldId);
        },
        multiNestedBuilder: (context, values, _) async {
          final value = values[contract.value];
          final db = context.db;
          final nestedContext = context.copyWith(
              fields: db.customField.fieldsBuilder.type().build());

          final customField = await db.customField
              .find(nestedContext, LocalId(values[contract.customFieldId]));

          switch (customField!.type!.value) {
            case CustomFieldTypeValue.radio:
            case CustomFieldTypeValue.select:
              return value is Map
                  ? CustomFieldMetadataValue(null)
                  : CustomFieldMetadataValue(value);
            case CustomFieldTypeValue.project:
            case CustomFieldTypeValue.user:
              return CustomFieldMetadataValue(null);
            case CustomFieldTypeValue.text:
            case CustomFieldTypeValue.textArea:
            case CustomFieldTypeValue.intNumber:
            case CustomFieldTypeValue.floatNumber:
            case CustomFieldTypeValue.time:
            case CustomFieldTypeValue.checkboxGroup:
              return CustomFieldMetadataValue(value);
            case CustomFieldTypeValue.dateTime:
            case CustomFieldTypeValue.date:
              return CustomFieldMetadataValue(value == null
                  ? null
                  : DateTime.fromMicrosecondsSinceEpoch(value));
            case CustomFieldTypeValue.checkbox:
              return CustomFieldMetadataValue(
                  value == null ? null : value == 1);
          }
        },
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder fieldName() {
    addField(
      CustomFieldMetadataField.fieldName,
      DbField(
        column: contract.fieldName,
        valueBuilder: (v) => CustomFieldMetadataFieldName(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder createdAt() {
    addField(
      CustomFieldMetadataField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) => CustomFieldMetadataCreatedAt(
            DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder updatedAt() {
    addField(
      CustomFieldMetadataField.updatedAt,
      DbField(
        column: contract.updatedAt,
        valueBuilder: (v) => CustomFieldMetadataUpdatedAt(
            DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder user(Fields fields) {
    addField(
      CustomFieldMetadataField.user,
      DbField(
        key: CustomFieldMetadataField.user,
        column: contract.userId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.user.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder allowedValue(Fields fields) {
    addField(
      CustomFieldMetadataField.allowedValue,
      DbField(
        key: CustomFieldMetadataField.allowedValue,
        column: contract.allowedValueId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.customFieldAllowedValue.find(
          nestedContext,
          LocalId(value),
        ),
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder project(Fields fields) {
    addField(
      CustomFieldMetadataField.project,
      DbField(
        key: CustomFieldMetadataField.project,
        column: contract.projectId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) async {
          final result = await nestedContext.db.project
              .find(nestedContext, LocalId(value));
          return result;
        },
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder customField(Fields fields) {
    addField(
      CustomFieldMetadataField.customField,
      DbField(
        key: CustomFieldMetadataField.customField,
        column: contract.customFieldId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.customField.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  CustomFieldMetadataFieldsBuilder templatelog(Fields fields) {
    addField(
      CustomFieldMetadataField.templatelog,
      DbField(
        key: CustomFieldMetadataField.templatelog,
        column: contract.templatelogId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.templatelog.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
