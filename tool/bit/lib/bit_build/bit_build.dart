import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/command_line.dart';
import 'package:bit/process.dart';

const _dev4a = 'dev4a';
const _dev4ios = 'dev4ios';
const _bit4a = 'bit4a';
const _bit4ios = 'bit4ios';

const _nymphacora = 'Nymphacora.io';
const _bitacora = 'Bitacora.io';

const _nymphacoraAppId = 'com.bitacora.locust';
const _bitacoraAppId = 'com.bitacora.locust';

const _kFlagHelp = '-h';
const _kFlagInstall = '--install';
const _kFlagSymbolsOutput = '--symbols-output';

final _kTargets = {
  _bit4ios: _BuildTarget(
    _bit4ios,
    _bitacora,
    _bitacoraAppId,
  ),
  _bit4a: _BuildTarget(
    _bit4a,
    _bitacora,
    _bitacoraAppId,
    is4a: true,
  ),
  _dev4ios: _BuildTarget(
    _dev4ios,
    _nymphacora,
    _nymphacoraAppId,
    isDev: true,
  ),
  _dev4a: _BuildTarget(
    _dev4a,
    _nymphacora,
    _nymphacoraAppId,
    isDev: true,
    is4a: true,
  ),
};

final _kInstallableTargets = {
  _bit4a: _BuildTarget(
    _bit4a,
    _bitacora,
    _bitacoraAppId,
    is4a: true,
    isAppBundle: false,
  ),
  _dev4a: _BuildTarget(
    _dev4a,
    _nymphacora,
    _nymphacoraAppId,
    isDev: true,
    is4a: true,
    isAppBundle: false,
  ),
};

const _help = '''
A tool for producing bitacora_flutter release builds.

Runs `flutter apk` `flutter ipa` with correct flags for releases.

Available targets:

 bit4ios    (Bitacora.io | ios | prod)
 bit4a      (Bitacora.io | android | prod)
 dev4ios    (Nymphacora.io | ios | dev)
 dev4a      (Nymphacora.io | android | dev)
 
Available arguments:

 --symbols-path  path/to/symbols/output
 --install       Build and install (works only on android). 
 
Targets are split by prod and dev builds. Dev builds have dev-tooling
available through app ui.

Usage: 

  bit build
  bit build <target> [arguments]
  bit install <target> (alias for `bit build <target> --install`)
  bit install (defaults to dev4a)
''';

Future<int> bitBuild([List<String> args = const <String>[]]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  final target = await _resolveTarget(args);
  if (target == null) {
    return 1;
  }

  if (args.contains(_kFlagSymbolsOutput)) {
    final symbolsIndex = args.indexOf(_kFlagSymbolsOutput);
    final symbolsOutputPath = args[symbolsIndex + 1];
    target.symbolsOutputPath = symbolsOutputPath;
  }

  writeln('Building ${target.label}...', TextStyle.highlighted);
  writeln('Symbols in ${target.symbolsOutputPath}');
  final build = await startProcess(
    'flutter',
    ['build', ...target.flutterBuildFlags],
    ProcessStartMode.inheritStdio,
  );

  final exitCode = await build.exitCode;
  await _maybeInstall(exitCode, target, args);
  return exitCode;
}

Future<int> bitInstall([List<String> args = const <String>[]]) {
  return bitBuild([
    ...args,
    if (args.isEmpty) 'dev4a',
    _kFlagInstall,
  ]);
}

Future<_BuildTarget?> _resolveTarget(List<String> args) async {
  if (args.isNotEmpty) {
    final codename = args.first;
    final target = args.contains(_kFlagInstall)
        ? _kInstallableTargets[codename]
        : _kTargets[codename];
    if (target != null) {
      return target;
    }

    writeln('Invalid target [$codename].', TextStyle.error);
  }
  return _requestTarget();
}

Future<_BuildTarget?> _requestTarget() async {
  writeln('What to build?', TextStyle.highlighted);
  for (var target in _kTargets.values) {
    writeln(target.label);
  }

  final target = await CommandLine(
    _kTargets.map((k, v) => MapEntry(k, () => v)),
    title: 'Select target.',
  ).present();

  return target;
}

Future<void> _maybeInstall(
  int buildExitCode,
  _BuildTarget target,
  List<String> args,
) async {
  if (!args.contains(_kFlagInstall)) {
    return;
  }

  if (!target.is4a) {
    writeln('Install only available for android.', TextStyle.error);
    return;
  }

  if (buildExitCode != 0) {
    return;
  }

  writeln('Installing ${target.codename}...');

  const apkLocation =
      'build/app/outputs/flutter-apk/app-armeabi-v7a-release.apk';
  await startProcess(
    'adb',
    ['install', '-r', apkLocation],
    ProcessStartMode.inheritStdio,
  );
}

class _BuildTarget {
  final String codename;
  final String appName;
  final String appId;
  final bool isDev;
  final bool is4a;
  final bool isAppBundle;
  String symbolsOutputPath;

  _BuildTarget(
    this.codename,
    this.appName,
    this.appId, {
    this.isDev = false,
    this.is4a = false,
    this.isAppBundle = true,
  }) : symbolsOutputPath = 'build/_symbols/$codename';

  List<String> get flutterBuildFlags {
    final flags = <String>[];
    flags.addAll([
      is4a
          ? isAppBundle
              ? 'appbundle'
              : 'apk'
          : 'ipa',
      '--dart-define=APP_NAME=$appName',
      '--dart-define=APPLICATION_ID=$appId',
      if (isDev) '--dart-define=DEV=true',
      '--obfuscate',
      '--split-debug-info=$symbolsOutputPath',
      '--extra-gen-snapshot-options=--save-obfuscation-map'
          '=$symbolsOutputPath/obfuscation-map.json',
      if (is4a && !isAppBundle) '--split-per-abi',
    ]);

    // FIXME: set different application_ids. Need to fix some config files.
    //   firebase_options.dart
    //   google-services.json
    //   GoogleService-Info.plist
    // (larvae / locust / ?)

    return flags;
  }

  String get label => '$codename '
      '($appName | ${is4a ? 'android' : 'ios'} | ${isDev ? 'dev' : 'prod'})';
}
