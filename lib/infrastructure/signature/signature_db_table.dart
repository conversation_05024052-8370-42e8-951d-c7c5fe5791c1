import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_key.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/domain/signature/signature_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/signature/signature_db_contract.dart';
import 'package:bitacora/infrastructure/signature/signature_db_fields_builder.dart';
import 'package:bitacora/infrastructure/signature/signature_db_translator.dart';
import 'package:bitacora/infrastructure/signature/signature_for_outgoing_mutation_repository_query.dart';
import 'package:bitacora/util/logger/logger.dart';

class SignatureDbTable extends DbTable<Signature, SignatureDbFieldsBuilder>
    implements SignatureRepository<DbContext, SignatureDbFieldsBuilder> {
  SignatureDbTable() : super();

  @override
  SignatureDbFieldsBuilder get fieldsBuilder => SignatureDbFieldsBuilder();

  @override
  DbTranslator<Signature> get translator => const SignatureDbTranslator();

  @override
  SignatureDbContract get contract => const SignatureDbContract();

  @override
  Future<void> onSyncRequested(
      DbContext context, Mutation<Signature> mutation) async {
    final signature = await context.db.query(
      SignatureForOutgoingMutationRepositoryQuery(signatureId: mutation.id!),
      context: context,
    );

    if (mutation.type == MutationType.delete &&
        signature?.remoteId?.value == null) {
      return;
    }

    if (signature == null) {
      logger.f('db:entry Tried to sync missing entry.');
      return;
    }

    await context.db.outgoingMutation.save(
      context,
      OutgoingMutation(
        key: OutgoingMutationKey(DateTime.now().microsecondsSinceEpoch),
        mutationType: mutation.type,
        model: OutgoingMutationModel(
          signature,
          OutgoingMutationModelType.signature,
        ),
        organization: Organization(id: context.queryScope!.orgId),
      ),
    );
  }

  @override
  Future<List<Signature>> findAll(DbContext context, LocalId entryId) {
    return query(
      context,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );
  }

  @override
  Future<void> saveAll(
    DbContext context,
    Mutation<Entry> entryMutation,
  ) async {
    assert(context.txn != null);

    final signatures = entryMutation.model!.signatures!;
    for (final signature in signatures) {
      if (signature.id == null) {
        await save(context, signature, requestSync: true);
      }
    }
  }

  @override
  Future<void> deleteAll(DbContext context, LocalId entryId) async {
    final executor = await context.executor;
    await executor.delete(
      contract.tableName,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );
  }
}
