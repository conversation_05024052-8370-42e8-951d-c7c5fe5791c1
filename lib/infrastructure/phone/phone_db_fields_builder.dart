import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/phone/phone.dart';
import 'package:bitacora/domain/phone/phone_post_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/phone/phone_db_contract.dart';

class PhoneDbFieldsBuilder extends DbFieldsBuilder
    implements PhoneFieldsBuilder {
  PhoneDbFieldsBuilder() {
    _id();
  }

  PhoneDbContract get contract => const PhoneDbContract();

  PhoneDbFieldsBuilder _id() {
    addField(
      PhoneField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  PhoneDbFieldsBuilder remoteId() {
    addField(
      PhoneField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  PhoneDbFieldsBuilder number() {
    addField(
      PhoneField.number,
      DbField(
        column: contract.number,
        valueBuilder: (v) => PhoneNumber(v),
      ),
    );
    return this;
  }

  @override
  PhoneDbFieldsBuilder type() {
    addField(
      PhoneField.type,
      DbField(
        column: contract.type,
        valueBuilder: (v) => PersonDetailContactType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  PhoneDbFieldsBuilder updatedAt() {
    addField(
      PhoneField.updatedAt,
      DbField(
        column: contract.updatedAt,
        valueBuilder: (v) =>
            PhoneUpdatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  PhoneDbFieldsBuilder createdAt() {
    addField(
      PhoneField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) =>
            PhoneCreatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  PhoneFieldsBuilder personDetail(Fields fields) {
    addField(
      PhoneField.personDetail,
      DbField(
        key: PhoneField.personDetail,
        column: contract.personDetailId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.personDetail.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
