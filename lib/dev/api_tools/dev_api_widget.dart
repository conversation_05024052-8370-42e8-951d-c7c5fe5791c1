import 'package:bitacora/dev/api_tools/dev_api_man_in_the_middle.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:flutter/material.dart';

const _kDefaultMockStatusCode = 500;

class DevApiMockStatusWidget extends StatefulWidget {
  const DevApiMockStatusWidget({super.key});

  @override
  State<DevApiMockStatusWidget> createState() => _DevApiMockStatusWidgetState();
}

class _DevApiMockStatusWidgetState extends State<DevApiMockStatusWidget> {
  final TextEditingController _mockStatusController = TextEditingController();

  int? _mockStatusCode;
  bool _isSlowdownEnabled = false;
  bool _isMockStatusCodeEnabled = false;

  @override
  void initState() {
    super.initState();
    DevApiManInTheMiddle.getStatusCode().then(
      (value) => setState(() {
        _mockStatusController.text = '${value ?? ''}';
        _mockStatusCode = value;
        _isMockStatusCodeEnabled = _mockStatusCode != null;
      }),
    );
    DevApiManInTheMiddle.getSlowdownDuration().then(
      (value) => setState(() {
        _isSlowdownEnabled = value.inMilliseconds > 0;
      }),
    );
  }

  @override
  void dispose() {
    _mockStatusController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        IconButton(
          icon: Stack(
            children: [
              Icon(
                _isMockStatusCodeEnabled && _mockStatusCode != 200
                    ? Icons.wifi_off
                    : Icons.wifi,
                color: bitacoraGreen,
              ),
              if (_isSlowdownEnabled)
                const Positioned(
                  right: 0,
                  bottom: 0,
                  child: SizedBox(
                    width: 10,
                    height: 10,
                    child: Icon(
                      Icons.help,
                      color: bitacoraGreen,
                      size: 10,
                    ),
                  ),
                ),
            ],
          ),
          onPressed: () => _toggleSlowdown(),
        ),
        const Text(
          'Mock Api Response:',
          style: TextStyle(color: bitacoraGreen, fontWeight: FontWeight.w600),
        ),
        const SizedBox(width: 8),
        SizedBox(
          width: 70,
          height: 40,
          child: TextFormField(
            controller: _mockStatusController,
            decoration: InputDecoration(
              hintText: 'i.e. ${_mockStatusCode ?? _kDefaultMockStatusCode}',
            ),
            enabled: _isMockStatusCodeEnabled,
            onChanged: (text) => setState(() {
              try {
                _mockStatusCode = int.parse(text);
              } catch (_) {
                _mockStatusCode = null;
              }
              DevApiManInTheMiddle.setStatusCode(_mockStatusCode);
            }),
          ),
        ),
        const SizedBox(width: 10),
        SizedBox(
            width: 30,
            height: 30,
            child: Switch(
              value: _isMockStatusCodeEnabled,
              onChanged: (value) {
                setState(() {
                  _isMockStatusCodeEnabled = value;
                  _mockStatusCode ??= _kDefaultMockStatusCode;
                  if (value) {
                    _mockStatusController.text = '${_mockStatusCode ?? ''}';
                  } else {
                    _mockStatusController.text = '';
                  }
                });
                if (value) {
                  DevApiManInTheMiddle.setStatusCode(_mockStatusCode);
                } else {
                  DevApiManInTheMiddle.setStatusCode(null);
                }
              },
            )),
      ],
    );
  }

  void _toggleSlowdown() {
    setState(() {
      _isSlowdownEnabled = !_isSlowdownEnabled;
      DevApiManInTheMiddle.setSlowdownDuration(
          Duration(seconds: (_isSlowdownEnabled ? 5 : 0)));
    });
  }
}
