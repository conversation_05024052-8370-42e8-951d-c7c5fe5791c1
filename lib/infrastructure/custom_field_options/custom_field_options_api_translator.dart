import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';

class CustomFieldOptionsApiTranslator
    implements ModelTranslator<CustomFieldOptions> {
  const CustomFieldOptionsApiTranslator();

  @override
  CustomFieldOptions fromMap(Map<String, dynamic> data) {
    return CustomFieldOptions(
      remoteId: RemoteId(data['id']),
      isRequired: CustomFieldOptionsIsRequired(data['is_required']),
      placeholder: CustomFieldOptionsPlaceholder(data['placeholder']),
      customField: CustomField(remoteId: RemoteId(data['custom_field_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(CustomFieldOptions model) {
    throw UnimplementedError();
  }
}
