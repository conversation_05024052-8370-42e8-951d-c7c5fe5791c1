import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';

class PendingAttachmentsUploadRepositoryQuery
    extends RepositoryQuery<List<Attachment>> {
  const PendingAttachmentsUploadRepositoryQuery();

  @override
  Future<List<Attachment>> run(RepositoryQueryContext context) {
    return context.db.attachment.findPendingUpload(context);
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.attachment.fieldsBuilder.build();
}
