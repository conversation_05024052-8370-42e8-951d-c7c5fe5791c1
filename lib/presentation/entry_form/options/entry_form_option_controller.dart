import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';

abstract class EntryFormOptionController {
  final GlobalKey key = GlobalKey();
  final ValueNotifier<Entry?> liveEntry;

  final ValueNotifier<bool> isShowing = ValueNotifier(false);
  final ValueNotifier<bool> hasData = ValueNotifier(false);
  var mounted = true;

  EntryFormOptionController(this.liveEntry);

  void dispose() {
    mounted = false;
    isShowing.dispose();
    hasData.dispose();
  }

  void onTap(BuildContext context) {
    isShowing.value = !isShowing.value;
  }

  bool get isAvailable => true;

  MaterialColor get color;

  Color getSelectedColor(BuildContext context) {
    return colorWithOffset(context, color, 100)!;
  }

  IconData get icon;

  Widget iconWidget(BuildContext context) => ValueListenableBuilder<bool>(
      valueListenable: hasData,
      builder: (context, hasDataValue, _) {
        return Icon(icon,
            color: hasDataValue ? getSelectedColor(context) : null);
      });

  Widget form(BuildContext context);
}
