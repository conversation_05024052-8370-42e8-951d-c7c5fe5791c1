import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/signature/value/signature_description.dart';
import 'package:bitacora/domain/signature/value/signature_doodle.dart';
import 'package:bitacora/domain/signature/value/signature_location.dart';
import 'package:bitacora/domain/signature/value/signature_owner_email.dart';
import 'package:bitacora/domain/signature/value/signature_owner_name.dart';
import 'package:bitacora/domain/signature/value/signature_s3_key.dart';
import 'package:bitacora/domain/signature/value/signature_status.dart';
import 'package:bitacora/domain/user/user.dart';

export 'package:bitacora/domain/signature/value/signature_description.dart';
export 'package:bitacora/domain/signature/value/signature_doodle.dart';
export 'package:bitacora/domain/signature/value/signature_location.dart';
export 'package:bitacora/domain/signature/value/signature_owner_email.dart';
export 'package:bitacora/domain/signature/value/signature_owner_name.dart';
export 'package:bitacora/domain/signature/value/signature_s3_key.dart';
export 'package:bitacora/domain/signature/value/signature_status.dart';

class Signature extends Model {
  final SignatureS3Key? s3Key;
  final SignatureDoodle? doodle;
  final SignatureOwnerName? ownerName;
  final SignatureOwnerEmail? ownerEmail;
  final SignatureComments? comments;
  final SignatureStatus? status;
  final SignatureLocation? location;

  final Entry? entry;
  final User? user;

  Signature({
    super.id,
    super.remoteId,
    this.s3Key,
    this.doodle,
    this.ownerName,
    this.ownerEmail,
    this.comments,
    this.status,
    this.location,
    this.entry,
    this.user,
  });

  Signature copyWith({
    LocalId? id,
    RemoteId? remoteId,
    SignatureDoodle? doodle,
    SignatureS3Key? s3Key,
    SignatureOwnerName? ownerName,
    SignatureOwnerEmail? ownerEmail,
    SignatureComments? comments,
    SignatureStatus? status,
    SignatureLocation? location,
    Entry? entry,
    User? user,
  }) {
    return Signature(
      id: id ?? this.id,
      remoteId: remoteId ?? this.remoteId,
      doodle: doodle ?? this.doodle,
      s3Key: s3Key ?? this.s3Key,
      ownerName: ownerName ?? this.ownerName,
      ownerEmail: ownerEmail ?? this.ownerEmail,
      status: status ?? this.status,
      comments: comments ?? this.comments,
      location: location ?? this.location,
      entry: entry ?? this.entry,
      user: user ?? this.user,
    );
  }

  @override
  Map<Field, dynamic> get fields => {
        SignatureField.id: id,
        SignatureField.remoteId: remoteId,
        SignatureField.doodle: doodle,
        SignatureField.s3Key: s3Key,
        SignatureField.ownerName: ownerName,
        SignatureField.ownerEmail: ownerEmail,
        SignatureField.comments: comments,
        SignatureField.status: status,
        SignatureField.location: location,
        SignatureField.entry: entry,
        SignatureField.user: user,
      };
}

enum SignatureField with Field {
  id,
  remoteId,
  doodle,
  s3Key,
  ownerName,
  ownerEmail,
  comments,
  status,
  location,
  entry,
  user,
}

const signatureNestedModelFields = {SignatureField.entry, SignatureField.user};
