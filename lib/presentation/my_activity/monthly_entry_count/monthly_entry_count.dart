import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:bitacora/presentation/my_activity/monthly_entry_count/monthly_entry_bar_chart.dart';
import 'package:bitacora/presentation/my_activity/monthly_entry_count/monthly_entry_count_repository_query.dart';
import 'package:bitacora/presentation/my_activity/my_activity_percent_chip.dart';
import 'package:bitacora/util/my_activity/my_activity_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:provider/provider.dart';

class MonthlyEntryCount extends StatefulWidget {
  const MonthlyEntryCount({super.key});

  @override
  State<MonthlyEntryCount> createState() => _MonthlyEntryCountState();
}

class _MonthlyEntryCountState extends State<MonthlyEntryCount> {
  List<int>? _values;
  int? _lastTotalCount;
  double? _comparePercent;

  @override
  void initState() {
    super.initState();

    _loadData();
  }

  void _loadData() async {
    final db = context.read<Repository>();
    final user = context.read<ActiveSession>().value!.user;
    final organization = context.read<ActiveOrganization>().value!;

    final now = DateTime.now();
    final count = await db.query(
      MonthlyEntryCountRepositoryQuery(now),
      context: db.context(
        queryScope: db.queryScope(
          userId: user.id,
          orgId: organization.id,
        ),
      ),
    );

    final lastMonthDate = DateTime(now.year, now.month - 1, 1);
    final lastMonthCount = await db.query(
      MonthlyEntryCountRepositoryQuery(lastMonthDate),
      context: db.context(
        queryScope: db.queryScope(
          userId: user.id,
          orgId: organization.id,
        ),
      ),
    );
    _lastTotalCount = lastMonthCount.values.reduce((a, b) => a + b);

    setState(() {
      _comparePercent =
          MyActivityUtils().calculateComparePercent(count, lastMonthCount);
      _values = count.values.toList(growable: false);
    });
  }

  @override
  Widget build(BuildContext context) {
    if (_values == null) {
      return Center(child: PlatformCircularProgressIndicator());
    }

    return Column(
      children: [
        Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context)!.monthChartTitle,
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    color: Theme.of(context).colorScheme.surface,
                  ),
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${_values!.reduce((a, b) => a + b)}',
                  style: Theme.of(context).textTheme.displayMedium!.copyWith(
                        color: Theme.of(context).colorScheme.surface,
                      ),
                ),
                MyActivityPercentChip(
                    percent: _comparePercent!, brightness: Brightness.dark),
              ],
            ),
            Text(
              AppLocalizations.of(context)!
                  .monthChartSubtitle(_lastTotalCount!),
              style: Theme.of(context).textTheme.bodySmall!.copyWith(
                    color: Theme.of(context).colorScheme.surface,
                  ),
            ),
          ],
        ),
        SizedBox(height: 16.0),
        ConstrainedBox(
            constraints: BoxConstraints(maxHeight: 100),
            child: MonthlyEntryBarChart(values: _values!)),
      ],
    );
  }
}
