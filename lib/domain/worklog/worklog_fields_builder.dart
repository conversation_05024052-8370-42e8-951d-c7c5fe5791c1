import 'package:bitacora/domain/common/repository_query_context.dart';

abstract class WorklogFieldsBuilder {
  WorklogFieldsBuilder remoteId();

  WorklogFieldsBuilder title();

  WorklogFieldsBuilder quantity();

  WorklogFieldsBuilder sublocation();

  WorklogFieldsBuilder costPrice();

  WorklogFieldsBuilder salePrice();

  WorklogFieldsBuilder paymentStatus();

  WorklogFieldsBuilder priceIsUnit();

  WorklogFieldsBuilder provider();

  WorklogFieldsBuilder type();

  WorklogFieldsBuilder project(Fields fields);

  Fields build();
}
