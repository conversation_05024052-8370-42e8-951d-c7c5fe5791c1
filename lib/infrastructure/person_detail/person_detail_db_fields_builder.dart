import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';
import 'package:bitacora/domain/person_detail/person_detail_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/person_detail/person_detail_db_contract.dart';

class PersonDetailDbFieldsBuilder extends DbFieldsBuilder
    implements PersonDetailFieldsBuilder {
  PersonDetailDbFieldsBuilder() {
    _id();
  }

  PersonDetailDbContract get contract => const PersonDetailDbContract();

  PersonDetailDbFieldsBuilder _id() {
    addField(
      PersonDetailField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  PersonDetailDbFieldsBuilder remoteId() {
    addField(
      PersonDetailField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  PersonDetailDbFieldsBuilder area() {
    addField(
      PersonDetailField.area,
      DbField(
        column: contract.area,
        valueBuilder: (v) => PersonDetailArea(v),
      ),
    );
    return this;
  }

  @override
  PersonDetailDbFieldsBuilder company() {
    addField(
      PersonDetailField.company,
      DbField(
        column: contract.company,
        valueBuilder: (v) => PersonDetailCompany(v),
      ),
    );
    return this;
  }

  @override
  PersonDetailDbFieldsBuilder description() {
    addField(
      PersonDetailField.description,
      DbField(
        column: contract.description,
        valueBuilder: (v) => PersonDetailDescription(v),
      ),
    );
    return this;
  }

  @override
  PersonDetailFieldsBuilder person(Fields fields) {
    addField(
      PersonDetailField.person,
      DbField(
        key: PersonDetailField.person,
        column: contract.personId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.person.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  PersonDetailFieldsBuilder avatar(Fields fields) {
    addField(
      PersonDetailField.avatar,
      DbField(
        key: PersonDetailField.avatar,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext.db.avatar
            .findByPersonDetailId(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  PersonDetailFieldsBuilder phones(Fields fields) {
    addField(
      PersonDetailField.phones,
      DbField(
        key: PersonDetailField.phones,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.phone.findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  PersonDetailFieldsBuilder emails(Fields fields) {
    addField(
      PersonDetailField.emails,
      DbField(
        key: PersonDetailField.emails,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.email.findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  PersonDetailFieldsBuilder addresses(Fields fields) {
    addField(
      PersonDetailField.addresses,
      DbField(
        key: PersonDetailField.addresses,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.address.findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  PersonDetailFieldsBuilder organization(Fields fields) {
    addField(
      PersonDetailField.organization,
      DbField(
        key: PersonDetailField.organization,
        column: contract.organizationId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.organization.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
