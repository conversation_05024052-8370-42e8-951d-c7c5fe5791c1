import 'dart:math' as math;

import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';

class FloatingActionMenuArcText extends StatefulWidget {
  final double radius;
  final String text;
  final TextStyle textStyle;

  const FloatingActionMenuArcText({
    super.key,
    required this.radius,
    required this.text,
    required this.textStyle,
  });

  @override
  State<FloatingActionMenuArcText> createState() =>
      _FloatingActionMenuArcTextState();
}

class _FloatingActionMenuArcTextState extends State<FloatingActionMenuArcText>
    with SingleTickerProviderStateMixin {
  late final AnimationController _animationController;
  late final Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    final tween = Tween<double>(begin: 1, end: 1.1);
    _animation = tween.animate(CurvedAnimation(
        parent: _animationController, curve: Curves.bounceInOut));

    _animationController
      ..forward()
      ..addListener(() {
        if (_animationController.isCompleted) {
          _animationController.reverse();
        } else if (_animationController.isDismissed) {
          _animationController.forward();
        }
      });
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _animation,
      child: CustomPaint(
        painter: _Painter(
          widget.radius,
          widget.text,
          widget.textStyle,
        ),
      ),
    );
  }
}

class _Painter extends CustomPainter {
  final double radius;
  final String text;
  final TextStyle textStyle;

  double? textAngle;

  _Painter(this.radius, this.text, this.textStyle);

  final _textPainter = TextPainter(textDirection: TextDirection.ltr);

  @override
  void paint(Canvas canvas, Size size) {
    canvas.translate(size.width / 2, size.height / 2 - radius);

    textAngle ??= _findTextAngle(text);

    if (textAngle! > 360.0) {
      throw 'Text $text is too large.';
    }

    final initialAngle = (315 * math.pi / 180) - textAngle! / 2;
    if (initialAngle != 0) {
      final d = 2 * radius * math.sin(initialAngle / 2);
      final rotationAngle = _calculateRotationAngle(0, initialAngle);
      canvas.rotate(rotationAngle);
      canvas.translate(d, 0);
    }

    var angle = initialAngle;
    for (int i = 0; i < text.length; i++) {
      angle = _drawLetter(canvas, text[i], angle);
    }
  }

  double _findTextAngle(String text) {
    var angle = 0.0;
    for (var i = 0; i < text.length; i++) {
      _textPainter.text = TextSpan(text: text[i], style: textStyle);
      _textPainter.layout(
        minWidth: 0,
        maxWidth: double.maxFinite,
      );
      final d = _textPainter.width;
      angle += 2 * math.asin(d / (2 * radius));
    }

    return angle;
  }

  double _drawLetter(Canvas canvas, String letter, double prevAngle) {
    _textPainter.text = TextSpan(text: letter, style: textStyle);
    _textPainter.layout(
      minWidth: 0,
      maxWidth: double.maxFinite,
    );

    final d = _textPainter.width;
    final alpha = 2 * math.asin(d / (2 * radius));

    final newAngle = _calculateRotationAngle(prevAngle, alpha);
    canvas.rotate(newAngle);

    _textPainter.paint(canvas, Offset(0, -_textPainter.height));
    canvas.translate(d, 0);

    return alpha;
  }

  double _calculateRotationAngle(double prevAngle, double alpha) =>
      (alpha + prevAngle) / 2;

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
