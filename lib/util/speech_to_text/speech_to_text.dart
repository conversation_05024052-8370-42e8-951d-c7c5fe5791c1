import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:speech_to_text/speech_to_text.dart';

/// FIXME: Migrate to use the driver when refactoring old audio recording.
class SpeechToTextInjector {
  static final SpeechToText _speechToText = SpeechToText();

  static SpeechToText get() => inject(() => _speechToText);

  SpeechToTextInjector._();
}

class SpeechToTextController {
  final SpeechToText _speechToText = SpeechToTextInjector.get();
  String _recognizedWords = '';

  Future<dynamic> listen() async {
    _recognizedWords = '';

    final available = await _speechToText.initialize(
      onError: (e) => logger.i('entry:audio-listener STT error: $e'),
    );

    if (!available) {
      logger.w('Speech to text not available');
      return;
    }

    await _speechToText.listen(
      onResult: (val) {
        _recognizedWords = '$_recognizedWords $val.recognizedWords';
        logger.d('stt:recognizedWords: <$_recognizedWords>');
      },
    );
  }

  Future<void> cancel() {
    _recognizedWords = '';
    return _speechToText.cancel();
  }

  Future<void> stop() {
    return _speechToText.stop();
  }

  String get recognizedWords => _recognizedWords;
}
