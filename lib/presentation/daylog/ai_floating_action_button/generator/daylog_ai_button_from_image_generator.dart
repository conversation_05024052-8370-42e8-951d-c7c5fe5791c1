import 'dart:async';

import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/daylog/ai_floating_action_button/generator/daylog_ai_button_generator.dart';
import 'package:bitacora/util/attachment/attachment_compression.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:firebase_vertexai/firebase_vertexai.dart';
import 'package:flutter/material.dart';
import 'package:mime/mime.dart';
import 'package:path/path.dart' as path;
import 'package:file/file.dart';

class DaylogAiButtonFromImageGenerator extends DaylogAiButtonSourceGenerator {
  DaylogAiButtonFromImageGenerator(super.contextSnapshot);

  @override
  DaylogAiButtonSource get source => DaylogAiButtonSource.image;

  @override
  AnalyticsEvent get event => AnalyticsEvent.aiVideoEntrySelected;

  @override
  Future<Entry> runInternal() async {
    logAnalytics();

    final originalFiles = await pickFiles();

    durationTracker.markStart();
    logger.i('ai-generation:from image process started');
    final compressedFiles = await _compressImages(originalFiles);
    logger.i('ai-generation:image compression completed [$durationTracker]');

    logger.i('ai-generation:uploading compressed files [$durationTracker]');
    final fileUris = await uploadTempFiles(compressedFiles);
    logger.i(
        'ai-generation:uploaded ${fileUris.length} files [$durationTracker]');

    logger.i('ai-generation:creating file data parts [$durationTracker]');
    final parts = await Future.wait(fileUris.map((i) async {
      final mimeType = lookupMimeType(i)!;
      logger.i(
          'ai-generation:processing file with mime type: $mimeType [$durationTracker]');
      return FileData(mimeType, i);
    }));
    logger.i('ai-generation:created ${parts.length} file '
        'data parts [$durationTracker]');

    logger.i('ai-generation:starting parallel operations '
        '(AI generation + location) [$durationTracker]');
    final (generatedEntry, location) =
        await (generate(parts), getEntryLocation()).wait;
    logger.i('ai-generation:AI generation and location '
        'retrieval completed [$durationTracker]');

    logger.i('ai-generation:cleaning up temporary files [$durationTracker]');
    unawaited(deleteTempFiles(compressedFiles));

    logger
        .i('ai-generation:processing files to attachments [$durationTracker]');
    final attachments = await Future.wait(compressedFiles.map((i) {
      return processFileToAttachment(i);
    }));
    logger.i('ai-generation:processed ${attachments.length}'
        'attachments [$durationTracker]');

    logger.i('ai-generation:creating final entry object [$durationTracker]');
    final finalEntry = generatedEntry.copyWith(
      attachments: [],
      tags: [],
      location: location,
      source: generatedEntry.source!.copyWith(attachments: attachments),
    );

    logger.i('ai-generation:from image process '
        'completed successfully [$durationTracker]');

    return finalEntry;
  }

  Future<List<File>> _compressImages(List<File> originalFiles) async {
    logger.i('ai-generation:initializing image compressor [$durationTracker]');
    final compressor = AttachmentCompression();

    logger.i('ai-generation:compressing ${originalFiles.length} '
        'images [$durationTracker]');
    final compressedFiles = await Future.wait(originalFiles.map((file) async {
      logger.i('ai-generation:compressing file: '
          '${path.basename(file.path)} [$durationTracker]');

      final key =
          ValueKey<String>(DateTime.now().millisecondsSinceEpoch.toString());
      final stagingDirectory = await StorageUtils().getStagingDirectory(key);

      final compressedFile =
          await compressor.maybeCompressImage(file, stagingDirectory.path, 75);

      if (compressedFile != null) {
        logger.i('ai-generation:file compressed successfully: '
            '${path.basename(compressedFile.path)} [$durationTracker]');
        return compressedFile;
      } else {
        logger.i('ai-generation:file compression skipped, using original: '
            '${path.basename(file.path)} [$durationTracker]');
        return file;
      }
    }));

    logger.i(
        'ai-generation:image compression batch completed [$durationTracker]');
    return compressedFiles;
  }
}
