import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/signature/signature.dart';

class EntrySignatureItemRepositoryQuery extends RepositoryQuery<Signature?> {
  final LocalId signatureId;

  EntrySignatureItemRepositoryQuery({required this.signatureId});

  @override
  Future<Signature?> run(RepositoryQueryContext context) =>
      context.db.signature.find(context, signatureId);

  @override
  Fields fields(Repository db) => db.signature.fieldsBuilder
      .status()
      .doodle()
      .ownerEmail()
      .ownerName()
      .comments()
      .build();
}
