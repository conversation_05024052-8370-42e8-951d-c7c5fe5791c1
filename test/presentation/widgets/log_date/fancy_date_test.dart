import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/presentation/widgets/log_date/fancy_date.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:intl/intl.dart' as intl;

void main() {
  Widget testApp(child) {
    return MaterialApp(
      home: Localizations(
          locale: const Locale('en'),
          delegates: AppLocalizations.localizationsDelegates,
          child: Material(child: child)),
    );
  }

  group('$FancyDate test', () {
    testWidgets('Display date from current year', (tester) async {
      final now = DateTime.now();
      final logDay = LogDay(getLogDayFromDateTime(now));
      await tester.pumpWidget(testApp(FancyDate(logDay: logDay)));

      expect(find.text(now.day.toString()), findsOneWidget);
      expect(find.text(intl.DateFormat.MMM('en').format(now)), findsOneWidget);
      expect(find.text(intl.DateFormat.E('en').format(now)), findsOneWidget);
    });

    testWidgets('Display date from another year', (tester) async {
      const logDay = LogDay(20200101);
      final date = getDateTimeFromLogDay(logDay);
      await tester.pumpWidget(testApp(const FancyDate(logDay: logDay)));

      expect(find.text(date.day.toString()), findsOneWidget);
      expect(find.text(intl.DateFormat.MMM('en').format(date)), findsOneWidget);
      expect(find.text(date.year.toString()), findsOneWidget);
    });
  });
}
