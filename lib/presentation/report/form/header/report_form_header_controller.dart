import 'dart:math';

import 'package:bitacora/application/sync/machine/steps/download/collection/template/template_by_remote_id_repository_query.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/report/field/report_entry_type.dart';
import 'package:bitacora/domain/report/field/report_format.dart';
import 'package:bitacora/domain/report/field/report_period_type.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:flutter/widgets.dart';
import 'package:intl/intl.dart';

const _kDifferenceSevenDays = 6;

class ReportFormHeaderController {
  final DateFormat dateFormat;
  final ValueNotifier<ReportEntryType> entryType =
      ValueNotifier(ReportEntryType.all());
  final ValueNotifier<ReportFormat> format =
      ValueNotifier(ReportFormat.photoPdf);
  final ValueNotifier<ReportPeriodType> periodType =
      ValueNotifier(ReportPeriodType.today);
  final ValueNotifier<Set<Entry>> selectedEntries = ValueNotifier({});

  final TextEditingController periodStart = TextEditingController();
  final TextEditingController periodEnd = TextEditingController();

  ReportFormHeaderController(BuildContext context)
      : dateFormat =
            DateFormat.yMMMd(Localizations.localeOf(context).languageCode) {
    periodStart.addListener(_maybeValidatePeriodRange);
    periodEnd.addListener(() => _maybeValidatePeriodRange(takeStart: true));
  }

  bool get isExcel => format.value == ReportFormat.excel;

  Future<void> setDefaultValues(
    Repository db,
    Report? report,
    Set<Entry>? selected,
  ) async {
    final hasSelected = (selected?.isNotEmpty ?? false);
    if (hasSelected) {
      selectedEntries.value = selected!;
    }

    if (report == null) {
      return;
    }

    if (report.entryType != null) {
      if (hasSelected) {
        entryType.value = ReportEntryType.all();
      } else if (report.entryType!.value == ReportEntryTypeValue.templatelog) {
        final template = await db.query(TemplateByRemoteIdRepositoryQuery(
            report.entryType!.template!.remoteId!));
        entryType.value = ReportEntryType(
            value: report.entryType!.value, template: template!);
      } else {
        entryType.value = report.entryType!;
      }
    }
    if (report.format != null) {
      format.value = report.format!;
    }
    if (report.minDate != null && report.maxDate != null) {
      _setPeriod(report.minDate!, report.maxDate!);
    }
  }

  final FocusNode startDateFocus = FocusNode();

  Map<String, dynamic> getApiPostParams(BuildContext context) {
    final period = _getPeriod(context);
    return <String, dynamic>{
      format.value == ReportFormat.excel ? 'min_day' : 'min_date': period[0],
      format.value == ReportFormat.excel ? 'max_day' : 'max_date': period[1],
      'entry_type': entryType.value.value.apiValue,
      'type': format.value.apiValue,
      if (selectedEntries.value.isNotEmpty)
        'entry_ids': selectedEntries.value
            .map((e) => e.remoteId!.value)
            .toList(growable: false),
      if (entryType.value.value == ReportEntryTypeValue.templatelog &&
          entryType.value.template != null)
        'template_id': entryType.value.template!.remoteId!.apiValue
    };
  }

  List<int> _getPeriod(BuildContext context) {
    final outFormat = DateFormat('yyyyMMdd');

    if (selectedEntries.value.isNotEmpty) {
      int? minLogDay;
      int? maxLogDay;
      for (final entry in selectedEntries.value) {
        final entryMaxLogDay = entry.endDate?.value ?? entry.day!.value;
        final entryMinLogDay = entry.startDate?.value ?? entry.day!.value;

        maxLogDay = max(entryMaxLogDay, maxLogDay ?? entryMaxLogDay);
        minLogDay = min(entryMinLogDay, minLogDay ?? entryMinLogDay);
      }
      periodType.value = ReportPeriodType.other;
      return [
        getDateTimeFromLogDay(LogDay(minLogDay!)),
        getDateTimeFromLogDay(LogDay(maxLogDay!))
      ].map((e) => int.parse(outFormat.format(e))).toList(growable: false);
    }

    switch (periodType.value) {
      case ReportPeriodType.today:
        final today = int.parse(outFormat.format(DateTime.now()));
        return [today, today];
      case ReportPeriodType.sevenDays:
        final lastWeek = int.parse(outFormat.format(DateTime.now()
            .subtract(const Duration(days: _kDifferenceSevenDays))));
        final today = int.parse(outFormat.format(DateTime.now()));
        return [lastWeek, today];
      case ReportPeriodType.other:
        final textFormat =
            DateFormat.yMMMd(Localizations.localeOf(context).languageCode);
        return [periodStart, periodEnd]
            .map((e) => int.parse(outFormat.format(textFormat.parse(e.text))))
            .toList(growable: false);
    }
  }

  void _setPeriod(int minDate, int maxDate) {
    final minDateTime = getDateTimeFromLogDayValue(minDate);
    final maxDateTime = getDateTimeFromLogDayValue(maxDate);

    if (minDate == maxDate) {
      periodType.value = ReportPeriodType.today;
      return;
    }

    if (maxDateTime.difference(minDateTime).inDays == _kDifferenceSevenDays) {
      periodType.value = ReportPeriodType.sevenDays;
      return;
    }

    periodType.value = ReportPeriodType.other;
    periodStart.text = dateFormat.format(minDateTime);
    periodEnd.text = dateFormat.format(maxDateTime);
  }

  void _maybeValidatePeriodRange({takeStart = false}) {
    final start =
        periodStart.text.isNotEmpty ? dateFormat.parse(periodStart.text) : null;
    final end =
        periodEnd.text.isNotEmpty ? dateFormat.parse(periodEnd.text) : null;

    if (start != null && end != null) {
      if (start.isBefore(end)) {
        return;
      }
      if (takeStart) {
        periodStart.text = periodEnd.text;
      } else {
        periodEnd.text = periodStart.text;
      }
    }
  }
}
