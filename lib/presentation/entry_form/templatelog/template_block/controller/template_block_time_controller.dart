import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_date_time_form_controller.dart';
import 'package:flutter/material.dart';

class TemplateBlockTimeFormController
    extends TemplateBlockDateTimeFormController {
  TemplateBlockTimeFormController(
      super.templateBlock, super.dateTimeFormat, super.onShowPicker);

  @override
  get dbValue {
    return value.value == null
        ? null
        : value.value!.hour * 100 + value.value!.minute;
  }

  @override
  void read(
    EntryFormReadFunction<TextEditingValue> onRead,
    CustomFieldMetadata? metadata,
    CustomFieldMetadata? lastMetadata,
    String emptyString,
  ) {
    final readValue = metadata?.displayValue ?? '';
    final lastValue = lastMetadata?.displayValue ?? '';
    onRead(
      value.inputController,
      TextEditingValue(text: readValue),
      TextEditingValue(text: lastValue),
    );
  }
}
