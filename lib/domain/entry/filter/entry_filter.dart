import 'package:bitacora/domain/entry/value/entry_signature_status.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/user/user.dart';

class EntryFilter {
  final String? searchQuery;
  final String? titleQuery;
  final String? commentsQuery;
  final Set<Project> projects;
  final Set<String> sublocations;
  final Set<Tag> tags;
  final Set<String> providers;
  final Set<User> authors;
  final Set<User> assignee;
  final Set<User> signee;
  final Set<EntrySignatureStatus> signatureStatus;

  EntryFilter({
    this.searchQuery,
    this.titleQuery,
    this.commentsQuery,
    this.projects = const {},
    this.sublocations = const {},
    this.tags = const {},
    this.providers = const {},
    this.authors = const {},
    this.assignee = const {},
    this.signee = const {},
    this.signatureStatus = const {},
  });
}
