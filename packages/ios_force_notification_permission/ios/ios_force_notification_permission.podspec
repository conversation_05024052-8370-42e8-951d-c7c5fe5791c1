#
# To learn more about a Podspec see http://guides.cocoapods.org/syntax/podspec.html.
# Run `pod lib lint ios_force_notification_permission.podspec` to validate before publishing.
#
Pod::Spec.new do |s|
  s.name             = 'ios_force_notification_permission'
  s.version          = '0.0.1'
  s.license      = { :type => 'MIT' }
  s.summary          = 'Force iOS notification permission request.'
  s.description      = <<-DESC
Force iOS notification permission request for Bitacora.io iOS users.
                       DESC
  s.homepage         = 'https://github.com/Bitacora-io/bitacora_flutter/'
  s.authors      = { 'Joseph León' => '<EMAIL>' }
  s.source           = { :path => '.' }
  s.source_files = 'Classes/**/*'
  s.dependency 'Flutter'
  s.platform = :ios, '11.0'

  # Flutter.framework does not contain a i386 slice.
  s.pod_target_xcconfig = { 'DEFINES_MODULE' => 'YES', 'EXCLUDED_ARCHS[sdk=iphonesimulator*]' => 'i386' }
  s.swift_version = '5.0'
end
