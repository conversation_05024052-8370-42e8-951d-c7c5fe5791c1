import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/quick_action_button/value/quick_action_button_background_color.dart';
import 'package:bitacora/domain/quick_action_button/value/quick_action_button_foreground_color.dart';
import 'package:bitacora/domain/quick_action_button/value/quick_action_button_icon.dart';
import 'package:bitacora/domain/quick_action_button/value/quick_action_button_name.dart';
import 'package:bitacora/domain/quick_action_button/value/quick_action_button_payload.dart';
import 'package:bitacora/domain/quick_action_button/value/quick_action_button_type.dart';

export 'package:bitacora/domain/quick_action_button/value/quick_action_button_icon.dart';
export 'package:bitacora/domain/quick_action_button/value/quick_action_button_name.dart';
export 'package:bitacora/domain/quick_action_button/value/quick_action_button_payload.dart';
export 'package:bitacora/domain/quick_action_button/value/quick_action_button_type.dart';
export 'package:bitacora/domain/quick_action_button/value/quick_action_button_background_color.dart';
export 'package:bitacora/domain/quick_action_button/value/quick_action_button_foreground_color.dart';

class QuickActionButton extends Model {
  final QuickActionButtonType? type;
  final QuickActionButtonName? name;
  final QuickActionButtonIcon? icon;
  final QuickActionButtonPayload? payload;
  final QuickActionButtonBackgroundColor? backgroundColor;
  final QuickActionButtonForegroundColor? foregroundColor;

  const QuickActionButton({
    this.type,
    this.name,
    this.icon,
    this.payload,
    this.backgroundColor,
    this.foregroundColor,
  });

  @override
  Map<Field, dynamic> get fields => {
        QuickActionButtonField.type: type,
        QuickActionButtonField.name: name,
        QuickActionButtonField.icon: name,
        QuickActionButtonField.metadata: name,
        QuickActionButtonField.backgroundColor: backgroundColor,
        QuickActionButtonField.foregroundColor: foregroundColor,
      };
}

enum QuickActionButtonField with Field {
  type,
  name,
  icon,
  metadata,
  backgroundColor,
  foregroundColor,
}
