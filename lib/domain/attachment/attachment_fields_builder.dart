import 'package:bitacora/domain/common/repository_query_context.dart';

abstract class AttachmentFieldsBuilder {
  AttachmentFieldsBuilder s3Key();

  AttachmentFieldsBuilder name();

  AttachmentFieldsBuilder isUploaded();

  AttachmentFieldsBuilder isDownloaded();

  AttachmentFieldsBuilder transferState();

  AttachmentFieldsBuilder transferAttempts();

  AttachmentFieldsBuilder path();

  AttachmentFieldsBuilder comments();

  AttachmentFieldsBuilder doodle();

  AttachmentFieldsBuilder entry(Fields fields);

  Fields build();
}
