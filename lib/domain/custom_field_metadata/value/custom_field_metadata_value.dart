import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/domain/custom_field/value/custom_field_type.dart';
import 'package:bitacora/util/date_utils.dart';

class CustomFieldMetadataValue extends ValueObject {
  CustomFieldMetadataValue(super._value);

  factory CustomFieldMetadataValue.fromApiValue(
    dynamic value,
    CustomFieldTypeValue type,
  ) {
    switch (type) {
      case CustomFieldTypeValue.time:
        return CustomFieldMetadataValue(
            value == null ? null : int.parse(value));
      case CustomFieldTypeValue.dateTime:
      case CustomFieldTypeValue.date:
        return CustomFieldMetadataValue(
            value == null ? null : getDateTimeFromApi(value, true));
      case CustomFieldTypeValue.checkbox:
        return CustomFieldMetadataValue(value ?? 0);
      case CustomFieldTypeValue.select:
      case CustomFieldTypeValue.radio:
        if (value is Map) {
          return CustomFieldMetadataValue(null);
        }
        return CustomFieldMetadataValue(value);
      default:
        return CustomFieldMetadataValue(value);
    }
  }

  @override
  bool isSameType(Object other) {
    return other is CustomFieldMetadataValue;
  }

  @override
  dynamic get dbValue {
    switch (value) {
      case DateTime dateTime:
        return dateTime.microsecondsSinceEpoch;
      case bool boolean:
        return boolean ? 1 : 0;
      default:
        return value;
    }
  }

  @override
  dynamic get apiValue {
    switch (value) {
      case DateTime dateTime:
        return dateTime.millisecondsSinceEpoch;
      default:
        return value;
    }
  }
}
