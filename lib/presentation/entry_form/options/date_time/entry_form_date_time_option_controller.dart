import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/non_editable_entry_form_open_state_option.dart';
import 'package:bitacora/presentation/entry_form/options/entry_form_option_controller.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/form/form_has_changes.dart';
import 'package:bitacora/util/time_utils.dart';
import 'package:flutter/material.dart';

class EntryFormDateTimeOptionController extends EntryFormOptionController
    with FormHasChanges {
  final EntryFormProps props;
  final ValueNotifier<bool> isScheduleEntry = ValueNotifier(false);

  final ValueNotifier<bool> isProgressive = ValueNotifier(false);
  final TextEditingController openStart = TextEditingController();
  final TextEditingController openEnd = TextEditingController();

  final TextEditingController startDate = TextEditingController();
  final TextEditingController endDate = TextEditingController();
  final TextEditingController startTime = TextEditingController();
  final TextEditingController endTime = TextEditingController();

  EntryFormDateTimeOptionController(this.props, ValueNotifier<Entry?> liveEntry)
      : super(liveEntry) {
    liveEntry.addListener(_read);
    startDate.addListener(_onStartFieldChanged);
    endDate.addListener(_onEndFieldChanged);
    startTime.addListener(_onStartFieldChanged);
    endTime.addListener(_onEndFieldChanged);
    openStart.addListener(_onStartFieldChanged);
    openEnd.addListener(_onEndFieldChanged);
    isScheduleEntry.addListener(_onIsScheduleEntryChanged);
    _read();
    _checkHasData();
    isShowing.value = hasData.value;

    monitorChanges();
  }

  @override
  Map<ValueNotifier, ValueObject?> get fields => {
        startDate: liveEntry.value?.startDate,
        endDate: liveEntry.value?.endDate,
        startTime: liveEntry.value?.startTime,
        endTime: liveEntry.value?.endTime,
        openStart: null,
        openEnd: null,
      };

  @override
  void dispose() {
    isScheduleEntry.dispose();
    isProgressive.dispose();
    openStart.dispose();
    openEnd.dispose();
    startDate.dispose();
    endDate.dispose();
    startTime.dispose();
    endTime.dispose();

    super.dispose();
  }

  void _read() {
    if (liveEntry.value == null) {
      return;
    }

    isScheduleEntry.value = liveEntry.value?.openState != null;

    final startDateFormatted = liveEntry.value!.startDate?.value == null
        ? ''
        : liveEntry.value!.startDate!.displayValue;
    final endDateFormatted = liveEntry.value!.endDate?.value == null
        ? ''
        : liveEntry.value!.endDate!.displayValue;

    final startTimeFormatted = liveEntry.value!.startTime?.value == null
        ? ''
        : liveEntry.value!.startTime!.displayValue;

    final endTimeFormatted = liveEntry.value!.endTime?.value == null
        ? ''
        : liveEntry.value!.endTime!.displayValue;

    startDate.text = startDateFormatted;
    endDate.text = endDateFormatted;
    startTime.text = startTimeFormatted;
    endTime.text = endTimeFormatted;
  }

  void _checkHasData() {
    if (isScheduleEntry.value) {
      hasData.value = liveEntry.value != null || openStart.text.isNotEmpty;
    } else {
      hasData.value = startDate.text.isNotEmpty ||
          endDate.text.isNotEmpty ||
          startTime.text.isNotEmpty ||
          endTime.text.isNotEmpty;
    }
  }

  @override
  MaterialColor get color => bitacoraPurple;

  @override
  IconData get icon => Icons.schedule;

  @override
  Widget form(BuildContext context) {
    if (liveEntry.value?.openState != null) {
      return NonEditableEntryOpenStateOption(controller: this);
    }

    return EntryFormDateTimeOption(
      controller: this,
      isOpenStateAllowed: _isOpenStateAllowed,
    );
  }

  bool get _isOpenStateAllowed =>
      liveEntry.value == null ||
      (props.isFromQr && liveEntry.value?.id == null);

  OpenState? buildOpenStateForSave() {
    if (!isScheduleEntry.value ||
        openStart.text.isEmpty ||
        !_isOpenStateAllowed) {
      return null;
    }

    final startDay =
        getLogDayFromDateTime(kLogDayDisplayDateFormat.parse(openStart.text));
    return OpenState(
      progress: const OpenStateProgress(0),
      progressive: OpenStateProgressive(isProgressive.value),
      startDay: OpenStateStartDay(startDay),
      endDay: OpenStateEndDay(openEnd.text.isEmpty
          ? startDay
          : getLogDayFromDateTime(
              kLogDayDisplayDateFormat.parse(openEnd.text))),
    );
  }

  void _onStartFieldChanged() {
    _maybeResolveDateConflict();

    _checkHasData();
  }

  void _onEndFieldChanged() {
    _maybeResolveDateConflict(takeStart: false);
  }

  void _onIsScheduleEntryChanged() {
    _checkHasData();
  }

  void _maybeResolveDateConflict({
    bool takeStart = true,
  }) {
    try {
      final startText = isScheduleEntry.value ? openStart : startDate;
      final endText = isScheduleEntry.value ? openEnd : endDate;

      if (startText.text.isEmpty || endText.text.isEmpty) {
        _maybeResolveTimeConflict();
        return;
      }

      if (startText.text == endText.text) {
        _maybeResolveTimeConflict();
      }

      var start = kLogDayDisplayDateFormat.parse(startText.text);
      var end = kLogDayDisplayDateFormat.parse(endText.text);

      if (!end.isBefore(start)) {
        return;
      }

      if (takeStart) {
        endText.text = startText.text;
      } else {
        startText.text = endText.text;
      }
    } catch (_) {
      return;
    }
  }

  void _maybeResolveTimeConflict({bool takeStart = true}) {
    try {
      final start = startTime.text.isNotEmpty
          ? kTimeOfDayDateFormat.parse(startTime.text)
          : null;
      final end = endTime.text.isNotEmpty
          ? kTimeOfDayDateFormat.parse(endTime.text)
          : null;

      if (start != null && end != null) {
        if (!end.isBefore(start)) {
          return;
        }

        if (takeStart) {
          endTime.text = startTime.text;
        } else {
          startTime.text = endTime.text;
        }
      }
    } catch (_) {
      return;
    }
  }
}
