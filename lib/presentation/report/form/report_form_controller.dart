import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/presentation/entry/filter/entry_filter_controller.dart';
import 'package:bitacora/presentation/report/form/customize/report_form_customize_controller.dart';
import 'package:bitacora/presentation/report/form/filters/report_form_filters_controller.dart';
import 'package:bitacora/presentation/report/form/header/report_form_header_controller.dart';
import 'package:bitacora/presentation/report/form/include/report_form_include_controller.dart';
import 'package:bitacora/presentation/report/form/last_report_repository_query.dart';
import 'package:bitacora/presentation/report/form/report_form_page.dart';
import 'package:bitacora/presentation/report/form/template/report_form_template_controller.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';

class ReportFormController {
  final GlobalKey<FormState> formKey = GlobalKey<FormState>();

  final ReportFormHeaderController headerController;
  final ReportFormTemplateController templateController =
      ReportFormTemplateController();
  final ReportFormIncludeController includeController =
      ReportFormIncludeController();
  final ReportFormCustomizeController customizeController =
      ReportFormCustomizeController();
  late final ReportFormFiltersController filtersController;

  ReportFormController(
    BuildContext context, {
    required ReportsFormPageProps props,
  }) : headerController = ReportFormHeaderController(context) {
    filtersController = ReportFormFiltersController(
      context,
      headerController.format,
      headerController.entryType,
      EntryFilterController(
        entryFilter: props.inputReport != null ? null : props.entryFilter,
      ),
    );

    _setDefaultValues(context, props);
  }

  Future<void> _setDefaultValues(
    BuildContext context,
    ReportsFormPageProps props,
  ) async {
    final db = context.read<Repository>();
    final activeOrganization = context.read<ActiveOrganization>();
    final defaultReport =
        props.inputReport ?? await db.query(LastReportRepositoryQuery());
    await headerController.setDefaultValues(
        db, defaultReport, props.selectedEntries);

    if (defaultReport == null) {
      return;
    }

    templateController.setDefaultValues(defaultReport);

    if (props.inputReport != null) {
      customizeController.setDefaultValues(defaultReport);
      includeController.setDefaultValues(defaultReport);
      filtersController.setDefaultValues(db, activeOrganization, defaultReport);
    }
  }

  ReportPostParams buildPostParams(BuildContext context) {
    if (headerController.isExcel) {
      return ReportPostParams.fromMap({});
    }
    return ReportPostParams.fromMap({
      ...headerController.getApiPostParams(context),
      ...customizeController.getApiPostParams(context),
      ...templateController.apiPostParams,
      'open_entries': false,
      'is_preview': false,
    });
  }

  ReportGetParams buildGetParams(BuildContext context) {
    return headerController.isExcel
        ? _buildExcelGetParams(context)
        : _buildPdfGetParams();
  }

  ReportGetParams _buildPdfGetParams() {
    return ReportGetParams.fromMap(<String, dynamic>{
      ...includeController.getApiGetParams(customizeController),
      ...filtersController.apiGetParams,
    });
  }

  ReportGetParams _buildExcelGetParams(BuildContext context) {
    return ReportGetParams.fromMap(<String, dynamic>{
      ...headerController.getApiPostParams(context),
      ...filtersController.apiGetParams,
    });
  }
}
