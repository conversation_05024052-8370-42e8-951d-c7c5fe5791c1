import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/auth_app_service.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/infrastructure/attachment/has_pending_attachment_uploads_repository_query.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/presentation/theme/active_theme_mode.dart';
import 'package:bitacora/presentation/user_settings/has_pending_outgoing_mutations_repository_query.dart';
import 'package:bitacora/presentation/user_settings/speech_to_text_settings.dart';
import 'package:bitacora/presentation/user_settings/user_settings_page.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/restart_widget.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:path_provider_platform_interface/path_provider_platform_interface.dart';
import 'package:provider/provider.dart';

import '../../application/cache/auth/mocks.dart';
import '../../application/cache/organization/mocks.dart';
import '../../application/mocks.dart';
import '../../domain/auth/mocks.dart';
import '../../domain/common/mocks.dart';
import '../../mocks.dart';
import '../../mocktail_fallback_values.dart';

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
    PathProviderPlatform.instance = MockPathProvider();
  });

  Widget testUserSettingsPage({
    Widget? widget,
    Repository? repository,
    AuthRepository? authRepository,
    AuthAppService? authAppService,
  }) {
    return RestartWidget(
      child: MultiProvider(
        providers: [
          Provider<Repository>(create: (_) {
            repository ??= MockRepository();
            when(
              () => repository!.context(queryScope: any(named: 'queryScope')),
            ).thenReturn(DbContext(db: DbRepository()));
            when(
              () => repository!.queryScope(orgId: any(named: 'orgId')),
            ).thenReturn(QueryScope(orgId: mockOrganization().id));
            when(
              () => repository!.queryScope(userId: any(named: 'userId')),
            ).thenReturn(QueryScope(orgId: mockUser().id));
            return repository as Repository;
          }),
          Provider<AuthAppService>(
              create: (context) => authAppService ?? mockAuthAppService()),
          ChangeNotifierProvider<AuthRepository>(
              create: (_) => authRepository ?? MockAuthRepository()),
          ChangeNotifierProvider<ActiveSession>(
              create: (_) => mockActiveSession(session: mockSession())),
          ChangeNotifierProvider<ActiveOrganization>(
              create: (_) =>
                  mockActiveOrganization(organization: mockOrganization())),
          ChangeNotifierProvider<ActiveThemeMode>(
              create: (_) => ActiveThemeMode()),
        ],
        child: MaterialApp(
          home: Localizations(
              locale: const Locale('en'),
              delegates: AppLocalizations.localizationsDelegates,
              child: widget ?? const UserSettingsPage()),
        ),
      ),
    );
  }

  Future<void> testLogoutMessage(
    WidgetTester tester,
    bool hasPendingOutgoingMutations,
    bool hasPendingAttachmentUploads,
  ) async {
    final db = MockRepository();
    when(
      () => db.query(
        const HasPendingOutgoingMutationsRepositoryQuery(
          ignoreRetryLimit: true,
        ),
        context: any(named: 'context'),
      ),
    ).thenAnswer((_) => Future.value(hasPendingOutgoingMutations));
    when(
      () => db.query(
        const HasPendingAttachmentUploadsRepositoryQuery(),
        context: any(named: 'context'),
      ),
    ).thenAnswer((_) => Future.value(hasPendingAttachmentUploads));
    const widget = UserSettingsPage();
    await tester
        .pumpWidget(testUserSettingsPage(widget: widget, repository: db));

    await tester.tap(find.byIcon(Icons.logout));
    await tester.pumpAndSettle();

    expect(
      find.text(
        'Are you sure that you want to log out?\n\n*You have local changes, they will be permanently lost.',
      ),
      hasPendingAttachmentUploads || hasPendingOutgoingMutations
          ? findsOneWidget
          : findsNothing,
    );
  }

  group('$UserSettingsPage tests', () {
    testWidgets('Display page', (tester) async {
      final appConfig = mockAppConfig(isSpeechToTextEnabled: true);

      withInjected<AppConfig>(appConfig, () async {
        await tester.pumpWidget(testUserSettingsPage());

        expect(find.text('User settings'), findsOneWidget);
        expect(find.text('Theme'), findsOneWidget);
        expect(find.text('System'), findsOneWidget);
        expect(find.text('Light'), findsOneWidget);
        expect(find.text('Dark'), findsOneWidget);
        expect(find.byType(SpeechToTextSettings), findsOneWidget);
        expect(find.byIcon(Icons.logout), findsOneWidget);
      });
    });

    testWidgets('Without Stt settings', (tester) async {
      await tester.pumpWidget(testUserSettingsPage());

      expect(find.text('User settings'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
      expect(find.text('System'), findsOneWidget);
      expect(find.text('Light'), findsOneWidget);
      expect(find.text('Dark'), findsOneWidget);
      expect(find.byType(SpeechToTextSettings), findsNothing);
      expect(find.byIcon(Icons.logout), findsOneWidget);
    });

    testWidgets(
      'Logout when exist record pending sync',
      (tester) async {
        await testLogoutMessage(tester, true, false);
      },
    );

    testWidgets(
      'Logout when attachment is uploading',
      (tester) async {
        await testLogoutMessage(tester, false, true);
      },
    );

    testWidgets(
        'Logout when when attachment is uploading and  exist record pending sync',
        (tester) async {
      await testLogoutMessage(tester, true, true);
    });

    testWidgets('Logout when no local changes', (tester) async {
      await testLogoutMessage(tester, false, false);
    });

    testWidgets('Nuke when logout', (tester) async {
      final authAppService = mockAuthAppService();
      final db = MockRepository();
      when(
        () => db.query(
          const HasPendingOutgoingMutationsRepositoryQuery(
            ignoreRetryLimit: true,
          ),
          context: any(named: 'context'),
        ),
      ).thenAnswer((_) => Future.value(false));
      when(
        () => db.query(
          const HasPendingAttachmentUploadsRepositoryQuery(),
          context: any(named: 'context'),
        ),
      ).thenAnswer((_) => Future.value(false));
      await tester.pumpWidget(testUserSettingsPage(
        repository: db,
        authAppService: authAppService,
      ));
      await tester.tap(find.byIcon(Icons.logout));
      await tester.pumpAndSettle();

      await tester.tap(
          find.textContaining(RegExp('LoGoUt', caseSensitive: false)).last);
      await tester.pumpAndSettle();

      verify(() => authAppService.logout(any())).called(1);
    });
  });
}
