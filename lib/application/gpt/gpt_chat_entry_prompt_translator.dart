import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/domain/personnellog/personnellog.dart';
import 'package:bitacora/domain/worklog/worklog.dart';

class GptChatEntryPromptTranslator {
  String toNaturalLanguage(Entry entry) {
    switch (entry.extension!.extensionType) {
      case ExtensionType.simplelog:
      case ExtensionType.worklog:
        return _worklogTranslate(entry.worklog!);
      case ExtensionType.inventorylog:
        return _inventorylogTranslate(entry.inventorylog!);
      case ExtensionType.personnellog:
        return _personnellogTranslate(entry.personnellog!);
      case ExtensionType.templatelog:
      case ExtensionType.progresslog:
        throw '$GptChatEntryPromptTranslator: '
            '${entry.extension!.extensionType} not supported';
    }
  }

  String _worklogTranslate(Worklog worklog) {
    return '''
${worklog.title!.displayValue} en el proyecto ${worklog.project!.name!.value}.
Tipo de actividad: ${worklog.type!.localizedDisplayValue}''';
  }

  String _inventorylogTranslate(Inventorylog inventorylog) {
    final itemName = inventorylog.itemName!.value;
    final quantity = inventorylog.quantity!.displayValue;
    final sourceProject = inventorylog.sourceProject?.name?.value;
    final destProject = inventorylog.destProject?.name?.value;

    switch (inventorylog.type!.value) {
      case InventorylogTypeValue.incoming:
        return 'Entrada de $quantity de $itemName desde $sourceProject';
      case InventorylogTypeValue.movement:
        return 'Movimiento de $quantity de $itemName desde $sourceProject a $destProject';
      case InventorylogTypeValue.outgoing:
        return 'Saida de $quantity de $itemName a $destProject';
    }
  }

  String _personnellogTranslate(Personnellog personnellog) {
    final minutes = personnellog.minutes?.inHours;
    final entrance = personnellog.entrance?.displayValue;
    final exit = personnellog.exit?.displayValue;
    var prompt = '${personnellog.name} trabajó ';
    if (minutes != null) {
      prompt += '$minutes horas ';
    } else {
      if (entrance != null) {
        prompt += 'desde las $entrance ';
      }
      if (entrance != null) {
        prompt += 'hasta las $exit ';
      }
    }
    prompt += 'en ${personnellog.project!.name!.displayValue}';
    return prompt;
  }
}
