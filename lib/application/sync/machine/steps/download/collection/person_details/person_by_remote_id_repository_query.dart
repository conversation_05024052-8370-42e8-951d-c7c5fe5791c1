import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/person/person.dart';

class PersonByRemoteIdRepositoryQuery extends RepositoryQuery<Person?> {
  final RemoteId remoteId;

  const PersonByRemoteIdRepositoryQuery(this.remoteId);

  @override
  Future<Person?> run(RepositoryQueryContext context) =>
      context.db.person.findByRemoteId(context, remoteId);

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.person.fieldsBuilder.build();
}
