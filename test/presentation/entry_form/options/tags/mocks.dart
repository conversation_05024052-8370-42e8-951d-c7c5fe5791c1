import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/presentation/entry_form/options/tags/entry_form_tag_option_controller.dart';
import 'package:bitacora/util/collection_selection/collection_selection.dart';
import 'package:flutter/cupertino.dart';
import 'package:mocktail/mocktail.dart';

import '../../util/mocks.dart';

class MockEntryFormTagOptionController extends Mock
    implements EntryFormTagOptionController {}

EntryFormTagOptionController mockEntryFormTagOptionController({
  ValueNotifier<List<Tag>>? tags,
  CollectionSelection<Tag>? collectionSelection,
}) {
  final mock = MockEntryFormTagOptionController();
  collectionSelection ??= mockCollectionSelection();
  when(() => mock.tags).thenReturn(tags ?? ValueNotifier(<Tag>[]));
  when(() => mock.selection).thenReturn(collectionSelection);
  when(() => mock.addTag(any())).thenReturn(null);
  when(() => mock.deleteSelection()).thenAnswer((_) => Future.value());
  when(() => mock.clearSelection()).thenAnswer((_) => Future.value());
  return mock;
}
