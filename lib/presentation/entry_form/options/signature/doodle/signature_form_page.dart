import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/presentation/login/validators.dart';
import 'package:bitacora/presentation/widgets/text_field_done.dart';
import 'package:bitacora/util/bitacora_icons.dart';
import 'package:bitacora/util/parent_builder/parent_builder.dart';
import 'package:bitacora/util/trim_text_on_focus_lost.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:hand_signature/signature.dart';
import 'package:provider/provider.dart';

const _kTextFormFieldScrollPadding = EdgeInsets.only(bottom: 800);

class SignatureFormPage extends StatefulWidget {
  final Signature? signature;

  const SignatureFormPage({super.key, this.signature});

  @override
  State<SignatureFormPage> createState() => _SignatureFormPageState();
}

class _SignatureFormPageState extends State<SignatureFormPage> {
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  final GlobalKey _bodyKey = GlobalKey();
  final TextEditingController _ownerNameController = TextEditingController();
  final FocusNode _ownerNameFocusNode = FocusNode();
  final TextEditingController _ownerEmailController = TextEditingController();
  final FocusNode _ownerEmailFocusNode = FocusNode();
  final TextEditingController _commentsController = TextEditingController();
  final FocusNode _commentsFocusNode = FocusNode();
  final ValueNotifier<SignatureStatus> _signatureStatusNotifier =
      ValueNotifier(SignatureStatus.approved);
  final HandSignatureControl _handSignatureControl = HandSignatureControl(
    smoothRatio: 0.65,
    velocityRange: 2.0,
  );
  final ValueNotifier<bool> _hasSignature = ValueNotifier(false);
  double? _bodyHeight;
  double _bottomPadding = 0;
  bool _isTextFieldFocused = false;

  bool get _isEditable => widget.signature == null;

  @override
  void initState() {
    super.initState();

    if (_isEditable) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        setState(() {
          _bodyHeight = _bodyKey.currentContext!.size!.height;
          _bottomPadding = MediaQuery.paddingOf(context).bottom;
        });
      });
    }

    _read();
  }

  @override
  void dispose() {
    if (_isEditable) {
      _handSignatureControl.removeListener(_handSignatureListener);
    }
    super.dispose();
  }

  void _read() {
    if (_isEditable) {
      _handSignatureControl.addListener(_handSignatureListener);
      final owner = context.read<ActiveSession>().value!.user;
      _ownerNameController.text = owner.name!.displayValue;
      _ownerEmailController.text = owner.email!.displayValue;
    } else {
      _ownerNameController.text = widget.signature!.ownerName!.displayValue;
      _ownerEmailController.text = widget.signature!.ownerEmail!.displayValue;
      _commentsController.text =
          widget.signature!.comments!.displayValue.isEmpty
              ? '-'
              : widget.signature!.comments!.displayValue;
      _signatureStatusNotifier.value = widget.signature!.status!;
    }
  }

  void _handSignatureListener() {
    _hasSignature.value = _handSignatureControl.lines.isNotEmpty;
  }

  bool get _hasFocus =>
      _ownerNameFocusNode.hasFocus ||
      _ownerEmailFocusNode.hasFocus ||
      _commentsFocusNode.hasFocus;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        actions: !_isEditable
            ? null
            : [
                ValueListenableBuilder<bool>(
                  valueListenable: _hasSignature,
                  builder: (context, _, __) {
                    return IconButton(
                      icon: const Icon(Icons.check),
                      onPressed: _hasSignature.value
                          ? () {
                              if (!_formKey.currentState!.validate()) {
                                return;
                              }

                              Navigator.of(context).pop(_buildSignature());
                            }
                          : null,
                      color: Theme.of(context).colorScheme.primary,
                    );
                  },
                ),
              ],
      ),
      body: Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: Theme.of(context).inputDecorationTheme.copyWith(
                errorStyle: const TextStyle(height: 0, fontSize: 0),
              ),
        ),
        child: ParentBuilder(
          key: _bodyKey,
          builder: (context, child) {
            if (_bodyHeight == null) {
              return child;
            }

            final currentBottomPadding = MediaQuery.paddingOf(context).bottom;
            return SingleChildScrollView(
              child: SizedBox(
                  height: currentBottomPadding == _bottomPadding
                      ? _bodyHeight
                      : (_bodyHeight! + currentBottomPadding - _bottomPadding),
                  child: child),
            );
          },
          child: Container(
            color: Theme.of(context).colorScheme.surfaceContainerHighest,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Expanded(
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      if (_isEditable)
                        Positioned(
                          bottom: 16.0,
                          right: 16.0,
                          child: Icon(
                            BitacoraIcons.signature,
                            color: Theme.of(context)
                                .colorScheme
                                .primary
                                .withValues(alpha: .5),
                          ),
                        ),
                      Positioned(
                        top: 0,
                        bottom: 0,
                        left: 32.0,
                        right: 32.0,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Flexible(flex: 2, child: SizedBox()),
                            Divider(
                              color: Theme.of(context).colorScheme.primary,
                            ),
                            ValueListenableBuilder(
                                valueListenable: _ownerNameController,
                                builder: (context, _, __) {
                                  return Text(
                                    _ownerNameController.text,
                                    style: TextStyle(
                                        fontWeight: FontWeight.w300,
                                        color: Theme.of(context)
                                            .colorScheme
                                            .primary),
                                  );
                                }),
                            const Flexible(flex: 1, child: SizedBox()),
                          ],
                        ),
                      ),
                      Positioned.fill(
                        child: _isEditable
                            ? ParentBuilder(
                                builder: (context, child) {
                                  if (!_isTextFieldFocused) {
                                    return child;
                                  }

                                  return IgnorePointer(child: child);
                                },
                                child: HandSignature(
                                  control: _handSignatureControl,
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                  type: SignatureDrawType.shape,
                                  maxWidth: 7.0,
                                ),
                              )
                            : Padding(
                                padding: const EdgeInsets.symmetric(
                                    horizontal: 32.0),
                                child: SvgPicture.string(
                                    widget.signature!.doodle!.value!),
                              ),
                      ),
                      if (_isEditable)
                        Positioned(
                          top: 0.0,
                          right: 0.0,
                          child: ValueListenableBuilder<bool>(
                            valueListenable: _hasSignature,
                            builder: (context, _, __) {
                              return AnimatedOpacity(
                                opacity: _hasSignature.value ? 1.0 : 0.0,
                                duration: kThemeAnimationDuration,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    IconButton(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      icon: const Icon(Icons.undo),
                                      onPressed: _hasSignature.value
                                          ? _handSignatureControl.stepBack
                                          : null,
                                    ),
                                    IconButton(
                                      color:
                                          Theme.of(context).colorScheme.primary,
                                      icon: const Icon(Icons.delete),
                                      onPressed: _hasSignature.value
                                          ? _handSignatureControl.clear
                                          : null,
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                    ],
                  ),
                ),
                IgnorePointer(
                  ignoring: !_isEditable,
                  child: ParentBuilder(
                    builder: (context, child) {
                      if (_isEditable) {
                        return child;
                      }

                      return Theme(
                          data: Theme.of(context).copyWith(
                            inputDecorationTheme: const InputDecorationTheme(
                              contentPadding: EdgeInsets.only(),
                              border: InputBorder.none,
                              isDense: true,
                            ),
                          ),
                          child: child);
                    },
                    child: FocusScope(
                      onFocusChange: (focus) {
                        setState(() {
                          _isTextFieldFocused = _hasFocus;
                        });
                      },
                      child: GestureDetector(
                        onTap: () {
                          FocusScope.of(context).unfocus();
                        },
                        child: Container(
                          padding: const EdgeInsets.symmetric(horizontal: 16.0),
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(16.0),
                              topRight: Radius.circular(16.0),
                            ),
                          ),
                          child: Form(
                            key: _formKey,
                            child: Column(
                              children: [
                                const SizedBox(height: 16),
                                TextFormField(
                                  obscureText: false,
                                  autocorrect: false,
                                  keyboardType: TextInputType.name,
                                  textInputAction: TextInputAction.done,
                                  decoration: InputDecoration(
                                    labelText:
                                        AppLocalizations.of(context)!.name,
                                    alignLabelWithHint: true,
                                    suffixIcon: !_isEditable
                                        ? null
                                        : IconButton(
                                            icon: const Icon(Icons.clear),
                                            onPressed: () {
                                              _ownerNameController.clear();
                                            },
                                          ),
                                  ),
                                  scrollPadding: _kTextFormFieldScrollPadding,
                                  controller: _ownerNameController,
                                  focusNode: _ownerNameFocusNode,
                                  validator: (v) =>
                                      validateName(context, v, false),
                                  autofillHints: const [AutofillHints.email],
                                ),
                                const SizedBox(height: 16),
                                TextFormField(
                                  obscureText: false,
                                  autocorrect: false,
                                  keyboardType: TextInputType.emailAddress,
                                  textInputAction: TextInputAction.done,
                                  decoration: InputDecoration(
                                    labelText:
                                        AppLocalizations.of(context)!.email,
                                    alignLabelWithHint: true,
                                    suffixIcon: !_isEditable
                                        ? null
                                        : IconButton(
                                            icon: const Icon(Icons.clear),
                                            onPressed: () {
                                              _ownerEmailController.clear();
                                            },
                                          ),
                                  ),
                                  scrollPadding: _kTextFormFieldScrollPadding,
                                  controller: _ownerEmailController,
                                  focusNode: _ownerEmailFocusNode,
                                  validator: (v) =>
                                      validateEmail(context, v, false),
                                  autofillHints: const [AutofillHints.email],
                                ),
                                const SizedBox(height: 16),
                                DropdownButtonFormField<SignatureStatusValue>(
                                  iconSize: _isEditable ? 24.0 : 0.0,
                                  focusNode: FocusNode(canRequestFocus: false),
                                  decoration: InputDecoration(
                                      labelText:
                                          AppLocalizations.of(context)!.type),
                                  value: _signatureStatusNotifier.value.value,
                                  items: SignatureStatusValue.values
                                      .map(
                                        (e) => DropdownMenuItem<
                                            SignatureStatusValue>(
                                          value: e,
                                          child: Text(
                                            e.apiValue,
                                            style: TextStyle(
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .primary),
                                          ),
                                        ),
                                      )
                                      .toList(growable: false),
                                  onChanged: !_isEditable
                                      ? null
                                      : (value) {
                                          _signatureStatusNotifier.value =
                                              SignatureStatus(value!);
                                        },
                                ),
                                const SizedBox(height: 16),
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 8.0),
                                  child: TextFieldDone(
                                    focusNode: _commentsFocusNode,
                                    child: TrimTextOnFocusLost(
                                      focusNode: _commentsFocusNode,
                                      controller: _commentsController,
                                      child: TextFormField(
                                        focusNode: _commentsFocusNode,
                                        controller: _commentsController,
                                        minLines: 3,
                                        maxLines: null,
                                        keyboardType: TextInputType.multiline,
                                        textInputAction:
                                            TextInputAction.newline,
                                        textCapitalization:
                                            TextCapitalization.sentences,
                                        decoration: InputDecoration(
                                          labelText:
                                              AppLocalizations.of(context)!
                                                  .formComments,
                                          alignLabelWithHint: true,
                                        ),
                                        scrollPadding:
                                            _kTextFormFieldScrollPadding,
                                      ),
                                    ),
                                  ),
                                ),
                                SizedBox(
                                    height:
                                        MediaQuery.paddingOf(context).bottom)
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Signature _buildSignature() {
    return Signature(
      ownerEmail: SignatureOwnerEmail(_ownerEmailController.text),
      ownerName: SignatureOwnerName(_ownerNameController.text),
      comments: SignatureComments(_commentsController.text),
      status: _signatureStatusNotifier.value,
      doodle: SignatureDoodle(_handSignatureControl.toSvg(color: Colors.black)),
    );
  }
}
