import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/feed_post/feed_post.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation_fields_builder.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_failed_attempts.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_key.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_status_code.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/outgoing_mutation/outgoing_mutation_db_contract.dart';

class OutgoingMutationMissingModelError extends Error {
  final LocalId missingModelId;

  OutgoingMutationMissingModelError(this.missingModelId);
}

class OutgoingMutationDbFieldsBuilder extends DbFieldsBuilder
    implements OutgoingMutationFieldsBuilder {
  OutgoingMutationDbFieldsBuilder() {
    _id();
  }

  OutgoingMutationDbContract get contract => const OutgoingMutationDbContract();

  OutgoingMutationDbFieldsBuilder _id() {
    addField(
      OutgoingMutationField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  OutgoingMutationDbFieldsBuilder key() {
    addField(
      OutgoingMutationField.key,
      DbField(
        column: contract.key,
        valueBuilder: (v) => OutgoingMutationKey(v),
      ),
    );
    return this;
  }

  @override
  OutgoingMutationDbFieldsBuilder statusCode() {
    addField(
      OutgoingMutationField.statusCode,
      DbField(
        column: contract.statusCode,
        valueBuilder: (v) => OutgoingMutationStatusCode(v),
      ),
    );
    return this;
  }

  @override
  OutgoingMutationDbFieldsBuilder failedAttempts() {
    addField(
      OutgoingMutationField.failedAttempts,
      DbField(
        column: contract.failedAttempts,
        valueBuilder: (v) => OutgoingMutationFailedAttempts(v),
      ),
    );
    return this;
  }

  @override
  OutgoingMutationDbFieldsBuilder mutationType() {
    addField(
      OutgoingMutationField.mutationType,
      DbField(
        column: contract.mutationType,
        valueBuilder: (v) => MutationType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  OutgoingMutationDbFieldsBuilder emptyModel() {
    addField(
      OutgoingMutationField.model,
      DbField(
        columnAdder: (columns) => columns.addAll(
            [contract.modelType, contract.modelId, contract.modelRemoteId]),
        multiNestedBuilder: (context, values, _) async {
          final type =
              OutgoingMutationModelType.fromDbValue(values[contract.modelType]);
          final id = NullableLocalId(values[contract.modelId]);
          final remoteId = RemoteId(values[contract.modelRemoteId]);
          final model = EmptyModel(id: id, remoteId: remoteId);
          return OutgoingMutationModel(model, type);
        },
      ),
    );
    return this;
  }

  @override
  OutgoingMutationDbFieldsBuilder model(
      Map<OutgoingMutationModelType, Fields> fields) {
    addField(
      OutgoingMutationField.model,
      DbField(
        columnAdder: (columns) => columns.addAll(
            [contract.modelType, contract.modelId, contract.modelRemoteId]),
        multiNestedBuilder: (context, values, _) async {
          final modelType =
              OutgoingMutationModelType.fromDbValue(values[contract.modelType]);
          final modelId = values[contract.modelId] != null
              ? LocalId(values[contract.modelId])
              : null;
          final modelRemoteId = values[contract.modelRemoteId] != null
              ? RemoteId(values[contract.modelRemoteId])
              : null;

          final model = await _buildModel(
            context,
            values,
            modelType,
            modelId,
            modelRemoteId,
            fields[modelType]! as DbFields,
          );
          return OutgoingMutationModel(model, modelType);
        },
      ),
    );
    return this;
  }

  Future<Model> _buildModel(
    RepositoryQueryContext context,
    Map<String, dynamic> values,
    OutgoingMutationModelType modelType,
    LocalId? modelId,
    RemoteId? modelRemoteId,
    DbFields fields,
  ) async {
    final nestedContext = context.copyWith(fields: fields);
    final db = context.db;
    switch (modelType) {
      case OutgoingMutationModelType.worklog:
      case OutgoingMutationModelType.inventorylog:
      case OutgoingMutationModelType.personnellog:
      case OutgoingMutationModelType.progresslog:
      case OutgoingMutationModelType.templatelog:
        Entry? entry;
        if (modelId != null) {
          entry = await db.entry.find(nestedContext, modelId);
        }
        if (entry == null && modelRemoteId != null) {
          entry = Entry(remoteId: modelRemoteId);
        }
        if (entry == null) {
          throw OutgoingMutationMissingModelError(modelId!);
        }
        return entry;
      case OutgoingMutationModelType.locationTracking:
        LocationTracking? tracking;
        if (modelId != null) {
          tracking = await db.locationTracking.find(nestedContext, modelId);
        }
        if (tracking == null && modelRemoteId != null) {
          tracking = LocationTracking(remoteId: modelRemoteId);
        }
        if (tracking == null) {
          throw OutgoingMutationMissingModelError(modelId!);
        }
        return tracking;
      case OutgoingMutationModelType.feedPostRead:
        FeedPost? post;
        if (modelId != null) {
          post = await db.feedPost.find(nestedContext, modelId);
        }
        if (post == null && modelRemoteId != null) {
          post = FeedPost(remoteId: modelRemoteId);
        }
        if (post == null) {
          throw OutgoingMutationMissingModelError(modelId!);
        }
        return post;
      case OutgoingMutationModelType.signature:
        Signature? signature;
        if (modelId != null) {
          signature = await db.signature.find(nestedContext, modelId);
        }
        if (signature == null && modelRemoteId != null) {
          signature = Signature(remoteId: modelRemoteId);
        }
        if (signature == null) {
          throw OutgoingMutationMissingModelError(modelId!);
        }
        return signature;
      case OutgoingMutationModelType.entryGroup:
        EntryGroup? entryGroup;
        if (modelId != null) {
          entryGroup = await db.entryGroup.find(nestedContext, modelId);
        }
        if (entryGroup == null && modelRemoteId != null) {
          entryGroup = EntryGroup(remoteId: modelRemoteId);
        }
        if (entryGroup == null) {
          throw OutgoingMutationMissingModelError(modelId!);
        }
        return entryGroup;
      case OutgoingMutationModelType.entryGroupEntry:
        EntryGroupEntry? entryGroupEntry;
        if (modelId != null) {
          entryGroupEntry =
              await db.entryGroupEntry.find(nestedContext, modelId);
        }
        if (entryGroupEntry == null && modelRemoteId != null) {
          entryGroupEntry = EntryGroupEntry(remoteId: modelRemoteId);
        }
        if (entryGroupEntry == null) {
          throw OutgoingMutationMissingModelError(modelId!);
        }
        return entryGroupEntry;
      }
  }

  @override
  OutgoingMutationDbFieldsBuilder organization(Fields fields) {
    addField(
      OutgoingMutationField.organization,
      DbField(
        key: OutgoingMutationField.organization,
        column: contract.organizationId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.organization.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
