import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/dev/command/dev_command.dart';
import 'package:bitacora/dev/command/dev_command_bug_report.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/widgets.dart';

Map<DevCommand, bool> commands() => {
      DevCommandBugReport(): false,
    };

class HotReloadCommand extends StatefulWidget {
  final Widget child;

  const HotReloadCommand({super.key, required this.child});

  @override
  State<HotReloadCommand> createState() => _HotReloadCommandState();
}

class _HotReloadCommandState extends State<HotReloadCommand> {
  @override
  void reassemble() async {
    super.reassemble();

    if (!AppConfig().isDevToolsEnabled) {
      return;
    }

    final map = commands();
    final hasCommand = map.values.reduce((a, b) => a || b);
    if (!hasCommand) {
      return;
    }

    logger.d('dev:command Executing hot reload commands...');
    for (final command in map.keys) {
      if (map[command]!) {
        await command.run(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return widget.child;
  }
}
