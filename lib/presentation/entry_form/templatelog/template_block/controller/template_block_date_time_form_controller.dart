import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/value/template_block_form_date_time_input_value.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

class TemplateBlockDateTimeFormController<T>
    extends TemplateBlockFormController<DateTime?, TextEditingValue> {
  @override
  final TemplateBlockFormDateTimeInputValue value;

  final DateFormat dateTimeFormat;
  final Future<DateTime?> Function(BuildContext context, DateTime? currentValue)
      onShowPicker;

  @protected
  TemplateBlockDateTimeFormController(
      super.templateBlock, this.dateTimeFormat, this.onShowPicker)
      : value = TemplateBlockFormDateTimeInputValue(null, dateTimeFormat);

  @override
  int? get dbValue => value.value?.microsecondsSinceEpoch;

  @override
  void read(
    EntryFormReadFunction<TextEditingValue> onRead,
    CustomFieldMetadata? metadata,
    CustomFieldMetadata? lastMetadata,
    String emptyString,
  ) {
    final readValue = metadata?.displayValue ?? '';
    final lastValue = lastMetadata?.displayValue ?? '';
    onRead(
      value.inputController,
      TextEditingValue(text: readValue),
      TextEditingValue(text: lastValue),
    );
  }
}
