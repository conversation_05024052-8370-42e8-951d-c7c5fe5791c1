import 'dart:convert';

import 'package:bitacora/infrastructure/inventorylog/inventorylog_api_translator.dart';
import 'package:flutter_test/flutter_test.dart';

const _kInventorylogMovementJson =
    '{"quantity":2500,"created_at":"2022-06-23T19:04:46.974Z","item_name":"<PERSON><PERSON><PERSON><PERSON>","source_project_id":251452,"price_is_unit":0,"source_project_name":"1","source_sublocation":"A","updated_at":"2022-06-23T19:04:46.997Z","inventorylog_type":2,"dest_project_name":"2","id":86993,"dest_project_id":251449,"dest_sublocation":"B", "organization_id": 1020}';
const _kInventorylogIncomingJson =
    '{"quantity":100,"payment_status":0,"created_at":"2022-06-23T18:56:12.075Z","item_name":"Entrante","price_is_unit":0,"source_project_name":null,"updated_at":"2022-06-23T19:02:01.740Z","provider":"Office Depot","inventorylog_type":1,"dest_project_name":"3","id":86991,"dest_project_id":251451,"dest_sublocation":"M","cost_price":2500, "organization_id": 1020}';
const _kInventorylogOutgoingJson =
    '{"reason":"Razón ","quantity":25000,"payment_status":0,"created_at":"2022-06-23T18:58:13.354Z","item_name":"Saliente ","source_project_id":251459,"price_is_unit":0,"sale_price":1500,"source_project_name":"B","source_sublocation":"C","updated_at":"2022-06-23T19:03:50.195Z","provider":"Juan el de enfrente","inventorylog_type":3,"dest_project_name":null,"id":86992,"dest_sublocation":"", "organization_id": 1020}';

void main() {
  group('$InventorylogApiTranslator', () {
    test('FromMap Movement has source and dest project', () {
      const translator = InventorylogApiTranslator();
      final data = json.decode(_kInventorylogMovementJson);

      final inventorylog = translator.fromMap(data);

      expect(inventorylog.remoteId!.value, 86993);
      expect(inventorylog.destProject, isNotNull);
      expect(inventorylog.sourceProject, isNotNull);
    });

    test('FromMap Incoming has dest project', () {
      const translator = InventorylogApiTranslator();
      final data = json.decode(_kInventorylogIncomingJson);

      final inventorylog = translator.fromMap(data);

      expect(inventorylog.remoteId!.value, 86991);
      expect(inventorylog.destProject, isNotNull);
      expect(inventorylog.sourceProject, isNull);
    });

    test('FromMap Outgoing has source project', () {
      const translator = InventorylogApiTranslator();
      final data = json.decode(_kInventorylogOutgoingJson);

      final inventorylog = translator.fromMap(data);

      expect(inventorylog.remoteId!.value, 86992);
      expect(inventorylog.destProject, isNull);
      expect(inventorylog.sourceProject, isNotNull);
    });
  });
}
