import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/template_condition/template_condition.dart';
import 'package:bitacora/domain/template_condition/template_condition_fields_builder.dart';

abstract class TemplateConditionRepository<C extends RepositoryQueryContext,
        F extends TemplateConditionFieldsBuilder>
    extends RepositoryTable<TemplateCondition, C, F> {
  Future<List<TemplateCondition>> findAllByGroup(C context, LocalId groupId);

  Future<List<TemplateCondition>> findAllByBlock(C context, LocalId blockId);
}
