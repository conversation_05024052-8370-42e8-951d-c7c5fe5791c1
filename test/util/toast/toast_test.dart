import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../mocks.dart';
import 'mocks.dart';

void main() {
  BuildContext? context;
  Widget buildContext({required VoidCallback onTap}) {
    return MaterialApp(
      home: Localizations(
        locale: const Locale('en'),
        delegates: AppLocalizations.localizationsDelegates,
        child: Scaffold(
          body: Builder(builder: (c) {
            context = c;
            return GestureDetector(
              onTap: onTap,
              child: const Text('Tap me'),
            );
          }),
        ),
      ),
    );
  }

  group('$Toast test', () {
    test('Injects same', () {
      expect(Toast(), Toast());
    });

    testWidgets('showToast', (tester) async {
      const text = 'hello';
      await tester.pumpWidget(
        buildContext(onTap: () => Toast().showToast(context!, text)),
      );

      await _testToast(tester, text);
    });

    testWidgets('showToastWithPossibleDelay is not possible', (tester) async {
      const text = 'With delay';
      const startMillis = 820482285000;
      const differenceMillis = 400;
      final startTime = DateTime.fromMillisecondsSinceEpoch(820482285000);

      await tester.pumpWidget(
        buildContext(
          onTap: () => Toast().showToastWithPossibleDelay(
            startTime,
            text,
          ),
        ),
      );

      final now =
          DateTime.fromMillisecondsSinceEpoch(startMillis + differenceMillis);

      await withInjected<Clock>(
        mockClock(now),
        () => _testToast(
          tester,
          text,
          isFluttertoast: true,
        ),
      );
    });

    testWidgets('showToastWithPossibleDelay is possible', (tester) async {
      await tester.runAsync(() async {
        const text = 'With delay';
        const startMillis = 820482285000;
        const differenceMillis = 200;
        final startTime = DateTime.fromMillisecondsSinceEpoch(820482285000);
        await tester.pumpWidget(
          buildContext(
            onTap: () => Toast().showToastWithPossibleDelay(
              startTime,
              text,
            ),
          ),
        );
        final now =
            DateTime.fromMillisecondsSinceEpoch(startMillis + differenceMillis);

        await withInjected<Clock>(
          mockClock(now),
          () => tester.tap(find.text('Tap me')),
        );

        await _testToast(
          tester,
          text,
          duration: Duration(
            milliseconds:
                Toast.kMinToastAppearanceDurationMillis - differenceMillis,
          ),
          isFluttertoast: true,
        );
      });
    });

    testWidgets('showNetworkErrorToast without delay', (tester) async {
      const text = 'There was an issue with the network.';
      await tester.pumpWidget(
        buildContext(onTap: () => Toast().showNetworkErrorToast()),
      );

      await _testToast(tester, text, isFluttertoast: true);
    });

    testWidgets('showNetworkErrorToast with delay', (tester) async {
      await tester.runAsync(() async {
        const text = 'There was an issue with the network.';
        const startMillis = 820482285000;
        const differenceMillis = 200;
        final startTime = DateTime.fromMillisecondsSinceEpoch(820482285000);
        final clock = MockClock();
        when(() => clock.now()).thenReturn(DateTime.fromMillisecondsSinceEpoch(
            startMillis + differenceMillis));
        await tester.pumpWidget(
          buildContext(onTap: () => Toast().showNetworkErrorToast(startTime)),
        );

        await withInjected<Clock>(clock, () => tester.tap(find.text('Tap me')));

        await _testToast(
          tester,
          text,
          duration: Duration(
            milliseconds:
                Toast.kMinToastAppearanceDurationMillis - differenceMillis,
          ),
          isFluttertoast: true,
        );
      });
    });
  });
}

Future<void> _testToast(
  WidgetTester tester,
  String text, {
  bool isFluttertoast = false,
  Duration? duration,
}) async {
  if (isFluttertoast) {
    final fluttertoast = mockFluttertoastUtils();

    await withInjected(fluttertoast, () => tester.tap(find.text('Tap me')));

    verify(
      () => fluttertoast.showToast(
          msg: text, toastLength: any(named: 'toastLength')),
    );
    return;
  }

  await tester.tap(find.text('Tap me'));
  expect(find.text(text), findsNothing);
  expect(find.byType(SnackBar), findsNothing);
  if (duration != null) {
    await Future.microtask(() async {
      await Future.delayed(duration);
    });
  }
  await tester.pump();
  expect(find.text(text), findsOneWidget);
  expect(find.byType(SnackBar), findsOneWidget);
}
