import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/organization/organization_cache.dart';
import 'package:bitacora/application/entry_timer/entry_timer_handler.dart';
import 'package:bitacora/application/location_tracking/location_tracking_common_queries.dart';
import 'package:bitacora/application/location_tracking/location_tracking_handler.dart';
import 'package:bitacora/application/location_tracking/widget/location_tracking_monitor_subscription_notifier.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/template_block/template_block.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_navigator_props.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/widgets/timer/timer_widget.dart';
import 'package:bitacora/presentation/widgets/top_snack_bar.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/toast/toast.dart';
import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class LocationTrackingTopSnackbar extends StatefulWidget {
  final LocalId? scopeTrackingId;

  const LocationTrackingTopSnackbar({super.key, this.scopeTrackingId});

  @override
  State<LocationTrackingTopSnackbar> createState() =>
      _LocationTrackingTopSnackbarState();
}

class _LocationTrackingTopSnackbarState
    extends State<LocationTrackingTopSnackbar> {
  final LocationTrackingHandler _handler = const LocationTrackingHandler();
  final TimerWidgetController _timerController = TimerWidgetController();
  late final LocationTrackingMonitorSubscriptionNotifier
      _locationTrackingSubscriptionNotifier;

  LocationTracking? _currentTracking;
  bool isStopping = false;

  @override
  void initState() {
    super.initState();

    _locationTrackingSubscriptionNotifier =
        context.read<LocationTrackingMonitorSubscriptionNotifier>();

    _locationTrackingSubscriptionNotifier.addListener(_checkCurrentTracking);
    _checkCurrentTracking();
  }

  void _checkCurrentTracking() async {
    logger.i(
      'location-tracking-top-snack-bar: '
      'checkCurrentTracking [current ${_currentTracking?.id?.value}]',
    );
    final hasTracking = _locationTrackingSubscriptionNotifier.hasSubscription;

    if (!hasTracking) {
      logger.i('location-tracking-top-snack-bar:no tracking');
      _maybeSetCurrentTrackingNull();
      return;
    }

    if (isStopping) {
      logger.i(
        'location-tracking-top-snack-bar:checkCurrentTracking '
        'abort (isStopping)',
      );
      return;
    }

    final db = context.read<Repository>();
    final activeTracking = await db.query(
      const ActiveEntryLocationTrackingRepositoryQuery(),
    );

    if (!mounted) {
      return;
    }

    if (activeTracking == null) {
      _maybeSetCurrentTrackingNull();
      return;
    }
    logger.i('location-tracking-top-snack-bar:checkCurrentTracking '
        'active[${activeTracking.id?.value}]');

    final isLocal = activeTracking.isLocal!.value;
    if (_currentTracking?.id != activeTracking.id && isLocal) {
      if (widget.scopeTrackingId != null &&
          activeTracking.id != widget.scopeTrackingId) {
        return;
      }

      logger.i('location-tracking-top-snack-bar:checkCurrentTracking '
          'maybeStart [${activeTracking.id?.value}]');
      _currentTracking = activeTracking;
      final elapsed =
          EntryTimerHandler().getCurrentElapsed(_currentTracking!.entry!);
      _timerController.maybeStart(initialElapsed: elapsed, isTracking: true);
      setState(() {});
    }
  }

  void _maybeSetCurrentTrackingNull() {
    if (_currentTracking != null) {
      setState(() {
        _currentTracking = null;
      });
    }
  }

  @override
  void dispose() {
    _locationTrackingSubscriptionNotifier.removeListener(_checkCurrentTracking);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_currentTracking == null) {
      return const SizedBox();
    }

    return TopSnackBar(
      color: bitacoraRed,
      title: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  _buildTitle(),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Flexible(
                child: TimerWidget(
                  controller: _timerController,
                  textStyle: Theme.of(context).textTheme.bodyMedium,
                  textFormat: ' (#)',
                ),
              ),
            ],
          ),
          Text(
            AppLocalizations.of(context)!.locationTrackingActive,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
      action: OutlinedButton(
        onPressed: () => _onStopPressed(context),
        child: Text(AppLocalizations.of(context)!.stop),
      ),
      onTap: widget.scopeTrackingId != null
          ? null
          : () async {
              final activeOrganization = context.read<ActiveOrganization>();
              if (_currentTracking!.organization!.id !=
                  activeOrganization.value!.id) {
                Toast().showToast(
                  context,
                  AppLocalizations.of(context)!.locationTrackingFromAnotherOrg(
                      _currentTracking!.organization!.name!.displayValue),
                  action: SnackBarAction(
                    label: AppLocalizations.of(context)!.switch_,
                    onPressed: () {
                      final orgCache = context.read<OrganizationCache>();
                      final organization = orgCache.value!.firstWhere(
                        (e) => e.id == _currentTracking!.organization!.id,
                      );

                      activeOrganization.set(organization);
                    },
                  ),
                );
                return;
              }

              await EntryFormPage.navigate(
                EntryFormPageNavigatorProps(
                  context,
                  Entry(id: _currentTracking!.entry!.id!),
                ),
              );
            },
    );
  }

  String _buildTitle() {
    final extensionType = _currentTracking!.entry!.extension!.extensionType;
    switch (extensionType) {
      case ExtensionType.simplelog:
      case ExtensionType.worklog:
        return _currentTracking!.entry!.worklog!.title!.displayValue;
      case ExtensionType.templatelog:
        final templatelog = _currentTracking!.entry!.templatelog!;
        for (final group in templatelog.template!.groups!) {
          final blocks = group.blocks!;
          for (final block in blocks) {
            if (block.role?.value == TemplateBlockRoleValue.mainText) {
              final metadata =
                  templatelog.fieldsMetadata!.firstWhereOrNull((e) {
                return e.customField!.id ==
                    block.customFieldOptions!.customField!.id;
              });

              return metadata?.value!.displayValue ??
                  AppLocalizationsResolver.get().locationTrackingActive;
            }
          }
        }
        return AppLocalizationsResolver.get().locationTrackingActive;
      case ExtensionType.inventorylog:
      case ExtensionType.personnellog:
      case ExtensionType.progresslog:
        throw '$LocationTrackingTopSnackbar:_buildTitle: '
            'extension type <$extensionType> not supported';
    }
  }

  void _onStopPressed(BuildContext context) async {
    logger.i('location-tracking-top-snack-bar: stop');
    if (isStopping) {
      return;
    }
    isStopping = true;

    final db = context.read<Repository>();

    final trackingToStop = _currentTracking;
    setState(() {
      _timerController.maybeEnd();
      _currentTracking = null;
    });

    await _handler.stop(
      db,
      trackingToStop!,
    );

    isStopping = false;
  }
}
