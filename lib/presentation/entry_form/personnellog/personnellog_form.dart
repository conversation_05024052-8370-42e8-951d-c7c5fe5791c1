import 'package:bitacora/presentation/entry_form/personnellog/personnellog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/personnellog/personnellog_name_suggestion_respository_query.dart';
import 'package:bitacora/presentation/entry_form/util/centesimal_text_input_formatter.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_collection_selection_search_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:bitacora/util/time_utils.dart';
import 'package:datetime_picker_formfield_new/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class PersonnellogForm extends StatefulWidget {
  final PersonnellogFormController controller;

  const PersonnellogForm({super.key, required this.controller});

  @override
  State<PersonnellogForm> createState() => _PersonnellogFormState();
}

class _PersonnellogFormState extends State<PersonnellogForm> {
  final _nameKey = GlobalKey();

  final FocusNode _nameFocus = FocusNode();
  final FocusNode _hoursFocus = FocusNode();
  final FocusNode _entranceFocus = FocusNode();
  final FocusNode _exitFocus = FocusNode();
  final FocusNode _projectNameFocus = FocusNode();
  final FocusNode _sublocationFocus = FocusNode();
  final FocusNode _commentsFocus = FocusNode();

  @override
  void dispose() {
    _nameFocus.dispose();
    _hoursFocus.dispose();
    _entranceFocus.dispose();
    _exitFocus.dispose();
    _projectNameFocus.dispose();
    _sublocationFocus.dispose();
    _commentsFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return _PersonnellogFormSkeleton(
      name: widget.controller.props.inputEntry == null
          ? wrapWidgetForMeasure(
              NewableFieldCollectionSelectionSearchTypeahead(
                key: _nameKey,
                fieldController: widget.controller.nameCollection,
              ),
            )
          : wrapWidgetForMeasure(NewableFieldSuggestionTypeahead(
              key: _nameKey,
              suggestionsQuery:
                  const PersonnellogNameSuggestionRepositoryQuery(),
              queryScope: getOrgScope(context),
              onSelect: (_) => widget.controller.isHours.value
                  ? maybeRequestFocus(
                      _hoursFocus, widget.controller.hours.text, false)
                  // FIXME: maybe focus on time fields?
                  : maybeRequestFocus(_projectNameFocus,
                      widget.controller.projectName.text, false),
              textFieldConfiguration: TypeaheadTextFieldConfiguration(
                focusNode: _nameFocus,
                controller: widget.controller.name,
                textInputAction: TextInputAction.next,
                textCapitalization: TextCapitalization.words,
                decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.name),
                onSubmitted: (value) => widget.controller.isHours.value
                    ? maybeRequestFocus(
                        _hoursFocus, widget.controller.hours.text, true)
                    : maybeRequestFocus(_projectNameFocus,
                        widget.controller.projectName.text, true),
                onEditingComplete: () => widget.controller.isHours.value
                    ? _hoursFocus.requestFocus()
                    : _projectNameFocus.requestFocus(),
                validator: getRequiredFieldValidator(),
              ))),
      hours: ValueListenableBuilder<bool>(
        valueListenable: widget.controller.isHours,
        builder: (context, isHours, _) =>
            isHours ? getHoursRow(context) : getTimeOfDayRow(context),
      ),
      projectSublocation: getProjectSublocationRow(
        context: context,
        projectFocusNode: _projectNameFocus,
        projectController: widget.controller.projectName,
        sublocationFocusNode: _sublocationFocus,
        sublocationController: widget.controller.sublocation,
      ),
      comments: getCommentsRow(
        context: context,
        controller: widget.controller,
        focusNode: _commentsFocus,
      ),
    );
  }

  Widget getHoursRow(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 6,
          child: TextButton(
            onPressed: () => widget.controller.isHours.value = false,
            child: Container(
              alignment: Alignment.centerLeft,
              child: Text(AppLocalizations.of(context)!.numHours_),
            ),
          ),
        ),
        const SizedBox(width: kFormHorizontalSpacing),
        Expanded(
          flex: 4,
          child: TextFormField(
            controller: widget.controller.hours,
            focusNode: _hoursFocus,
            textInputAction: TextInputAction.next,
            // Text capitalization set here due to iOS bug where the
            // capitalization prop is cached from previous textfields.
            textCapitalization: TextCapitalization.sentences,
            keyboardType: const TextInputType.numberWithOptions(
                decimal: true, signed: true),
            inputFormatters: [CentesimalTextInputFormatter()],
            decoration:
                InputDecoration(labelText: AppLocalizations.of(context)!.hours),
            onFieldSubmitted: (value) => maybeRequestFocus(
                _projectNameFocus, widget.controller.projectName.text, true),
          ),
        ),
      ],
    );
  }

  Widget getTimeOfDayRow(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
          flex: 2,
          child: TextButton(
            onPressed: () => widget.controller.isHours.value = true,
            child: Container(
              alignment: Alignment.centerLeft,
              child: Text(AppLocalizations.of(context)!.time_.toUpperCase()),
            ),
          ),
        ),
        Expanded(
          flex: 4,
          child: DateTimeField(
            focusNode: _entranceFocus,
            controller: widget.controller.entrance,
            format: kTimeOfDayDateFormat,
            resetIcon: const Icon(
              Icons.close,
              size: 18,
            ),
            textCapitalization: TextCapitalization.sentences,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            onShowPicker: pickTime,
            onFieldSubmitted: (value) => maybeRequestFocus(
                _exitFocus, widget.controller.exit.text, true),
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.entrance,
              contentPadding: getContentPaddingWithSuffix(),
            ),
          ),
        ),
        const SizedBox(width: kFormHorizontalSpacing),
        Expanded(
          flex: 4,
          child: DateTimeField(
            focusNode: _exitFocus,
            controller: widget.controller.exit,
            format: kTimeOfDayDateFormat,
            resetIcon: const Icon(
              Icons.close,
              size: 18,
            ),
            textCapitalization: TextCapitalization.sentences,
            keyboardType: TextInputType.text,
            textInputAction: TextInputAction.next,
            onShowPicker: pickTime,
            onFieldSubmitted: (value) => maybeRequestFocus(
                _projectNameFocus, widget.controller.projectName.text, true),
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.exit,
              contentPadding: getContentPaddingWithSuffix(),
            ),
          ),
        ),
      ],
    );
  }
}

class NonEditablePersonnellogForm extends StatelessWidget {
  final PersonnellogFormController controller;

  const NonEditablePersonnellogForm({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      child: Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: const InputDecorationTheme(
            contentPadding: EdgeInsets.only(),
            border: InputBorder.none,
            isDense: true,
          ),
        ),
        child: _PersonnellogFormSkeleton(
          isEditable: false,
          name: TextFormField(
            controller: controller.name,
            decoration:
                InputDecoration(labelText: AppLocalizations.of(context)!.name),
          ),
          hours: ValueListenableBuilder<bool>(
            valueListenable: controller.isHours,
            builder: (context, isHours, _) => isHours
                ? _getNonEditableHoursRow(context)
                : _getNonEditableTimeOfDayRow(context),
          ),
          projectSublocation: getNonEditableProjectSublocationRow(
            context: context,
            projectController: controller.projectName,
            sublocationController: controller.sublocation,
          ),
          comments: controller.comments.text.isEmpty
              ? null
              : TextFormField(
                  controller: controller.comments,
                  decoration: InputDecoration(
                      labelText: AppLocalizations.of(context)!.name),
                ),
        ),
      ),
    );
  }

  Widget _getNonEditableHoursRow(BuildContext context) {
    return TextFormField(
      controller: controller.hours,
      decoration:
          InputDecoration(labelText: AppLocalizations.of(context)!.hours),
    );
  }

  Widget _getNonEditableTimeOfDayRow(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: TextFormField(
            controller: controller.entrance,
            decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.entrance),
          ),
        ),
        const SizedBox(width: kFormHorizontalSpacing),
        Expanded(
          flex: 1,
          child: TextFormField(
            controller: controller.exit,
            decoration:
                InputDecoration(labelText: AppLocalizations.of(context)!.exit),
          ),
        ),
      ],
    );
  }
}

class _PersonnellogFormSkeleton extends StatelessWidget {
  final Widget name;
  final Widget hours;
  final Widget projectSublocation;
  final Widget? comments;
  final bool isEditable;

  const _PersonnellogFormSkeleton({
    required this.name,
    required this.hours,
    required this.projectSublocation,
    this.comments,
    this.isEditable = true,
  });

  @override
  Widget build(BuildContext context) {
    final children = [
      name,
      const SizedBox(height: kFormVerticalSpacing),
      hours,
      const SizedBox(height: kFormVerticalSpacing),
      projectSublocation,
      if (comments != null) ...[
        const SizedBox(height: kFormVerticalSpacing),
        comments!,
      ]
    ];

    return isEditable
        ? SliverList(delegate: SliverChildListDelegate.fixed(children))
        : Column(children: children);
  }
}
