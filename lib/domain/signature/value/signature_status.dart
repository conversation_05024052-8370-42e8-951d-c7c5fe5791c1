import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';

enum SignatureStatusValue {
  approved('APPROVED'),
  rejected('REJECTED'),
  received('RECEIVED');

  final String apiValue;

  const SignatureStatusValue(this.apiValue);

  static fromApi(String value) {
    switch (value) {
      case 'APPROVED':
        return SignatureStatusValue.approved;
      case 'REJECTED':
        return SignatureStatusValue.rejected;
      case 'RECEIVED':
        return SignatureStatusValue.received;
    }
  }

  String displayValue() {
    switch (this) {
      case SignatureStatusValue.approved:
        return AppLocalizationsResolver.get().approved.toUpperCase();
      case SignatureStatusValue.rejected:
        return AppLocalizationsResolver.get().rejected.toUpperCase();
      case SignatureStatusValue.received:
        return AppLocalizationsResolver.get().received.toUpperCase();
    }
  }
}

class SignatureStatus extends ValueObject<SignatureStatusValue> {
  SignatureStatus(super.value);

  static SignatureStatus get approved =>
      SignatureStatus(SignatureStatusValue.approved);

  static SignatureStatus get rejected =>
      SignatureStatus(SignatureStatusValue.rejected);

  static SignatureStatus get received =>
      SignatureStatus(SignatureStatusValue.received);

  @override
  get dbValue => value.index;

  factory SignatureStatus.fromDb(int index) =>
      SignatureStatus(SignatureStatusValue.values[index]);

  factory SignatureStatus.fromApi(String value) =>
      SignatureStatus(SignatureStatusValue.fromApi(value));

  @override
  String get displayValue => value.displayValue();

  @override
  String get apiValue => value.apiValue;
}
