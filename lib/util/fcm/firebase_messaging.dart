import 'package:bitacora/util/inject/inject.dart';
import 'package:firebase_messaging/firebase_messaging.dart' as firebase;

class FirebaseMessaging {
  factory FirebaseMessaging() => inject(() => const FirebaseMessaging._());

  const FirebaseMessaging._();

  final Stream<firebase.RemoteMessage> Function() onMessage =
      _firebaseMessagingOnMessage;
  final void Function(firebase.BackgroundMessageHandler handler)
      onBackgroundMessage = firebase.FirebaseMessaging.onBackgroundMessage;

  static Stream<firebase.RemoteMessage> _firebaseMessagingOnMessage() {
    return firebase.FirebaseMessaging.onMessage;
  }
}
