import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';

class EntryIdRepositoryQuery extends RepositoryQuery<Entry?> {
  final RemoteId remoteId;

  const EntryIdRepositoryQuery({required this.remoteId});

  @override
  Future<Entry?> run(RepositoryQueryContext context) =>
      context.db.entry.findByRemoteId(context, remoteId);

  @override
  Fields fields(Repository db) => db.entry.fieldsBuilder.build();
}

class EntryRelationsDbQuery extends RepositoryQuery<Entry?> {
  final LocalId entryId;

  const EntryRelationsDbQuery(this.entryId);

  @override
  Future<Entry?> run(RepositoryQueryContext context) {
    return context.db.entry.find(context, entryId);
  }

  @override
  Fields fields(Repository db) {
    final project = db.project.fieldsBuilder.build();
    final worklog = db.worklog.fieldsBuilder.project(project).build();

    final inventorylog = db.inventorylog.fieldsBuilder
        .sourceProject(project)
        .destProject(project)
        .build();

    final personnellog = db.personnellog.fieldsBuilder.project(project).build();

    final fieldsMetadata =
        db.customFieldMetadata.fieldsBuilder.project(project).build();
    final templatelog = db.templatelog.fieldsBuilder
        .fieldsMetadata(fieldsMetadata)
        .defaultProject(project)
        .build();

    return db.entry.fieldsBuilder
        .worklog(worklog)
        .inventorylog(inventorylog)
        .personnellog(personnellog)
        .progresslog(db.progresslog.fieldsBuilder
            .entry(db.entry.fieldsBuilder
                .worklog(worklog)
                .inventorylog(inventorylog)
                .personnellog(personnellog)
                .templatelog(templatelog)
                .build())
            .build())
        .openState(db.openState.fieldsBuilder.build())
        .templatelog(templatelog)
        .build();
  }
}

class EntryOpenProgressDbQuery {
  static Fields? _fields;

  const EntryOpenProgressDbQuery();

  Future<Entry?> run(
    RepositoryQueryContext context,
    LocalId id,
  ) {
    final db = context.db;
    return db.entry.find(context.copyWith(fields: _getFields(db)), id);
  }

  Fields _getFields(Repository db) {
    _fields ??= db.entry.fieldsBuilder
        .openState(
            db.openState.fieldsBuilder.progress().startDay().endDay().build())
        .createdAt()
        .build();
    return _fields!;
  }
}

class EntryForOutgoingMutationQuery extends RepositoryQuery<Entry?> {
  final LocalId id;

  const EntryForOutgoingMutationQuery({required this.id});

  @override
  Future<Entry?> run(RepositoryQueryContext context) {
    final db = context.db;
    return db.entry.find(context, id);
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) {
    final project = db.project.fieldsBuilder
        .organization(db.organization.fieldsBuilder.build())
        .build();

    final worklog = db.worklog.fieldsBuilder.project(project).build();

    final inventorylog = db.inventorylog.fieldsBuilder
        .sourceProject(project)
        .destProject(project)
        .build();

    final personnellog = db.personnellog.fieldsBuilder.project(project).build();

    final fieldsMetadata = db.customFieldMetadata.fieldsBuilder
        .customField(db.customField.fieldsBuilder.type().build())
        .project(project)
        .build();
    final templatelog = db.templatelog.fieldsBuilder
        .fieldsMetadata(fieldsMetadata)
        .defaultProject(project)
        .build();

    return db.entry.fieldsBuilder
        .remoteId()
        .worklog(worklog)
        .inventorylog(inventorylog)
        .personnellog(personnellog)
        .location()
        .progresslog(db.progresslog.fieldsBuilder
            .entry(db.entry.fieldsBuilder
                .worklog(worklog)
                .inventorylog(inventorylog)
                .personnellog(personnellog)
                .templatelog(templatelog)
                .build())
            .build())
        .templatelog(templatelog)
        .build();
  }
}

class OrganizationIdFromEntryRepositoryQuery extends RepositoryQuery<LocalId> {
  final LocalId entryId;

  const OrganizationIdFromEntryRepositoryQuery({required this.entryId});

  @override
  Future<LocalId> run(RepositoryQueryContext context) async {
    final entry = await context.db.entry.find(context, entryId);
    return entry!.projects.first.organization!.id!;
  }

  @override
  Fields fields(Repository db) {
    final project = db.project.fieldsBuilder
        .organization(db.organization.fieldsBuilder.build())
        .build();

    final worklog = db.worklog.fieldsBuilder.project(project).build();

    final inventorylog = db.inventorylog.fieldsBuilder
        .sourceProject(project)
        .destProject(project)
        .build();

    final personnellog = db.personnellog.fieldsBuilder.project(project).build();

    final templatelog = db.templatelog.fieldsBuilder
        .fieldsMetadata(
            db.customFieldMetadata.fieldsBuilder.project(project).build())
        .defaultProject(project)
        .build();

    return db.entry.fieldsBuilder
        .worklog(worklog)
        .inventorylog(inventorylog)
        .personnellog(personnellog)
        .progresslog(db.progresslog.fieldsBuilder
            .entry(db.entry.fieldsBuilder
                .worklog(worklog)
                .inventorylog(inventorylog)
                .personnellog(personnellog)
                .build())
            .build())
        .templatelog(templatelog)
        .build();
  }
}
