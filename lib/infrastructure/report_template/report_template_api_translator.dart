import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/report_template/report_template.dart';

class ReportTemplateApiTranslator implements ModelTranslator<ReportTemplate> {
  const ReportTemplateApiTranslator();

  @override
  ReportTemplate fromMap(Map<String, dynamic> data) {
    return ReportTemplate(
      active: ReportTemplateActive(data['active']),
      description: ReportTemplateDescription(data['description']),
      option: ReportTemplateOption(data['option']),
      order: ReportTemplateOrder(data['order']),
      organization: Organization(id: LocalId(data['organization_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(ReportTemplate model) {
    throw UnimplementedError();
  }
}
