import 'dart:async';
import 'dart:convert';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/command_line.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
Launch Android or iOS simulator/emulator.

Available arguments:

 -h               Prints help.

Usage:
 
  bit emulator

''';

Future<int> bitEmulator([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return -1;
  }

  writeln('Launching emulator...', TextStyle.highlighted);
  final emulatorsResult = await startProcess('flutter', ['emulators']);
  final lines = <String>[];
  emulatorsResult.stdout.listen((event) {
    lines.addAll(utf8.decoder
        .convert(event)
        .split('\n')
        .where((s) => RegExp(r'(.*)•(.*)•(.*)•').hasMatch(s)));
  });
  await emulatorsResult.exitCode;
  final numberEmulators = lines.length - 1;

  if (numberEmulators == 0) {
    writeln('No emulators found.', TextStyle.error);
    return 0;
  }

  final commands = <String, Future<int> Function()>{};
  for (int i = 1; i < lines.length; i++) {
    final id = lines[i].split(' • ').first.trim();
    commands[id] = () => _launchEmulator(id);
  }

  final launch = await CommandLine(
    commands,
    title: 'Select emulator.',
  ).present();
  return launch ?? 1;
}

Future<int> _launchEmulator(String id) {
  return runProcess('flutter', ['emulators', '--launch', id]);
}
