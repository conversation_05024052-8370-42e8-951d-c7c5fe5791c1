import 'dart:async';

import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/router.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_load_repository_query.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_navigator_props.dart';
import 'package:bitacora/presentation/entry_form/progresslog/progresslog_form.dart';
import 'package:bitacora/presentation/entry_form/progresslog/progresslog_form_controller.dart';
import 'package:bitacora/presentation/progress/percentage_picker.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/navigator_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../application/api/mocks.dart';
import '../../../domain/common/mocks.dart';
import '../../../domain/entry/mocks.dart';
import '../../../infrastructure/mocks.dart';
import '../../../mocktail_fallback_values.dart';
import '../../../navigation_test_util.dart';
import '../../../test_util.dart';
import '../../../util/mocks.dart';
import '../mocks.dart';
import 'mocks.dart';

void main() {
  Widget testApp({
    Entry? inputEntry,
    ProgresslogFormController? progresslogFormController,
    StreamController<Mutation<Entry>>? streamController,
  }) {
    final controller =
        progresslogFormController ?? _mockProgresslogFormController();

    return MultiProvider(
      providers: [
        Provider<Repository>(
          create: (_) {
            final entryRepository =
                mockEntryRepository(mutationsController: streamController);
            final db = MockRepository();
            when(() => db.entry).thenReturn(entryRepository);
            return db;
          },
        ),
        ChangeNotifierProvider<ActiveLogDay>(
          create: (_) => ActiveLogDay(),
        ),
      ],
      child: MaterialApp(
        home: Localizations(
          locale: const Locale('en'),
          delegates: AppLocalizations.localizationsDelegates,
          child: Scaffold(
            body: CustomScrollView(
              slivers: [
                ProgresslogForm(
                  controller: controller,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  group('$ProgresslogForm tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Has base UI', (tester) async {
      const percentage = 2;
      const comments = 'Commmentarios...';
      await tester.pumpWidget(
        testApp(
          progresslogFormController: _mockProgresslogFormController(
            percentage: Percentage(percentage),
            comments: TextEditingController(text: comments),
          ),
        ),
      );

      expect(find.text('$percentage%'), findsOneWidget);
      expect(find.text(comments), findsOneWidget);
      expect(find.byType(PercentagePicker), findsOneWidget);
      expect(find.byType(TextFormField), findsOneWidget);
    });

    testWidgets('Navigates to progressive progresslog', (tester) async {
      final openState =
          mockOpenState(progressive: const OpenStateProgressive(true));

      await _testNavigation(tester, openState);
      final NavigatorState navigator = tester.state(find.byType(Navigator));
      navigator.pop();
      await tester.pump();
    });

    testWidgets('Navigates to non-progressive progresslog', (tester) async {
      final openState =
          mockOpenState(progressive: const OpenStateProgressive(false));

      await _testNavigation(tester, openState);
    });

    testWidgets('Navigates fails for lost parent entry', (tester) async {
      await _testNavigation(tester, mockOpenState(), true);
    });

    testWidgets('Deleting parent pops navigation', (tester) async {
      final inputEntry = _mockProgresslogEntry();
      inputEntry.progresslog!.entry!.id;
      final navigatorUtils = mockNavigatorUtils();
      final streamController = StreamController<Mutation<Entry>>.broadcast();
      await withInjected<NavigatorUtils>(navigatorUtils, () async {
        await tester.pumpWidget(
          testApp(
            progresslogFormController:
                _mockProgresslogFormController(inputEntry: inputEntry),
            streamController: streamController,
          ),
        );

        streamController.sink.add(Mutation<Entry>(
          type: MutationType.delete,
          id: inputEntry.progresslog!.entry!.id,
        ));
      });

      await awaitUntilStopsThrowing(
          () => verify(() => navigatorUtils.popUntilParentRoute(any())));
    });
  });
}

Entry _mockProgresslogEntry() {
  return mockEntry(extension: mockProgresslog());
}

ProgresslogFormController _mockProgresslogFormController({
  Entry? inputEntry,
  Percentage? percentage,
  TextEditingController? comments,
}) {
  final proxyController = mockEntryFormController();
  when(() => proxyController.form()).thenReturn(Container());
  when(() => proxyController.formOptions()).thenReturn(Container());
  final controller = MockProgresslogFormController();
  when(() => controller.liveEntry)
      .thenReturn(ValueNotifier(inputEntry ?? _mockProgresslogEntry()));
  when(() => controller.proxyController).thenReturn(proxyController);
  when(() => controller.percentage).thenReturn(percentage ?? Percentage(1));
  when(() => controller.comments)
      .thenReturn(comments ?? TextEditingController(text: ''));
  return controller;
}

Future<void> _testNavigation(
  WidgetTester tester,
  OpenState openState, [
  bool isDeletedFromDb = false,
]) async {
  final db = MockRepository();
  final entry = mockEntry(openState: openState);
  whenQuery<EntryFormPageLoadRepositoryQuery, Entry?>(
    db,
    (_) => isDeletedFromDb ? null : entry,
  );

  await testNavigation(
    tester: tester,
    providers: <Provider>[
      Provider<Repository>(create: (_) => db),
      Provider<ApiHelper>(create: (_) => MockApiHelper()),
      Provider<ApiTranslator>(create: (_) => MockApiTranslator()),
    ],
    onNavigate: (context) =>
        ProgresslogForm.navigate(EntryFormPageNavigatorProps(context), entry),
    expectedPushedRouteName: kRouteEntryForm,
    onTestArguments: (arguments) {
      arguments as List;
      if (arguments[0] is! Entry) {
        return false;
      }
      final progresslog = arguments[0].progresslog!;
      final expectedProgress =
          openState.progressive!.value ? openState.progress!.value + 1 : 100;
      return progresslog.entry!.id! == entry.id! &&
          progresslog.progress!.value == expectedProgress;
    },
    isFailTest: isDeletedFromDb,
  );
}
