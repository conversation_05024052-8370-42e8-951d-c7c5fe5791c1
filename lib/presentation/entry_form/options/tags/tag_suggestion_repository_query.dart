import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';

class TagSuggestionRepositoryQuery extends RepositoryQuery<List<String>> {
  final List<String> filterOut;

  const TagSuggestionRepositoryQuery(this.filterOut);

  // FIXME: return List<Tag> and show color in suggestions ui?
  @override
  Future<List<String>> run(
    RepositoryQueryContext context,
  ) async =>
      context.db.tag.names(context, filterOut);
}
