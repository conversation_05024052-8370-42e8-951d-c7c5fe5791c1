import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/user/user.dart';

class UserApiTranslator implements ModelTranslator<User> {
  const UserApiTranslator();

  @override
  User fromMap(Map<String, dynamic> data) {
    return User(
      remoteId: RemoteId(data['id']),
      name: User<PERSON>ame(data['name']),
      email: UserEmail(data['email']),
    );
  }

  @override
  Map<String, dynamic> toMap(User model) {
    return toMapWithPrefix(model);
  }

  Map<String, dynamic> toMapWithPrefix(User model, {String prefix = ''}) {
    final map = <String, dynamic>{};
    if (model.remoteId?.value != null) {
      map['${prefix}id'] = model.remoteId!.apiValue;
    } else if (model.name?.value != null) {
      map['${prefix}name'] = model.name!.apiValue;
    } else {
      map['${prefix}id'] = null;
      map['${prefix}name'] = null;
    }
    return map;
  }
}
