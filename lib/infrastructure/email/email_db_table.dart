import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/email/email.dart';
import 'package:bitacora/domain/email/email_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/email/email_db_contract.dart';
import 'package:bitacora/infrastructure/email/email_db_fields_builder.dart';
import 'package:bitacora/infrastructure/email/email_db_translator.dart';

class EmailDbTable extends DbTable<Email, EmailDbFieldsBuilder>
    implements EmailRepository<DbContext, EmailDbFieldsBuilder> {
  EmailDbTable() : super();

  @override
  EmailDbContract get contract => const EmailDbContract();

  @override
  DbTranslator<Email> get translator => const EmailDbTranslator();

  @override
  EmailDbFieldsBuilder get fieldsBuilder => EmailDbFieldsBuilder();

  @override
  Future<List<Email>> findAll(DbContext context, LocalId personDetailId) {
    return query(
      context,
      where: '${contract.personDetailId} = ?',
      whereArgs: [personDetailId.dbValue],
    );
  }
}
