import 'dart:async';

import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/organization_cache_repository_query.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/loader/loader.dart';

class OrganizationCache extends Loader<List<Organization>> {
  final ActiveSession _activeSession;
  late final StreamSubscription _mutationsSubscription;

  factory OrganizationCache(ActiveSession activeSession, Repository db) =>
      inject(() => OrganizationCache._(activeSession, db));

  OrganizationCache._(
    ActiveSession activeSession,
    Repository db,
  )   : _activeSession = activeSession,
        super(() async {
          if (activeSession.value == null) {
            return null;
          }
          return db.query(const OrganizationCacheRepositoryQuery());
        }) {
    _activeSession.addListener(load);
    _mutationsSubscription = db.organization.getMutations().listen((event) {
      if (!_needsLoadFromMutationEvent(event)) {
        return;
      }
      load();
    });
  }

  bool _needsLoadFromMutationEvent(Mutation<Organization> event) {
    if (value == null) {
      return true;
    }

    if (event.type != MutationType.update) {
      return true;
    }

    final model = event.model!;
    final cachedModel = value?.where((element) => element.id == event.id).first;
    if (cachedModel == null) {
      return true;
    }

    // FIXME: comparison of all fields of interest instead of checking 'name' only
    return (cachedModel.remoteId != model.remoteId && model.remoteId != null) ||
        (cachedModel.activePlan != model.activePlan &&
            model.activePlan != null) ||
        (cachedModel.name != model.name && model.name != null) ||
        (cachedModel.color != model.color && model.color != null) ||
        (cachedModel.userHasSeen != model.userHasSeen &&
            model.userHasSeen != null);
  }

  @override
  void dispose() {
    _activeSession.removeListener(load);
    _mutationsSubscription.cancel();
    super.dispose();
  }
}
