import 'dart:async';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/command_line.dart';
import 'package:bit/git_utils.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
A tool for squashing commits.

Finds first ancestor branch and squashes commits up to it.

i.e.
  
Initial state:
  
  A(ancestor) -> B -> C -> D(current)
    
End result:
  
  A(ancestor) -> BCD(current)
  
The commit message is optionally the last commit message or a merge.

Usage: 

  bit git squash
''';

Future<int> bitGitSquash([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  final branch = await gitGetCurrentBranch();
  final status = await assertStatus(branch);
  if (status != 0) {
    return status;
  }

  final ancestor = await gitFindFirstAncestorBranch();
  if (ancestor == null) {
    writeln('No ancestor was found.', TextStyle.error);
    return 1;
  }

  if (ancestor.level == 1) {
    write('Branch is already squashed onto ');
    write(ancestor.name, TextStyle.highlighted);
    writeln('.');
    return 1;
  }

  await runProcess(
    'git',
    ['log', '-n', '${ancestor.level}'],
    ProcessStartMode.inheritStdio,
    false,
  );
  write('\n\n${ancestor.name}', TextStyle.focus);
  writeln(' found ${ancestor.level} commits deep.');

  final result = await CommandLine.confirm(() async {
    return _squash(ancestor.level);
  }, titlePrinter: () {
    write('\nSquash last ${ancestor.level} commits of ');
    write(branch, TextStyle.focus);
    writeln('?');
  }).present();
  return result ?? 1;
}

Future<int> assertStatus(String branch) async {
  final isClean = await gitIsWorkingTreeClean();
  if (isClean != 0) {
    writeln('Interrupting. Working tree is not clean.', TextStyle.error);
    return 1;
  }

  if (branch == 'master') {
    writeln('Interrupting. Can\'t squash master branch.', TextStyle.error);
    return 1;
  }

  return 0;
}

Future<int> _squash(int level) async {
  final branch = await gitGetCurrentBranch();
  if (branch == 'master') {
    writeln('Can\'t squash master.', TextStyle.error);
    return 1;
  }

  final message = await _getSquashCommitMessage(level);
  if (message == null) {
    return 1;
  }

  final reset = await runProcess(
    'git',
    ['reset', '--soft', 'HEAD~$level'],
    ProcessStartMode.normal,
    false,
  );
  if (reset != 0) {
    return reset;
  }

  final commit = await runProcess(
    'git',
    ['commit', '-m', message],
    ProcessStartMode.normal,
    false,
  );
  if (commit != 0) {
    return commit;
  }

  write('Branch ');
  write(branch, TextStyle.highlighted);
  writeln(' squashed successfully.\n');
  return runProcess(
    'git',
    ['log', '-n', '2'],
    ProcessStartMode.inheritStdio,
    false,
  );
}

Future<String?> _getSquashCommitMessage(int level) async {
  return CommandLine(
    {
      'keep-last': () => gitGetPreviousCommitMessage(),
      'merge': () => _getMergedMessage(level),
    },
    title: 'Do you want to keep the last commit message or merge all of them?',
  ).present();
}

Future<String> _getMergedMessage(int level) async {
  final buffer = StringBuffer();
  for (var i = 0; i < level; i++) {
    final message = await gitGetPreviousCommitMessage(i);
    if (i > 0) {
      buffer.writeln('----------------------');
    }
    buffer.writeln(message);
  }
  return buffer.toString();
}
