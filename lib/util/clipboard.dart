import 'dart:io';

import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/toast/flutter_toast_utils.dart';
import 'package:flutter/services.dart' as services;

export 'package:file/file.dart';

class Clipboard {
  factory Clipboard() => inject(() => const Clipboard._());

  const Clipboard._();

  Future<void> copyText(String text) async {
    await services.Clipboard.setData(services.ClipboardData(text: text));
    if (Platform.isIOS) {
      FluttertoastUtils().showToast(
        msg: AppLocalizationsResolver.get().copyClipboard(text),
        toastLength: Toast.LENGTH_SHORT,
      );
    }
  }
}
