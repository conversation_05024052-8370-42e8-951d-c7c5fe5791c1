import 'package:bitacora/domain/attachment/doodle_drawn_line.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/image/doodle/doodle_sketcher.dart';
import 'package:flutter/material.dart';

class DoodleCanvasCurrentLine extends StatefulWidget {
  final GlobalKey repaintGlobalKey;
  final ValueNotifier<DoodleDrawnLine?> currentUiLineNotifier;
  final ValueNotifier<List<DoodleDrawnLine>> imageLines;
  final ValueNotifier<bool> isDrawingNotifier;
  final Size canvasSize;
  final double imageScale;
  final Size imageSize;
  final Offset imageOffset;
  final Color selectedColor;
  final Function(double scale, Offset position)? onScaleUpdate;

  const DoodleCanvasCurrentLine({
    super.key,
    required this.repaintGlobalKey,
    required this.imageLines,
    required this.currentUiLineNotifier,
    required this.isDrawingNotifier,
    required this.canvasSize,
    required this.imageScale,
    required this.imageSize,
    required this.imageOffset,
    required this.selectedColor,
    this.onScaleUpdate,
  });

  @override
  State<DoodleCanvasCurrentLine> createState() =>
      _DoodleCanvasCurrentLineState();
}

class _DoodleCanvasCurrentLineState extends State<DoodleCanvasCurrentLine> {
  double? _initialPhotoScale;
  Offset? _initialFocalPointOffset;
  int? _lastEndPointerCount;
  bool _isDrawingPoint = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) {
        _isDrawingPoint = true;
      },
      onTapUp: (details) {
        if (_isDrawingPoint) {
          final point = _determinePoint(context, details.globalPosition);
          _startLine(point);
          _endLine(context);
          _isDrawingPoint = false;
        }
      },
      onScaleStart: (details) {
        _isDrawingPoint = false;
        if (details.pointerCount > 1) {
          _initialPhotoScale = widget.imageScale;
          _initialFocalPointOffset = widget.imageOffset - details.focalPoint;
          return;
        }

        final point = _determinePoint(context, details.focalPoint);
        if (!_isInsideCanvas(context, point)) {
          return;
        }
        _startLine(point);
      },
      onScaleUpdate: (details) {
        if (details.pointerCount > 1) {
          if (widget.onScaleUpdate != null) {
            widget.onScaleUpdate!(
              _initialPhotoScale! * details.scale,
              _initialFocalPointOffset! + details.focalPoint,
            );
          }

          return;
        }

        final point = _determinePoint(context, details.focalPoint);
        if (widget.currentUiLineNotifier.value != null &&
            !_isInsideCanvas(context, point)) {
          _endLine(context);
          return;
        } else if (widget.currentUiLineNotifier.value == null) {
          if (_isInsideCanvas(context, point)) {
            _startLine(point);
          }
          return;
        }

        _addPoint(point);
      },
      onScaleEnd: (details) {
        _lastEndPointerCount = details.pointerCount;
        if (widget.currentUiLineNotifier.value != null) {
          if (details.pointerCount != 0 && !_isCurrentLineLongEnough()) {
            widget.currentUiLineNotifier.value = null;
            return;
          }

          _endLine(context);
        }
      },
      child: RepaintBoundary(
        child: ValueListenableBuilder<DoodleDrawnLine?>(
          valueListenable: widget.currentUiLineNotifier,
          builder: (context, line, _) {
            return CustomPaint(
              painter: DoodleSketcher(
                lines: line == null ? [] : [line],
                canvasScale: widget.imageScale,
              ),
            );
          },
        ),
      ),
    );
  }

  Offset _determinePoint(
    BuildContext context,
    Offset globalPosition,
  ) {
    final box = context.findRenderObject() as RenderBox;
    return box.globalToLocal(globalPosition);
  }

  bool _isInsideCanvas(BuildContext context, Offset uiPoint) {
    final imagePoint = _uiPointToImage(context, uiPoint);
    return imagePoint.dx >= 0 &&
        imagePoint.dx <= widget.imageSize.width &&
        imagePoint.dy >= 0 &&
        imagePoint.dy <= widget.imageSize.height;
  }

  void _startLine(Offset point) {
    if (_lastEndPointerCount == 1 && !_isDrawingPoint) {
      /// A pointer was "lost" which normally happens at the end of a pinch
      /// to zoom.
      return;
    }

    widget.isDrawingNotifier.value = true;
    widget.currentUiLineNotifier.value =
        DoodleDrawnLine([point], widget.selectedColor, widget.imageScale);
  }

  void _addPoint(Offset point) {
    final line = [...widget.currentUiLineNotifier.value!.points, point];
    widget.currentUiLineNotifier.value =
        DoodleDrawnLine(line, widget.selectedColor, widget.imageScale);
  }

  void _endLine(BuildContext context) {
    widget.isDrawingNotifier.value = false;
    final imagePoints = widget.currentUiLineNotifier.value!.points
        .map((e) => _uiPointToImage(context, e!))
        .toList();
    widget.imageLines.value = [
      ...widget.imageLines.value,
      widget.currentUiLineNotifier.value!
          .copyWith(points: imagePoints, scale: widget.imageScale),
    ];
    widget.currentUiLineNotifier.value = null;
  }

  Offset _uiPointToImage(BuildContext context, Offset uiPoint) {
    final viewport = Offset(widget.canvasSize.width, widget.canvasSize.height);
    final imageSizeOffset =
        Offset(widget.imageSize.width, widget.imageSize.height);
    final offset = ((viewport - (imageSizeOffset * widget.imageScale)) / 2) +
        widget.imageOffset;

    return (uiPoint - offset) / widget.imageScale;
  }

  bool _isCurrentLineLongEnough() {
    final line = widget.currentUiLineNotifier.value!;
    return line.points.length > 5;
  }
}
