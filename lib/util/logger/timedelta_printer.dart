import 'package:logger/logger.dart';

class TimeDeltaPrinter extends SimplePrinter {
  DateTime lastLogTime = DateTime.now();

  TimeDeltaPrinter({super.colors = false}) : super(printTime: false);

  @override
  List<String> log(LogEvent event) {
    final list = super.log(event);

    final logTime = DateTime.now();
    final delta = '[+${logTime.difference(lastLogTime).inMicroseconds}µs]';
    lastLogTime = logTime;

    list.first = '$delta ${list.first}';
    return list;
  }
}
