import 'dart:io';
import 'dart:math';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:console/console.dart';
import 'package:glob/glob.dart';
import 'package:glob/list_local_fs.dart';

void printBitacoraBanner() {
  final bannerFiles = _getAllBannerFiles();
  final lines =
      bannerFiles[Random().nextInt(bannerFiles.length)].readAsLinesSync();
  final rowOffset = Console.rows ~/ 2 - lines.length ~/ 2;
  final columnOffset = 8;
  Console.moveCursor(row: rowOffset);
  Console.write(kAnsiColorReset);
  Console.write(kAnsiColorRedPlus);
  for (var line in lines) {
    Console.moveToColumn(max(columnOffset, 0));
    writeln(line);
  }
  Console.write(kAnsiColorReset);
}

List<File> _getAllBannerFiles() {
  final files = Glob('**.txt')
      .listSync(root: './tool/bit/ascii-art/bitacora/', followLinks: false)
      .map((e) => File(e.path))
      .whereType<File>()
      .where((e) => e.path.contains('ansi-shadow.txt'))
      .toList(growable: false);
  return files;
}
