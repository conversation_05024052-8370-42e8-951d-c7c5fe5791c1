import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/attachment/edit_attachment.dart';
import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/attachment_gallery_page_item_controller.dart';
import 'package:flutter/cupertino.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../domain/common/mocks.dart';

class MockAttachmentGalleryPageItemController extends Mock
    implements AttachmentGalleryPageItemController {}

AttachmentGalleryPageItemController mockAttachmentGalleryPageItemController({
  LiveModel<Attachment>? liveAttachment,
}) {
  liveAttachment ??= MockLiveModel<Attachment>();

  final mock = MockAttachmentGalleryPageItemController();
  when(() => mock.editAttachment).thenReturn(EditAttachment(liveAttachment));
  when(() => mock.commentsController).thenReturn(TextEditingController());
  return mock;
}
