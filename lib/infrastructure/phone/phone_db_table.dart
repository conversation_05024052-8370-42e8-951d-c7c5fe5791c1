import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/phone/phone.dart';
import 'package:bitacora/domain/phone/phone_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/phone/phone_db_contract.dart';
import 'package:bitacora/infrastructure/phone/phone_db_fields_builder.dart';
import 'package:bitacora/infrastructure/phone/phone_db_translator.dart';

class PhoneDbTable extends DbTable<Phone, PhoneDbFieldsBuilder>
    implements PhoneRepository<DbContext, PhoneDbFieldsBuilder> {
  PhoneDbTable() : super();

  @override
  PhoneDbContract get contract => const PhoneDbContract();

  @override
  DbTranslator<Phone> get translator => const PhoneDbTranslator();

  @override
  PhoneDbFieldsBuilder get fieldsBuilder => PhoneDbFieldsBuilder();

  @override
  Future<List<Phone>> findAll(DbContext context, LocalId personDetailId) {
    return query(
      context,
      where: '${contract.personDetailId} = ?',
      whereArgs: [personDetailId.dbValue],
    );
  }
}
