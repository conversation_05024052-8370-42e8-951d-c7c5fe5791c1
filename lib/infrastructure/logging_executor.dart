import 'dart:async';
import 'dart:core';
import 'dart:math';

import 'package:bitacora/dev/perf/perf_test_monitor.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:sqflite/sqflite.dart';

class LoggingExecutor implements DatabaseExecutor {
  final DatabaseExecutor executor;

  const LoggingExecutor(this.executor);

  @override
  Batch batch() => executor.batch();

  @override
  Future<int> delete(String table, {String? where, List<Object?>? whereArgs}) {
    return _log(
      ExecutorOperation.delete,
      'delete $table $where $whereArgs',
      () => executor.delete(table, where: where, whereArgs: whereArgs),
    );
  }

  @override
  Future<void> execute(String sql, [List<Object?>? arguments]) {
    return _log(
      ExecutorOperation.execute,
      'execute $sql $arguments',
      () => executor.execute(sql, arguments),
    );
  }

  @override
  Future<int> insert(
    String table,
    Map<String, Object?> values, {
    String? nullColumnHack,
    ConflictAlgorithm? conflictAlgorithm,
  }) {
    return _log(
      ExecutorOperation.insert,
      'insert $table $values $nullColumnHack $conflictAlgorithm',
      () => executor.insert(
        table,
        values,
        nullColumnHack: nullColumnHack,
        conflictAlgorithm: conflictAlgorithm,
      ),
    );
  }

  @override
  Future<List<Map<String, Object?>>> query(
    String table, {
    bool? distinct,
    List<String>? columns,
    String? where,
    List<Object?>? whereArgs,
    String? groupBy,
    String? having,
    String? orderBy,
    int? limit,
    int? offset,
  }) {
    return _log(
      ExecutorOperation.query,
      'query $table $distinct $columns $where $whereArgs $groupBy $having $orderBy $limit $offset',
      () => executor.query(
        table,
        distinct: distinct,
        columns: columns,
        where: where,
        whereArgs: whereArgs,
        groupBy: groupBy,
        having: having,
        orderBy: orderBy,
        limit: limit,
        offset: offset,
      ),
    );
  }

  @override
  Future<int> rawDelete(String sql, [List<Object?>? arguments]) {
    return _log(
      ExecutorOperation.rawDelete,
      'rawDelete $sql $arguments',
      () => executor.rawDelete(sql, arguments),
    );
  }

  @override
  Future<int> rawInsert(String sql, [List<Object?>? arguments]) {
    return _log(
      ExecutorOperation.rawInsert,
      'rawInsert $sql $arguments',
      () => executor.rawInsert(sql, arguments),
    );
  }

  @override
  Future<List<Map<String, Object?>>> rawQuery(String sql,
      [List<Object?>? arguments]) {
    return _log(
      ExecutorOperation.rawQuery,
      'rawQuery $sql $arguments',
      () => executor.rawQuery(sql, arguments),
    );
  }

  @override
  Future<int> rawUpdate(String sql, [List<Object?>? arguments]) {
    return _log(
      ExecutorOperation.rawUpdate,
      'rawUpdate $sql $arguments',
      () => executor.rawUpdate(sql, arguments),
    );
  }

  @override
  Future<int> update(
    String table,
    Map<String, Object?> values, {
    String? where,
    List<Object?>? whereArgs,
    ConflictAlgorithm? conflictAlgorithm,
  }) {
    return _log(
      ExecutorOperation.update,
      'update $table $values $where $whereArgs $conflictAlgorithm',
      () => executor.update(
        table,
        values,
        where: where,
        whereArgs: whereArgs,
        conflictAlgorithm: conflictAlgorithm,
      ),
    );
  }

  Future<T> _log<T>(
      ExecutorOperation operation, String s, FutureOr<T> Function() f) async {
    final start = DateTime.now();
    final result = await f();
    final timeElapsed = DateTime.now().difference(start).inMicroseconds;
    PerfTestMonitor().logOperation(operation, timeElapsed);

    logger.t(
      'db:execute '
      '[+$timeElapsedµs] '
      '${s.replaceAll('\n', ' ').substring(0, min(s.length, 80))}',
    );
    return result;
  }

  @override
  Future<QueryCursor> queryCursor(String table,
      {bool? distinct,
      List<String>? columns,
      String? where,
      List<Object?>? whereArgs,
      String? groupBy,
      String? having,
      String? orderBy,
      int? limit,
      int? offset,
      int? bufferSize}) {
    throw UnimplementedError();
  }

  @override
  Future<QueryCursor> rawQueryCursor(String sql, List<Object?>? arguments,
      {int? bufferSize}) {
    throw UnimplementedError();
  }

  @override
  Database get database => executor.database;
}

enum ExecutorOperation {
  delete,
  execute,
  insert,
  update,
  query,
  rawDelete,
  rawInsert,
  rawQuery,
  rawUpdate;

  @override
  String toString() {
    return super.toString().split('.').last;
  }
}
