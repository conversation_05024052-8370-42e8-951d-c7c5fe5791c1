import 'package:flutter/material.dart';

class TypeaheadSuggestionHighlighted<T> extends StatelessWidget {
  final T suggestion;
  final String Function(T item)? displayStringResolver;
  final String? pattern;
  final Color? highlightColor;
  final VoidCallback? onTap;

  const TypeaheadSuggestionHighlighted({
    super.key,
    required this.pattern,
    required this.suggestion,
    this.displayStringResolver,
    this.highlightColor,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final textStyle = Theme.of(context).textTheme.bodyLarge;
    return ListTile(
      title: Builder(
        builder: (c) {
          final displaySuggestion = displayStringResolver == null
              ? suggestion as String
              : displayStringResolver!(suggestion);

          if (pattern == null || pattern!.isEmpty) {
            return Text(
              displaySuggestion,
              style: textStyle,
            );
          }

          final sanitized = pattern!.replaceAllMapped(
            RegExp(r'[-!$%^&*()_+|~=`{}\[\]:;<>?,.\\/"]'),
            (match) => '\\${match.group(0)}',
          );
          final patternRegex = RegExp(sanitized, caseSensitive: false);
          final matches = patternRegex.allMatches(displaySuggestion);

          final matchStyleBackgroundColor =
              highlightColor ?? Theme.of(c).colorScheme.primary;
          final matchStyle = TextStyle(
            fontWeight: FontWeight.bold,
            backgroundColor: matchStyleBackgroundColor.withValues(alpha: 0.3),
          );

          final children = <InlineSpan>[];
          var startIndex = 0;
          for (final match in matches) {
            children.addAll([
              TextSpan(
                  text: displaySuggestion.substring(startIndex, match.start)),
              TextSpan(
                  text: displaySuggestion.substring(match.start, match.end),
                  style: matchStyle),
            ]);
            startIndex = match.end;
          }
          children.add(TextSpan(text: displaySuggestion.substring(startIndex)));

          return Text.rich(TextSpan(children: children), style: textStyle);
        },
      ),
      dense: true,
      onTap: onTap,
    );
  }
}
