import 'dart:io';

import 'package:bitacora/l10n/app_localizations_en.dart';
import 'package:bitacora/l10n/app_localizations_es.dart';

import 'app_localizations.dart';



/// Use this class to get an `AppLocalization` without a `BuildContext`.
/// i.e. for UI built from background (fcm, workmanager, etc.).
class AppLocalizationsResolver {
  static AppLocalizations get() => Platform.localeName.startsWith('es')
      ? AppLocalizationsEs()
      : AppLocalizationsEn();
}
