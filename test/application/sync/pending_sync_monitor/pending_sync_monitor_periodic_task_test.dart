import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/organization_cache_repository_query.dart';
import 'package:bitacora/application/hook/repository_hook.dart';
import 'package:bitacora/application/sync/background/background_sync_future_scheduler.dart';
import 'package:bitacora/application/sync/pending-sync-monitor/pending_sync_monitor_periodic_task.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/infrastructure/attachment/pending_attachment_upload_repository_query.dart';
import 'package:bitacora/infrastructure/db_lock.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/presentation/user_settings/has_pending_outgoing_mutations_repository_query.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/background_work/bitacora_background_app.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/workmanager/workmanager_utils.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:workmanager/workmanager.dart';

import '../../../domain/common/mocks.dart';
import '../../../infrastructure/mocks.dart';
import '../../../infrastructure/organization/mocks.dart';
import '../../../mocktail_fallback_values.dart';
import '../../../util/workmanager/mocks.dart';
import '../../cache/auth/mocks.dart';
import '../../hook/mocks.dart';

void main() {
  group('$PendingSyncMonitorPeriodicTask tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Injects same', () {
      expect(
          PendingSyncMonitorPeriodicTask(), PendingSyncMonitorPeriodicTask());
    });

    test('Run without pending sync', () async {
      SharedPreferences.setMockInitialValues({});
      final workmanager = mockWorkmanager();
      final pendingSyncMonitor = PendingSyncMonitorPeriodicTask();
      final db = _mockDbRepository();
      final session = mockSession();

      final result = await withInjectedN(
        {
          Workmanager: workmanager,
          DbRepository: db,
          RepositoryHook: mockRepositoryHook(),
          ActiveSession: mockActiveSession(session: session),
        },
        () => pendingSyncMonitor
            .run(BitacoraBackgroundApp(DbLockKey.periodicTask).context),
      );

      expect(result, true);
      verifyNever(
        () => workmanager.registerOneOffTask(
          WorkmanagerTask.backgroundSync.name,
          WorkmanagerTask.backgroundSync.name,
          constraints: any(named: 'constraints'),
          existingWorkPolicy: any(named: 'existingWorkPolicy'),
          initialDelay: any(named: 'initialDelay'),
          backoffPolicy: any(named: 'backoffPolicy'),
        ),
      );
    });

    test('Run with pending sync', () async {
      SharedPreferences.setMockInitialValues({
        SharedPreferencesKeys.hasPendingSync: true,
      });
      final workmanager = mockWorkmanager();
      final pendingSyncMonitor = PendingSyncMonitorPeriodicTask();

      final db = _mockDbRepository();
      final session = mockSession();
      final result = await withInjectedN(
          {
            Workmanager: workmanager,
            DbRepository: db,
            RepositoryHook: mockRepositoryHook(),
            ActiveSession: mockActiveSession(session: session),
          },
          () => pendingSyncMonitor
              .run(BitacoraBackgroundApp(DbLockKey.periodicTask).context));

      expect(result, true);
      verify(
        () => workmanager.registerOneOffTask(
          WorkmanagerTask.backgroundSync.name,
          WorkmanagerTask.backgroundSync.name,
          constraints: any(named: 'constraints'),
          existingWorkPolicy: ExistingWorkPolicy.replace,
          initialDelay: kBackgroundSyncDelay,
          backoffPolicy: BackoffPolicy.exponential,
        ),
      );
    });

    test('Run with attachment pending sync', () async {
      SharedPreferences.setMockInitialValues({});
      final workmanager = mockWorkmanager();
      final db = _mockDbRepository();
      final pendingSyncMonitor = PendingSyncMonitorPeriodicTask();

      whenQuery<PendingAttachmentUploadRepositoryQuery, Attachment?>(
        db,
        (_) => mockAttachment(),
      );

      final result = await withInjectedN(
        {
          DbRepository: db,
          RepositoryHook: mockRepositoryHook(),
          ActiveSession: mockActiveSession(session: mockSession()),
        },
        () => withInjected(
          workmanager,
          () => pendingSyncMonitor
              .run(BitacoraBackgroundApp(DbLockKey.periodicTask).context),
        ),
      );

      expect(result, true);
      verify(
        () => workmanager.registerOneOffTask(
          WorkmanagerTask.backgroundSync.name,
          WorkmanagerTask.backgroundSync.name,
          constraints: any(named: 'constraints'),
          existingWorkPolicy: ExistingWorkPolicy.replace,
          initialDelay: kBackgroundSyncDelay,
          backoffPolicy: BackoffPolicy.exponential,
        ),
      ).called(1);
    });

    test('Run with pending outgoing mutation sync', () async {
      SharedPreferences.setMockInitialValues({});
      final workmanager = mockWorkmanager();
      final db = _mockDbRepository();
      final pendingSyncMonitor = PendingSyncMonitorPeriodicTask();

      whenQuery<HasPendingOutgoingMutationsRepositoryQuery, bool>(
        db,
        (_) => true,
      );

      final result = await withInjectedN(
        {
          DbRepository: db,
          RepositoryHook: mockRepositoryHook(),
          ActiveSession: mockActiveSession(session: mockSession()),
        },
        () => withInjected(
          workmanager,
          () => pendingSyncMonitor
              .run(BitacoraBackgroundApp(DbLockKey.periodicTask).context),
        ),
      );

      expect(result, true);
      verify(
        () => workmanager.registerOneOffTask(
          WorkmanagerTask.backgroundSync.name,
          WorkmanagerTask.backgroundSync.name,
          constraints: any(named: 'constraints'),
          existingWorkPolicy: ExistingWorkPolicy.replace,
          initialDelay: kBackgroundSyncDelay,
          backoffPolicy: BackoffPolicy.exponential,
        ),
      ).called(1);
    });
  });
}

DbRepository _mockDbRepository() {
  final mock = MockDbRepository();
  final mockOrgDb = mockOrganizationDbTable();
  when(() => mock.query(any<PendingAttachmentUploadRepositoryQuery>()))
      .thenAnswer((_) => Future<Attachment?>.value());
  when(() => mock.query(const HasPendingOutgoingMutationsRepositoryQuery(
      ignoreRetryLimit: false))).thenAnswer(
    (_) => Future.value(false),
  );
  when(() => mock.organization).thenReturn(mockOrgDb);
  when(() => mock.query(const OrganizationCacheRepositoryQuery())).thenAnswer(
    (_) => Future.value([mockOrganization()]),
  );

  return mock;
}
