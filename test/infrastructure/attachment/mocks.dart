import 'package:amplify_flutter/amplify_flutter.dart' as amp;
import 'package:bitacora/infrastructure/amplify/amplify_storage_util.dart';
import 'package:bitacora/infrastructure/attachment/s3_syncer/attachment_s3_syncer.dart';
import 'package:mocktail/mocktail.dart';

class MockAttachmentS3SyncerInjector extends Mock
    implements AttachmentS3SyncerInjector {}

class MockAttachmentS3Syncer extends Mock implements AttachmentS3Syncer {}

class MockAmplifyStorageUtil extends Mock implements AmplifyStorageUtil {}

class MockAmplify extends Mock implements Amplify {}

class MockStorageCategory extends Mock implements amp.StorageCategory {}

class MockStorageUploadFileOperation<T extends amp.StorageUploadFileRequest,
        U extends amp.StorageUploadFileResult> extends Mock
    implements amp.StorageUploadFileOperation<T, U> {}

class MockStorageUploadFileResult extends Mock
    implements amp.StorageUploadFileResult {}

class MockAWSFile extends Mock implements amp.AWSFile {}

AttachmentS3SyncerInjector mockAttachmentS3SyncerInjector() {
  final syncer = mockAttachmentS3Syncer();
  final mock = MockAttachmentS3SyncerInjector();
  when(
    () => mock.get(
      db: any(named: 'db'),
      syncTrigger: any(named: 'syncTrigger'),
      syncState: any(named: 'syncState'),
      analyticsLogger: any(named: 'analyticsLogger'),
    ),
  ).thenReturn(syncer);
  return mock;
}

AttachmentS3Syncer mockAttachmentS3Syncer() {
  return MockAttachmentS3Syncer();
}

Amplify mockAmplify({amp.StorageCategory? storage, bool isConfigured = false}) {
  storage ??= mockStorageCategory();
  final mock = MockAmplify();
  when(() => mock.isConfigured).thenReturn(isConfigured);
  when(() => mock.storage).thenReturn(storage);
  when(() => mock.addPlugins(any())).thenAnswer((_) => Future.value());
  when(() => mock.configure(any())).thenAnswer((_) => Future.value());
  return mock;
}

amp.StorageCategory mockStorageCategory() {
  final mock = MockStorageCategory();
  final operation = mockStorageUploadFileOperation();
  when(
    () => mock.uploadFile(
      localFile: any(named: 'localFile'),
      path: any(named: 'path'),
      onProgress: any(named: 'onProgress'),
    ),
  ).thenReturn(operation);
  return mock;
}

amp.StorageUploadFileOperation mockStorageUploadFileOperation() {
  final mock = MockStorageUploadFileOperation();
  when(() => mock.result)
      .thenAnswer((invocation) => Future.value(MockStorageUploadFileResult()));
  return mock;
}
