import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/access/access_fields_builder.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/infrastructure/access/access_db_contract.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';

class AccessDbFieldsBuilder extends DbFieldsBuilder
    implements AccessFieldsBuilder {
  AccessDbFieldsBuilder() {
    _id();
  }

  AccessDbContract get contract => const AccessDbContract();

  AccessDbFieldsBuilder _id() {
    addField(
      AccessField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  AccessFieldsBuilder resourceType() {
    addField(
      AccessField.resourceType,
      DbField(
        column: contract.resourceType,
        valueBuilder: (v) => AccessResourceType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  AccessFieldsBuilder resourceId() {
    addField(
      AccessField.resourceId,
      DbField(
        column: contract.resourceId,
        valueBuilder: (v) => AccessResourceId(v),
      ),
    );
    return this;
  }

  @override
  AccessFieldsBuilder permission() {
    addField(
      AccessField.permission,
      DbField(
        column: contract.permission,
        valueBuilder: (v) => AccessPermission(v),
      ),
    );
    return this;
  }

  @override
  AccessFieldsBuilder rules() {
    addField(
      AccessField.rules,
      DbField(
        column: contract.rules,
        valueBuilder: (v) => AccessRules(v),
      ),
    );
    return this;
  }
}
