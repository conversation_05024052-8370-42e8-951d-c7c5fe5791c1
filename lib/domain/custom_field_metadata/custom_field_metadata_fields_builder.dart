import 'package:bitacora/domain/common/repository_query_context.dart';

abstract class CustomFieldMetadataFieldsBuilder {
  CustomFieldMetadataFieldsBuilder remoteId();

  CustomFieldMetadataFieldsBuilder value();

  CustomFieldMetadataFieldsBuilder fieldName();

  CustomFieldMetadataFieldsBuilder updatedAt();

  CustomFieldMetadataFieldsBuilder createdAt();

  CustomFieldMetadataFieldsBuilder user(Fields fields);

  CustomFieldMetadataFieldsBuilder allowedValue(Fields fields);

  CustomFieldMetadataFieldsBuilder project(Fields fields);

  CustomFieldMetadataFieldsBuilder customField(Fields fields);

  CustomFieldMetadataFieldsBuilder templatelog(Fields fields);

  Fields build();
}
