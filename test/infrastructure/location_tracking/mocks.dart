import 'dart:async';

import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/infrastructure/location_tracking/location_tracking_db_table.dart';
import 'package:mocktail/mocktail.dart';

import '../../domain/common/mocks.dart';

class MockLocationTrackingDbTable extends Mock
    implements LocationTrackingDbTable {}

class MockLocationTrackingModelTranslator extends Mock
    implements ModelTranslator<LocationTracking> {}

LocationTrackingDbTable mockLocationTrackingDbTable({
  LocalId? savedId,
}) {
  final mock = MockLocationTrackingDbTable();
  when(() => mock.save(any(), any())).thenAnswer((invocation) {
    return Future.value(
        savedId ?? LocalId(invocation.positionalArguments[1].uuid.value));
  });
  when(() => mock.delete(any(), any())).thenAnswer((_) => Future.value(1));

  when(() => mock.getMutations()).thenAnswer(
      (_) => StreamController<Mutation<LocationTracking>>.broadcast().stream);
  return mock;
}

ModelTranslator<LocationTracking> mockLocationTrackingModelTranslator({
  bool prepareToMap = true,
  bool prepareFromMap = true,
}) {
  final mock = MockLocationTrackingModelTranslator();
  prepareMockModelTranslator(
    mock,
    prepareToMap: prepareToMap,
    fromMapBuilder: prepareFromMap
        ? (map) => LocationTracking(remoteId: RemoteId(map['id']))
        : null,
  );
  return mock;
}
