import 'dart:async';
import 'dart:convert';

import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:logger/logger.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

const kLogFilenameRegexp = r'bit-log_(?<time>\d+)\.txt';

class LoggerToFileOutput extends LogOutput {
  static final _dateTimeFormat = DateFormat('yyyy-MM-dd HH:mm:ss.S');
  final bool overrideExisting = false;
  final Encoding encoding = utf8;
  final Completer<void> _initCompleter = Completer();
  late final DateTime _startTime;

  final String tag;

  IOSink? _sink;
  String? filePath;

  LoggerToFileOutput(this.tag);

  @override
  Future<void> init() async {
    _startTime = Clock().now();
    filePath ??= path.join(
      (await getLogsDirectory()).path,
      'bit-log_${Clock().now().millisecondsSinceEpoch}.txt',
    );

    final file = FileSystemInjector.get().file(filePath);
    _sink = file.openWrite(
      mode: overrideExisting ? FileMode.writeOnly : FileMode.writeOnlyAppend,
      encoding: encoding,
    );
    _initCompleter.complete();
  }

  static Future<Directory> getLogsDirectory() async {
    final supportDirectory = await getApplicationSupportDirectory();
    final logsDirectory = FileSystemInjector.get().directory(
      path.join(supportDirectory.path, 'logs'),
    );
    if (!logsDirectory.existsSync()) {
      await logsDirectory.create(recursive: true);
    }
    return logsDirectory;
  }

  Future<void> initAsync() => _initCompleter.future;

  @override
  void output(OutputEvent event) {
    final now = Clock().now();
    final elapsedTime = now.difference(_startTime);
    final elapsedTimeFormatted = _formatDuration(elapsedTime);
    final tagPlusElapsed = '${tag.isEmpty ? '' : '$tag-'}$elapsedTimeFormatted';

    if (AppConfig().isDevToolsEnabled || kDebugMode) {
      for (var line in event.lines) {
        // ignore: avoid_print
        print('-bit-$tagPlusElapsed-> $line');
      }
    }

    final formatNow = _dateTimeFormat.format(now);
    _sink?.write('$formatNow $tagPlusElapsed ');
    _sink?.writeAll(event.lines, '\n');
    _sink?.writeln();
  }

  @override
  Future<void> destroy() async {
    await _sink?.flush();
    await _sink?.close();
  }

  Future get done {
    return _sink?.done ?? Future.value();
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes;
    final seconds = duration.inSeconds;
    final millis = duration.inMilliseconds;

    final formattedMinutes =
        minutes > 10 ? '${minutes.remainder(60)}'.padLeft(2, '0') : '$minutes';
    final formattedSeconds =
        seconds > 10 ? '${seconds.remainder(60)}'.padLeft(2, '0') : '$seconds';
    final formattedMillis = '${millis.remainder(1000)}'.padLeft(3, '0');
    return '${hours > 0 ? '${duration.inHours}:' : ''}'
        '${minutes > 0 ? '$formattedMinutes:' : ''}'
        '$formattedSeconds.$formattedMillis';
  }
}
