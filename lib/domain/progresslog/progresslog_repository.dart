import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/progresslog/progresslog_fields_builder.dart';

abstract class ProgresslogRepository<C extends RepositoryQueryContext,
        F extends ProgresslogFieldsBuilder>
    extends RepositoryTable<Progresslog, C, F> {}
