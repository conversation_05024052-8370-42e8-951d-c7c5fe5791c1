import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/application/cache/project/project_cache.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:mocktail/mocktail.dart';

class MockProjectCache extends Mock implements ProjectCache {}

class MockActiveProject extends Mock implements ActiveProject {}

ProjectCache mockProjectCache({
  List<Project>? projects,
  bool hasLoaded = true,
  bool isLoading = false,
}) {
  final mock = MockProjectCache();
  when(() => mock.value).thenAnswer((_) => projects ?? <Project>[]);
  when(() => mock.hasLoaded).thenReturn(hasLoaded);
  when(() => mock.isLoading).thenReturn(isLoading);
  return mock;
}

ActiveProject mockActiveProject({
  Project? project,
  bool hasLoaded = true,
  bool isLoading = false,
}) {
  final mock = MockActiveProject();
  when(() => mock.value).thenAnswer((_) => project);
  when(() => mock.hasLoaded).thenReturn(hasLoaded);
  when(() => mock.isLoading).thenReturn(isLoading);
  when(() => mock.set(any())).thenAnswer((_) => Future.value());
  when(() => mock.maybeSelectActiveProjectAfterSave(any(), any(), any()))
      .thenAnswer((answer) {
    final entry = answer.positionalArguments[2] as Entry;
    if (project == null) {
      return;
    }

    final projects = entry.projects;

    for (final entryProject in projects) {
      if (entryProject.name == project.name) {
        return;
      }
    }

    final project1 = projects.first;
    if (project1.name!.value != kDefaultProjectName) {
      mock.set(project1);
    }
  });
  return mock;
}
