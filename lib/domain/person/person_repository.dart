import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/person/person.dart';
import 'package:bitacora/domain/person/person_fields_builder.dart';

abstract class PersonRepository<C extends RepositoryQueryContext,
    F extends PersonFieldsBuilder> extends RepositoryTable<Person, C, F> {
  Future<Person?> findByUserId(C context, LocalId userId);
}
