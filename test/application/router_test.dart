import 'package:bitacora/application/router.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/attachment/edit_attachment.dart';
import 'package:bitacora/domain/common/edit/edit_collection.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/attachment_gallery_page.dart';
import 'package:bitacora/presentation/home_page_selector.dart';
import 'package:bitacora/presentation/organization/organization_page.dart';
import 'package:bitacora/presentation/organization/organizations_page.dart';
import 'package:bitacora/presentation/report/reports_page.dart';
import 'package:bitacora/presentation/user_settings/user_settings_page.dart';
import 'package:bitacora/util/take_picture/take_picture_page.dart';
import 'package:camera/camera.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import '../mocks.dart';

void main() {
  group('Router tests', () {
    test('Routes to Home', () {
      final route = onGenerateRoute(const RouteSettings(
        name: '/',
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext());

      expect(page is HomePageSelectorWidget, true);
    });

    test('Routes to OrganizationsPage', () {
      final route = onGenerateRoute(const RouteSettings(
        name: kRouteOrganizations,
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext());

      expect(page is OrganizationsPage, true);
    });

    test('Routes to OrganizationPage', () {
      final route = onGenerateRoute(const RouteSettings(
        name: kRouteOrganization,
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext());

      expect(page is OrganizationPage, true);
    });

    test('Routes to UserSettingsPage', () {
      final route = onGenerateRoute(const RouteSettings(
        name: kRouteUserSettings,
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext());

      expect(page is UserSettingsPage, true);
    });

    test('Routes to EntryFormPage', () {
      final route = onGenerateRoute(const RouteSettings(
        name: kRouteEntryForm,
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext()) as EntryFormPage;

      expect(page.inputEntry, null);
    });

    test('Routes to EntryFormPage with Entry', () {
      final entry = mockEntry();
      final route = onGenerateRoute(RouteSettings(
        name: kRouteEntryForm,
        arguments: [entry],
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext()) as EntryFormPage;

      expect(page.inputEntry, entry);
    });

    test('Routes to TakePicturePage', () {
      final cameras = <CameraDescription>[
        const CameraDescription(
          name: 'MockCamera',
          lensDirection: CameraLensDirection.back,
          sensorOrientation: 0,
        )
      ];
      final route = onGenerateRoute(RouteSettings(
        name: kRouteTakePicture,
        arguments: cameras,
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext()) as TakePicturePage;

      expect(page.cameras, cameras);
    });

    test('Routes to AttachmentGalleryPage', () {
      final route = onGenerateRoute(RouteSettings(
        name: kRouteAttachmentGallery,
        arguments: [
          EditCollection<Attachment>(
            [],
            (liveModel) => EditAttachment(liveModel),
          ),
          0
        ],
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext());

      expect(page is AttachmentGalleryPage, true);
    });

    test('Routes to ReportPage', () {
      final route = onGenerateRoute(const RouteSettings(
        name: kRouteReports,
      )) as MaterialPageRoute;

      final page = route.builder(MockBuildContext());

      expect(page is ReportsPage, true);
    });
  });
}
