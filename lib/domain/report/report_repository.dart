import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/domain/report/report_fields_builder.dart';

abstract class ReportRepository<C extends RepositoryQueryContext,
    F extends ReportFieldsBuilder> extends RepositoryTable<Report, C, F> {
  Future<Report?> findLast(C context);

  Future<List<Report>> findAll(C context);

  Future<List<Report>> findAllWithPath(C context);

  Future<List<Report>> findAllDownloaded(C context);
}
