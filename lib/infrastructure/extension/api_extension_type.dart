import 'package:bitacora/domain/extension/extension_type.dart';

extension ApiExtensionType on ExtensionType {
  static ExtensionType fromApiString(String s) {
    switch (s) {
      case 'Worklog':
        return ExtensionType.worklog;
      case 'Inventorylog':
        return ExtensionType.inventorylog;
      case 'Personnellog':
        return ExtensionType.personnellog;
      case 'Progresslog':
        return ExtensionType.progresslog;
      case 'Templatelog':
        return ExtensionType.templatelog;
      default:
        throw ArgumentError('Unexpected extension type api string <$s>');
    }
  }
}
