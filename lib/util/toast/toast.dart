import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/toast/flutter_toast_utils.dart' as ftoast;
import 'package:flutter/material.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';

class Toast {
  @visibleForTesting
  static int kMinToastAppearanceDurationMillis = 300;

  factory Toast() => inject(() => const Toast._());

  const Toast._();

  void showNetworkErrorToast([DateTime? startTime]) {
    final text = AppLocalizationsResolver.get().networkError;
    if (startTime == null) {
      ftoast.FluttertoastUtils()
          .showToast(msg: text, toastLength: ftoast.Toast.LENGTH_LONG);
    } else {
      showToastWithPossibleDelay(startTime, text, ftoast.Toast.LENGTH_LONG);
    }
  }

  void showToastWithPossibleDelay(
    DateTime startTime,
    String text, [
    ftoast.Toast? toastLength,
  ]) {
    final now = Clock().now();
    final difference = now.difference(startTime);
    if (difference.inMilliseconds >= kMinToastAppearanceDurationMillis) {
      ftoast.FluttertoastUtils().showToast(msg: text, toastLength: toastLength);
      return;
    }

    Future.delayed(
      Duration(
          milliseconds:
              kMinToastAppearanceDurationMillis - difference.inMilliseconds),
      () => ftoast.FluttertoastUtils().showToast(
        msg: text,
        toastLength: toastLength,
      ),
    );
  }

  // FIXME: get text or content {String? text, Widget? content} and remove height
  void showToast(
    BuildContext context,
    String text, {
    SnackBarAction? action,
    Duration? duration,
    double? height,
  }) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(isMaterial(context) ? 5 : 16),
        ),
        content: height == null
            ? Text(text)
            : SizedBox(
                height: height,
                child: Center(child: Text(text)),
              ),
        duration: duration ?? const Duration(milliseconds: 4000),
        action: action,
      ),
    );
  }

  void hideToast(BuildContext context) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
  }
}
