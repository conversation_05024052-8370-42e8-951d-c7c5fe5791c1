import 'dart:async';

import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/material.dart';

const _kTimerIntervalDuration = Duration(seconds: 1);
const _kTimeTextFormatChar = '#';

class TimerWidgetController {
  static final _instance = TimerWidgetController._();
  final ValueNotifier<Duration?> elapsed = ValueNotifier(null);
  DateTime? _startedTime;
  Timer? _timer;
  bool isTracking = false;

  TimerWidgetController._();

  factory TimerWidgetController() {
    return _instance;
  }

  bool get isStarted => _timer != null;

  void maybeStart(
      {Duration initialElapsed = Duration.zero, bool isTracking = false}) {
    this.isTracking = isTracking;
    if (_timer != null) {
      return;
    }

    logger.i('timer-widget:start initialElapsed[$initialElapsed]');
    _instance.elapsed.value = initialElapsed;
    _startedTime = Clock().now().subtract(elapsed.value!);
    _timer = Timer.periodic(_kTimerIntervalDuration, (timer) {
      if (elapsed.value == null) {
        timer.cancel();
        return;
      }

      elapsed.value = elapsed.value! + _kTimerIntervalDuration;
    });
  }

  void maybeEnd() {
    if (_startedTime == null) {
      return;
    }

    logger.i('timer-widget:end');
    _startedTime = null;
    _maybeStop();
  }

  void _maybeStop() {
    if (_timer == null) {
      return;
    }

    logger.i('timer-widget:stop');
    _timer!.cancel();
    _timer = null;
    elapsed.value = null;
  }

  void _maybeResume() {
    if (_startedTime == null) {
      return;
    }

    logger.i('timer-widget:resume');
    final now = Clock().now();
    maybeStart(
        initialElapsed: now.difference(_startedTime!), isTracking: isTracking);
  }
}

class TimerWidget extends StatefulWidget {
  final TimerWidgetController controller;
  final TextStyle? textStyle;
  final String textFormat;

  TimerWidget({
    super.key,
    required this.controller,
    this.textStyle,
    this.textFormat = _kTimeTextFormatChar,
  }) {
    assert(
      _kTimeTextFormatChar.allMatches(textFormat).length == 1,
      'TimerWidget: textFormat textFormat must only '
      'have one $_kTimeTextFormatChar character',
    );
  }

  @override
  State<TimerWidget> createState() => _TimerWidgetState();
}

class _TimerWidgetState extends State<TimerWidget> with WidgetsBindingObserver {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.paused:
        logger.i('timer-widget: paused');
        widget.controller._maybeStop();
        break;
      case AppLifecycleState.resumed:
        logger.i('timer-widget: resumed');
        widget.controller._maybeResume();
        break;
      case AppLifecycleState.inactive:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<Duration?>(
      valueListenable: widget.controller.elapsed,
      builder: (context, elapsed, _) {
        if (elapsed == null) {
          return Container();
        }

        return Text(
          widget.textFormat.replaceAll('#', displayDuration(elapsed)),
          style: widget.textStyle ?? Theme.of(context).textTheme.titleLarge,
        );
      },
    );
  }
}
