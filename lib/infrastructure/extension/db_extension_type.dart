import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_contract.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_contract.dart';
import 'package:bitacora/infrastructure/templatelog/templatelog_db_contract.dart';
import 'package:bitacora/infrastructure/worklog/worklog_db_contract.dart';

extension DbExtensionType on ExtensionType {
  DbContract get contract {
    switch (this) {
      case ExtensionType.worklog:
      case ExtensionType.simplelog:
        return const WorklogDbContract();
      case ExtensionType.inventorylog:
        return const InventorylogDbContract();
      case ExtensionType.personnellog:
        return const PersonnellogDbContract();
      case ExtensionType.progresslog:
        return const ProgresslogDbContract();
      case ExtensionType.templatelog:
        return const TemplatelogDbContract();
    }
  }

  String get tableName => contract.tableName;

  String get idColumn => '${contract.prefix}id';
}
