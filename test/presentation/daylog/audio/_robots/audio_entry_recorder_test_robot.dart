import 'package:bitacora/presentation/daylog/audio/audio_entry_listener.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_bar/audio_entry_listener_bar.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_controller.dart';
import 'package:bitacora/presentation/daylog/audio/audio_entry_listener_side_control.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/toast/toast.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../base/base_robot.dart';
import '../../../../test_util.dart';
import '../../../../util/toast/mocks.dart';

class AudioEntryRecorderTestRobot extends BaseRobot {
  final _toast = mockToast();
  final AudioEntryListenerController controller;

  AudioEntryRecorderTestRobot(super.tester, {required this.controller});

  @override
  Future<void> pumpWidget() async {
    await pumpTestApp(
        child: AudioEntryListener(
      controller: controller,
      onHide: () {},
    ));
  }

  void verifyUi() {
    expect(find.byType(AudioEntryListenerBar), findsOneWidget);
    expect(find.byType(AudioEntryListenerSideControl), findsOneWidget);
  }

  Future<void> interrupt(ValueNotifier<bool> isInterrupted) async {
    await tester.runAsync(() async {
      await withInjected<Toast>(_toast, () => isInterrupted.value = true);
      await tester.pumpAndSettle();
    });
  }

  Future<void> verifyInterrupt() async {
    await tester.runAsync(() async {
      expect(find.byWidgetPredicate((w) {
        if (w is! AnimatedContainer) {
          return false;
        }

        if (w.constraints!.minHeight != 75.0) {
          return false;
        }

        return true;
      }), findsOneWidget);

      await Future.delayed(const Duration(milliseconds: 150));
      await awaitUntilStopsThrowing(
        () => verify(
          () => _toast.showToast(
            any(),
            '⚠️ Error due to speech recognition.',
            duration: any(named: 'duration'),
            height: 45.0,
            action: any(named: 'action'),
          ),
        ),
      );

      // FIXME: tests with mock of Future.
    });
  }
}
