import 'dart:io';

import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/application/report/report_service_controller.dart';
import 'package:bitacora/application/report/report_service_context_snapshot.dart';
import 'package:bitacora/application/report/service/report_service.dart';
import 'package:bitacora/application/router.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/filter/entry_filter.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/presentation/report/form/customize/report_form_customize.dart';
import 'package:bitacora/presentation/report/form/filters/report_form_filters.dart';
import 'package:bitacora/presentation/report/form/header/report_form_header.dart';
import 'package:bitacora/presentation/report/form/include/report_form_include.dart';
import 'package:bitacora/presentation/report/form/report_form_controller.dart';
import 'package:bitacora/presentation/report/form/report_form_page_context_snapshot.dart';
import 'package:bitacora/presentation/report/form/report_form_selected_entry_repository_query.dart';
import 'package:bitacora/presentation/report/form/template/report_form_template.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/presentation/widgets/top_snack_bar.dart';
import 'package:bitacora/util/dialog.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/open_file/open_file_util.dart';
import 'package:bitacora/util/parent_builder/parent_builder.dart';
import 'package:bitacora/util/storage/storage_utils.dart';
import 'package:bitacora/util/web_app_launcher.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:uuid/uuid.dart';

class ReportsFormPageProps {
  final EntryFilter? entryFilter;
  final Report? inputReport;
  final Set<Entry>? selectedEntries;

  ReportsFormPageProps(
      {this.inputReport, this.entryFilter, this.selectedEntries}) {
    if (entryFilter != null && inputReport != null) {
      logger.f('Unexpected report form page props');
    }
  }
}

class ReportFormPage extends StatefulWidget {
  final ReportsFormPageProps props;

  ReportFormPage({super.key, ReportsFormPageProps? props})
      : props = props ?? ReportsFormPageProps();

  @override
  State<ReportFormPage> createState() => _ReportFormPageState();

  static void navigate(
    ReportFormPageContextSnapshot contextSnapshot, {
    Set<LocalId>? selectionIds,
    EntryFilter? entryFilter,
  }) async {
    final navigator = contextSnapshot.read<NavigatorState>();
    final isOrgReportEnabled = contextSnapshot
        .read<ActiveOrganization>()
        .value!
        .activePlan!
        .isReportEnabled;
    if (!isOrgReportEnabled) {
      await navigator.pushNamed(kRouteProFeatures);
    } else {
      Set<Entry>? entries;
      if (selectionIds != null) {
        final db = contextSnapshot.read<Repository>();
        entries = {};
        for (final id in selectionIds) {
          entries.add((await db
              .query(ReportFormSelectedEntryRepositoryQuery(id: id)))!);
        }
      }

      await navigator.pushNamed(
        kRouteReportForm,
        arguments: ReportsFormPageProps(
          selectedEntries: entries,
          entryFilter: entryFilter,
        ),
      );
    }
  }
}

class _ReportFormPageState extends State<ReportFormPage> {
  late final ReportServiceController reportServiceController;
  ReportFormController? _controller;
  ReportServiceCreationStatus _creationStatus =
      ReportServiceCreationStatus.creating;
  bool _isCreating = false;
  bool _isOpening = false;
  bool _wasNetworkError = false;
  bool _didFail = false;
  Report? _report;

  @override
  void initState() {
    super.initState();
    reportServiceController =
        ReportServiceController(ReportServiceContextSnapshot(context));
  }

  @override
  Widget build(BuildContext context) {
    _controller ??= ReportFormController(
      context,
      props: widget.props,
    );

    return GestureDetector(
      onTap: FocusScope.of(context).unfocus,
      child: Scaffold(
        appBar: AppBar(
          actions: [
            IconButton(
              onPressed: _isBusy ? null : () => _onTapCreate(context),
              icon: const Icon(Icons.save),
            ),
          ],
          title: Text(AppLocalizations.of(context)!.newReport),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _ReportTopSnackBar(
              creationStatus: _creationStatus,
              isCreating: _isCreating,
              isOpening: _isOpening,
              didFail: _didFail,
              wasNetworkError: _wasNetworkError,
              report: _report,
              onOpen: _open,
              onRetry: () => _onTapCreate(context, _report != null),
            ),
            ParentBuilder(
              builder: (context, child) {
                return Expanded(
                  child: _isBusy
                      ? Opacity(
                          opacity: 0.5,
                          child: IgnorePointer(child: child),
                        )
                      : child,
                );
              },
              child: _ReportForm(controller: _controller!),
            ),
          ],
        ),
      ),
    );
  }

  bool get _isBusy => _isCreating || _isOpening;

  void _onTapCreate(BuildContext context, [bool isRetry = false]) async {
    if (_controller!.formKey.currentState == null ||
        !_controller!.formKey.currentState!.validate()) {
      return;
    }

    final syncState = context.read<SyncState>();
    if (syncState.hasPendingUploads) {
      showDestructiveDialog(
        context: context,
        title: AppLocalizations.of(context)!.syncInProgress,
        message: AppLocalizations.of(context)!.syncReport,
        destroyText: AppLocalizations.of(context)!.create,
        destroyer: () => _createReport(context, isRetry),
      );

      return;
    }

    await _createReport(context, isRetry);
  }

  Future<void> _createReport(BuildContext context, bool isRetry) async {
    setState(() {
      _wasNetworkError = false;
      _didFail = false;
      _isCreating = true;
      if (!isRetry) {
        _report = null;
      }
    });

    try {
      if (isRetry) {
        _report = await reportServiceController.retry(
          _report!.id!,
          callback: _onCreationStatusChanged,
        );
      } else {
        _report = await reportServiceController.create(
          Report(
            uuid: ReportUuid('$kReportUuidLocalPrefix${const Uuid().v4()}'),
            createdAt: ReportCreatedAt(DateTime.now()),
            organization: context.read<ActiveOrganization>().value!,
            postParams: _controller!.buildPostParams(context),
            getParams: _controller!.buildGetParams(context),
          ),
          _onCreationStatusChanged,
        );
      }
    } on DioException catch (_) {
      _isCreating = false;
      _wasNetworkError = true;
      _setStateIfMounted();
      return;
    } catch (_) {
      _isCreating = false;
      _didFail = true;
      _setStateIfMounted();
      return;
    }

    _isCreating = false;
    if (mounted) {
      _open();
    }
  }

  void _open() async {
    setState(() {
      _isOpening = true;
    });

    final path = await StorageUtils()
        .getAbsolutePath(StorageSubdirectory.reports, _report!.path!);
    await OpenFileUtil().open(path!);

    _isOpening = false;
    _setStateIfMounted();
  }

  void _onCreationStatusChanged(status, report) {
    _creationStatus = status;
    _report = report;

    _setStateIfMounted();
  }

  void _setStateIfMounted() {
    if (!mounted) {
      return;
    }
    setState(() {});
  }
}

class _ReportTopSnackBar extends StatelessWidget {
  final ReportServiceCreationStatus creationStatus;
  final bool isCreating;
  final bool isOpening;
  final bool wasNetworkError;
  final bool didFail;
  final Report? report;
  final VoidCallback onOpen;
  final VoidCallback onRetry;

  const _ReportTopSnackBar({
    required this.creationStatus,
    required this.isCreating,
    required this.isOpening,
    required this.wasNetworkError,
    required this.didFail,
    required this.report,
    required this.onOpen,
    required this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    if (wasNetworkError) {
      return TopSnackBar(
        title: Text(AppLocalizations.of(context)!.networkError),
        isError: true,
        action: const SizedBox(),
      );
    }

    if (didFail) {
      return TopSnackBar(
        title: Text(AppLocalizations.of(context)!.reportFailedToCreate),
        isError: true,
        action: OutlinedButton(
          onPressed: onRetry,
          child: Text(AppLocalizations.of(context)!.retry,
              style: TextStyle(
                  color: Theme.of(context).textTheme.bodyLarge!.color)),
        ),
      );
    }

    if (isOpening) {
      return TopSnackBar(
          title: Text('${AppLocalizations.of(context)!.opening}...'));
    }

    if (report?.isDownloaded?.value ?? false) {
      return TopSnackBar(
        title: Text(report!.path!.displayValue),
        action: OutlinedButton(
          onPressed: onOpen,
          child: Text(AppLocalizations.of(context)!.open),
        ),
      );
    }

    if (!isCreating) {
      return const SizedBox();
    }

    switch (creationStatus) {
      case ReportServiceCreationStatus.creating:
        return TopSnackBar(
          title: Text('${AppLocalizations.of(context)!.creating}...'),
        );
      case ReportServiceCreationStatus.processing:
        return TopSnackBar(
          title: Text('${AppLocalizations.of(context)!.processing}...'),
        );
      case ReportServiceCreationStatus.waiting:
        return TopSnackBar(
          title: Text('${AppLocalizations.of(context)!.syncing}...'),
        );
    }
  }
}

class _ReportForm extends StatelessWidget {
  final ReportFormController controller;

  const _ReportForm({required this.controller});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: kPageVerticalInsets,
        child: Form(
          key: controller.formKey,
          child: Column(
            children: [
              ReportFormHeader(controller: controller.headerController),
              if (!controller.headerController.isExcel) ...[
                ReportFormTemplate(
                  controller: controller.templateController,
                  reportType: controller.headerController.entryType,
                ),
                ReportFormCustomize(
                  controller: controller.customizeController,
                ),
                ReportFormInclude(controller: controller.includeController),
              ],
              ReportFormFilters(controller: controller.filtersController),
              SizedBox(height: MediaQuery.sizeOf(context).height * 0.3),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    child:
                        Text(AppLocalizations.of(context)!.usePreviousVersion),
                    onPressed: () => WebAppLauncher().maybeLaunchBitacoraWebApp(
                      context.read<RecoverSessionLauncher>(),
                      Navigator.of(context),
                      context.read<ActiveSession>(),
                      Localizations.localeOf(context).languageCode,
                      'reports',
                      launchMode: Platform.isAndroid
                          ? LaunchMode.externalApplication
                          : LaunchMode.inAppWebView,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
