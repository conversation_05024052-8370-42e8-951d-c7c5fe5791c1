import 'dart:async';

import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/feed_post/feed_post.dart';
import 'package:bitacora/domain/feed_post/feed_post_repository.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/domain/resource/resource_repository.dart';
import 'package:bitacora/presentation/daylog/app_bar/feed/daylog_app_bar_feed_badge_repository_query.dart';
import 'package:bitacora/presentation/daylog/app_bar/feed/daylog_app_bar_show_icon_repository_query.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entries_loader.dart';
import 'package:bitacora/util/infinity_loader.dart';
import 'package:mocktail/mocktail.dart';

import '../../../domain/common/mocks.dart';
import '../../../domain/entry/mocks.dart';

class MockLogListEntriesLoader extends Mock implements LogListEntriesLoader {}

ResourceRepository _mockResourceRepository() {
  final mock = MockResourceRepository();
  when(() => mock.getMutations()).thenAnswer((_) {
    final controller = StreamController<Mutation<Resource>>.broadcast();
    return controller.stream;
  });
  return mock;
}

FeedPostRepository _mockFeedRepository() {
  final mock = MockFeedPostRepository();
  when(() => mock.getMutations()).thenAnswer((_) {
    final controller = StreamController<Mutation<FeedPost>>.broadcast();
    return controller.stream;
  });
  return mock;
}

Repository mockRepository() {
  final mock = MockRepository();
  final feedPost = _mockFeedRepository();
  final resource = _mockResourceRepository();
  final queryScope = mockQueryScope();

  when(() => mock.feedPost).thenReturn(feedPost);
  when(() => mock.resource).thenReturn(resource);
  when(() => mock.queryScope(orgId: any(named: 'orgId')))
      .thenReturn(queryScope);
  when(() => mock.query(const DaylogAppBarFeedShowIconRepositoryQuery(),
          context: any(named: 'context')))
      .thenAnswer((invocation) => Future.value(true));
  when(() => mock.query(const DaylogAppBarFeedBadgeRepositoryQuery(),
          context: any(named: 'context')))
      .thenAnswer((invocation) => Future.value(true));
  final context = mockRepositoryQueryContext(db: mock);
  when(() => mock.context(queryScope: any(named: 'queryScope')))
      .thenReturn(context);
  return mock;
}

LogListEntriesLoader mockLogListEntriesLoader() {
  final mock = MockLogListEntriesLoader();
  when(() => mock.streamState).thenReturn(StreamState.done);
  return mock;
}
