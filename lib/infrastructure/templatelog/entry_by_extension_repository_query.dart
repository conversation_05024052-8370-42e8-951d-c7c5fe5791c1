import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';

class EntryByExtensionRepositoryQuery extends RepositoryQuery<Entry?> {
  final ExtensionType extensionType;
  final LocalId extensionId;

  const EntryByExtensionRepositoryQuery(this.extensionType, this.extensionId);

  @override
  Future<Entry?> run(RepositoryQueryContext context) {
    return context.db.entry
        .findByExtension(context, extensionType, extensionId);
  }

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) {
    return db.entry.fieldsBuilder.build();
  }
}
