import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/report/report.dart';

class ReportsPageListRepositoryQuery extends RepositoryQuery<List<Report>> {
  const ReportsPageListRepositoryQuery();

  @override
  Future<List<Report>> run(RepositoryQueryContext context) =>
      context.db.report.findAll(context);

  @override
  Fields fields(Repository db) => db.report.fieldsBuilder
      .createdAt()
      .isDownloaded()
      .getParams()
      .postParams()
      .path()
      .build();
}
