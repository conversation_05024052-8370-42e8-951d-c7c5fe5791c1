import 'package:bitacora/infrastructure/attachment/has_pending_attachment_uploads_repository_query.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';

import '../../domain/attachment/mocks.dart';
import '../../domain/common/mocks.dart';
import '../../mocktail_fallback_values.dart';

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
  });

  group('$HasPendingAttachmentUploadsRepositoryQuery tests', () {
    test('No pending changes', () async {
      final attachmentDbTable = MockAttachmentRepository();
      when(() => attachmentDbTable.hasPendingUploads(any()))
          .thenAnswer((invocation) => Future.value(false));
      final db = MockRepository();
      when(() => db.attachment).thenReturn(attachmentDbTable);
      final context = MockRepositoryQueryContext();
      when(() => db.context(queryScope: any(named: 'queryScope')))
          .thenReturn(context);
      when(() => context.db).thenReturn(db);
      const query = HasPendingAttachmentUploadsRepositoryQuery();

      final hasPending = await query.query(context);

      expect(hasPending, false);
    });

    test('Has pending changes', () async {
      final attachmentDbTable = MockAttachmentRepository();
      when(() => attachmentDbTable.hasPendingUploads(any()))
          .thenAnswer((invocation) => Future.value(true));
      final db = MockRepository();
      when(() => db.attachment).thenReturn(attachmentDbTable);
      final context = MockRepositoryQueryContext();
      when(() => db.context(queryScope: any(named: 'queryScope')))
          .thenReturn(context);
      when(() => context.db).thenReturn(db);
      const query = HasPendingAttachmentUploadsRepositoryQuery();

      final hasPending = await query.query(context);

      expect(hasPending, true);
    });
  });
}
