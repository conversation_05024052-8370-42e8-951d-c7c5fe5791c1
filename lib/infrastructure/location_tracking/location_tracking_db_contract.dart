import 'dart:async';

import 'package:bitacora/domain/location_tracking/value/location_tracking_owner_type.dart';
import 'package:bitacora/domain/location_tracking/value/location_tracking_status.dart';
import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class LocationTrackingDbContract extends DbContract {
  static const String _ = 'lt_';
  static const String _tableName = 'location_tracking';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String uuid = '${_}uuid';
  final String status = '${_}status';
  final String isLocal = '${_}isLocal';
  final String ownerType = '${_}ownerType';
  final String createdAt = '${_}createdAt';

  const LocationTrackingDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithLocationTrackingTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $uuid STRING UNIQUE,
    $status INTEGER NOT NULL DEFAULT ${LocationTrackingStatus.idle.dbValue},
    $isLocal INTEGER NOT NULL DEFAULT 0,
    $ownerType INTEGER NOT NULL,
    $createdAt INTEGER
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithUserLocationTracking) {
      await db.execute('ALTER TABLE $tableName ADD $ownerType INTEGER '
          'NOT NULL DEFAULT ${LocationTrackingOwnerType.entry.dbValue}');
    }
  }
}
