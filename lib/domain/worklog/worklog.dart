import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/worklog/value/worklog_cost_price.dart';
import 'package:bitacora/domain/worklog/value/worklog_payment_status.dart';
import 'package:bitacora/domain/worklog/value/worklog_price_is_unit.dart';
import 'package:bitacora/domain/worklog/value/worklog_provider.dart';
import 'package:bitacora/domain/worklog/value/worklog_quantity.dart';
import 'package:bitacora/domain/worklog/value/worklog_sale_price.dart';
import 'package:bitacora/domain/worklog/value/worklog_sublocation.dart';
import 'package:bitacora/domain/worklog/value/worklog_title.dart';
import 'package:bitacora/domain/worklog/value/worklog_type.dart';

export 'package:bitacora/domain/worklog/value/worklog_cost_price.dart';
export 'package:bitacora/domain/worklog/value/worklog_payment_status.dart';
export 'package:bitacora/domain/worklog/value/worklog_price_is_unit.dart';
export 'package:bitacora/domain/worklog/value/worklog_provider.dart';
export 'package:bitacora/domain/worklog/value/worklog_quantity.dart';
export 'package:bitacora/domain/worklog/value/worklog_sale_price.dart';
export 'package:bitacora/domain/worklog/value/worklog_sublocation.dart';
export 'package:bitacora/domain/worklog/value/worklog_title.dart';
export 'package:bitacora/domain/worklog/value/worklog_type.dart';

class Worklog extends Extension {
  final WorklogTitle? title;
  final WorklogQuantity? quantity;
  final WorklogSublocation? sublocation;
  final WorklogCostPrice? costPrice;
  final WorklogSalePrice? salePrice;
  final WorklogPaymentStatus? paymentStatus;
  final WorklogPriceIsUnit? priceIsUnit;
  final WorklogProvider? provider;
  final WorklogType? type;

  // FIXME: wrap nested models in valueobjects
  final Project? project;

  Worklog({
    super.id,
    super.remoteId,
    this.title,
    this.quantity,
    this.sublocation,
    this.costPrice,
    this.salePrice,
    this.paymentStatus,
    this.priceIsUnit,
    this.provider,
    this.type,
    this.project,
  });

  @override
  ExtensionType get extensionType => ExtensionType.worklog;

  @override
  Map<Field, dynamic> get fields => {
        WorklogField.id: id,
        WorklogField.remoteId: remoteId,
        WorklogField.title: title,
        WorklogField.quantity: quantity,
        WorklogField.sublocation: sublocation,
        WorklogField.costPrice: costPrice,
        WorklogField.salePrice: salePrice,
        WorklogField.paymentStatus: paymentStatus,
        WorklogField.priceIsUnit: priceIsUnit,
        WorklogField.provider: provider,
        WorklogField.type: type,
        WorklogField.project: project,
      };
}

enum WorklogField with Field {
  id,
  remoteId,
  title,
  quantity,
  sublocation,
  costPrice,
  salePrice,
  paymentStatus,
  priceIsUnit,
  provider,
  type,
  project,
}

const worklogNestedModelFields = {
  WorklogField.project,
};
