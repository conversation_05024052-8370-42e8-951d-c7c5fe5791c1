import 'dart:async';
import 'dart:math';

import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/logday/active_log_day.dart';
import 'package:bitacora/application/cache/metadata/active_custom_field_metadata_filter.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/cache/project/active_project.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/infrastructure/access/access_fix_unset_entry_cache.dart';
import 'package:bitacora/presentation/daylog/daylog_scroll_coordinator.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entries_loader.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entry_view.dart';
import 'package:bitacora/presentation/widgets/loading_indicator.dart';
import 'package:bitacora/presentation/widgets/sad.dart';
import 'package:bitacora/util/infinity_loader.dart';
import 'package:bitacora/util/list_entry/list_entry.dart';
import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:provider/provider.dart';

const _kLoadingIndicatorWaitDuration = Duration(milliseconds: 250);

class DaylogListView extends StatefulWidget {
  const DaylogListView({super.key});

  @override
  State<DaylogListView> createState() => _DaylogListViewState();
}

class _DaylogListViewState extends State<DaylogListView>
    with TickerProviderStateMixin {
  final ScrollController _scrollController = ScrollController();
  late final ActiveLogDay _activeLogDay;

  AnimationController? _topDividerController;
  Animation? _topDividerAnimation;

  GlobalKey _listKey = GlobalKey();
  List<ListEntry> _entries = [];
  LogListEntriesLoader? _entriesLoader;
  bool _isCollectionDirty = false;
  Timer? _showLoadingTimer;
  bool _isLoadingShowing = false;

  AnimationController get topDividerController =>
      _topDividerController ??= AnimationController(
        vsync: this,
        duration: const Duration(microseconds: 0),
      );

  Animation get topDividerAnimation => _topDividerAnimation ??= CurvedAnimation(
        parent: topDividerController,
        curve: Curves.easeIn,
      );

  @override
  void initState() {
    super.initState();

    _activeLogDay = context.read<ActiveLogDay>();
    _activeLogDay.addListener(_maybeFixEntryAccess);
    _maybeFixEntryAccess();
  }

  @override
  void dispose() {
    _topDividerController?.dispose();
    _scrollController.dispose();
    _activeLogDay.removeListener(_maybeFixEntryAccess);
    super.dispose();
  }

  void _maybeFixEntryAccess() async {
    final day = context.read<ActiveLogDay>().value;
    if (day == null) {
      return;
    }

    AccessFixUnsetEntryCache().addPriorityDayRange(minDay: day, maxDay: day);
  }

  @override
  Widget build(BuildContext context) {
    _prepareEntriesLoader(context);

    return StreamBuilder<List<ListEntry>>(
        stream: _entriesLoader?.entries,
        builder: (context, snapshot) {
          if (snapshot.connectionState != ConnectionState.waiting &&
              _isCollectionDirty) {
            /// FIXME: if snapshot.connectionState == ConnectionState.waiting
            /// && _isCollectionDirty, trigger cleanup collection in X ms
            SchedulerBinding.instance.addPostFrameCallback((_) {
              topDividerController.animateTo(0);
            });
            _isCollectionDirty = false;
            _entries = [];
            _listKey = GlobalKey();
          }

          if (snapshot.connectionState == ConnectionState.waiting) {
            _showLoadingTimer ??= Timer(_kLoadingIndicatorWaitDuration, () {
              setState(() {
                _isLoadingShowing = true;
              });
            });
          } else {
            _showLoadingTimer?.cancel();
            _showLoadingTimer = null;
            _isLoadingShowing = false;
          }

          if (_isLoadingShowing) {
            return const Center(child: LoadingIndicator());
          }

          if (_entries.isEmpty) {
            if (snapshot.hasError) {
              return const Sad();
            } else if (!snapshot.hasData || snapshot.data!.isEmpty) {
              return const SizedBox();
            }
          }

          if (snapshot.hasData) {
            _entries = snapshot.data!;
          }

          return Stack(children: [
            _buildListView(context, _entriesLoader!.streamState),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: kSpacing),
              child: AnimatedBuilder(
                animation: topDividerController,
                builder: (_, child) =>
                    Opacity(opacity: topDividerAnimation.value, child: child),
                child: Divider(
                  thickness: 0.5,
                  height: 0.5,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ]);
        });
  }

  Widget _buildListView(BuildContext context, StreamState streamState) {
    final isIdle = streamState == StreamState.idle;
    final slidingPanelHeight =
        context.watch<DaylogScrollCoordinator>().bottomHeight;
    final bottomPadding = MediaQuery.paddingOf(context).bottom;
    const famPadding = 50;
    const famExtraPadding = 80;
    return NotificationListener<ScrollNotification>(
      onNotification: (scrollInfo) {
        SchedulerBinding.instance.addPostFrameCallback((_) {
          topDividerController.animateTo(scrollInfo.metrics.pixels / 30);
        });
        return false;
      },
      child: ListView.builder(
        key: _listKey,
        controller: _scrollController,
        padding: EdgeInsets.only(
          top: 8,
          bottom: max(slidingPanelHeight, bottomPadding + famPadding) +
              famExtraPadding,
        ),
        itemCount: isIdle ? _entries.length + 1 : _entries.length,
        itemBuilder: (BuildContext context, int index) {
          if (index >= _entries.length - 5) {
            if (_entriesLoader!.streamState == StreamState.idle) {
              _entriesLoader!.loadMoreThan(_entries.length);
            }
          }

          if (isIdle && index == _entries.length) {
            return const Center(child: LoadingIndicator());
          }

          return LogListEntryView(entry: _entries[index]);
        },
      ),
    );
  }

  void _prepareEntriesLoader(BuildContext context) {
    _entriesLoader ??= LogListEntriesLoader(
      context.read<Repository>(),
      context.read<ActiveSession>().value!.user,
      AppConfig().isGhostEntriesEnabled,
    );
    _entriesLoader!.org = context.watch<ActiveOrganization>().value;
    _entriesLoader!.day = context.watch<ActiveLogDay>().value;
    _entriesLoader!.project = context.watch<ActiveProject>().value;
    _entriesLoader!.metadataFilter =
        context.watch<ActiveCustomFieldMetadataFilter>().value;

    if (_entriesLoader!.streamState == StreamState.staging) {
      _isCollectionDirty = true;
      _entriesLoader!.loadMoreThan(0);
      if (_scrollController.hasClients) {
        _scrollController.animateTo(0,
            duration: const Duration(milliseconds: 300), curve: Curves.ease);
      }
    }
  }
}
