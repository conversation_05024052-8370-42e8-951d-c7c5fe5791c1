import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/report/field/report_entry_type.dart';
import 'package:bitacora/domain/report/field/report_format.dart';
import 'package:bitacora/domain/report/value/report_created_at.dart';
import 'package:bitacora/domain/report/value/report_get_params.dart';
import 'package:bitacora/domain/report/value/report_is_downloaded.dart';
import 'package:bitacora/domain/report/value/report_path.dart';
import 'package:bitacora/domain/report/value/report_post_params.dart';
import 'package:bitacora/domain/report/value/report_uuid.dart';

export 'package:bitacora/domain/report/value/report_created_at.dart';
export 'package:bitacora/domain/report/value/report_get_params.dart';
export 'package:bitacora/domain/report/value/report_is_downloaded.dart';
export 'package:bitacora/domain/report/value/report_path.dart';
export 'package:bitacora/domain/report/value/report_post_params.dart';
export 'package:bitacora/domain/report/value/report_uuid.dart';

class Report extends Model {
  final ReportUuid? uuid;
  final ReportPath? path;
  final ReportIsDownloaded? isDownloaded;
  final ReportCreatedAt? createdAt;
  final ReportPostParams? postParams;
  final ReportGetParams? getParams;

  final Organization? organization;

  const Report({
    super.id,
    this.uuid,
    this.path,
    this.isDownloaded,
    this.createdAt,
    this.postParams,
    this.getParams,
    this.organization,
  });

  Report copyWith({
    LocalId? id,
    ReportUuid? uuid,
    ReportCreatedAt? createdAt,
    ReportPath? path,
    ReportIsDownloaded? isDownloaded,
  }) {
    return Report(
      id: id ?? this.id,
      uuid: uuid ?? this.uuid,
      path: path ?? this.path,
      isDownloaded: isDownloaded ?? this.isDownloaded,
      createdAt: createdAt ?? this.createdAt,
      postParams: postParams,
      getParams: getParams,
      organization: organization,
    );
  }

  ReportFormat? get format =>
      postParams?.format ??
      getParams?.format ??
      (path?.value == null ? null : ReportFormat.fromFilename(path!.value!));

  ReportEntryType? get entryType =>
      postParams?.entryType ??
      (getParams?.entryType == null ? null : getParams!.entryType!);

  int? get minDate => postParams?.minDate ?? getParams?.minDate;

  int? get maxDate => postParams?.maxDate ?? getParams?.maxDate;

  String? get template => postParams?.template;

  @override
  Map<Field, dynamic> get fields => {
        ReportField.id: id,
        ReportField.uuid: uuid,
        ReportField.path: path,
        ReportField.isDownloaded: isDownloaded,
        ReportField.createdAt: createdAt,
        ReportField.postParams: postParams,
        ReportField.getParams: getParams,
        ReportField.organization: organization,
      };
}

enum ReportField with Field {
  id,
  uuid,
  path,
  isDownloaded,
  createdAt,
  postParams,
  getParams,
  organization,
}

const reportNestedModelFields = {
  ReportField.organization,
};
