import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/custom_field/value/custom_field_created_at.dart';
import 'package:bitacora/domain/custom_field/value/custom_field_deleted_at.dart';
import 'package:bitacora/domain/custom_field/value/custom_field_name.dart';
import 'package:bitacora/domain/custom_field/value/custom_field_type.dart';
import 'package:bitacora/domain/custom_field/value/custom_field_updated_at.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/organization/organization.dart';

export 'package:bitacora/domain/custom_field/value/custom_field_created_at.dart';
export 'package:bitacora/domain/custom_field/value/custom_field_deleted_at.dart';
export 'package:bitacora/domain/custom_field/value/custom_field_name.dart';
export 'package:bitacora/domain/custom_field/value/custom_field_type.dart';
export 'package:bitacora/domain/custom_field/value/custom_field_updated_at.dart';
export 'package:bitacora/domain/custom_field_options/custom_field_options.dart';

class CustomField extends Model {
  final CustomFieldName? name;
  final CustomFieldType? type;
  final CustomFieldDeletedAt? deletedAt;
  final CustomFieldCreatedAt? createdAt;
  final CustomFieldUpdatedAt? updatedAt;

  final List<CustomFieldAllowedValue>? allowedValues;
  final CustomField? parent;
  final Organization? organization;

  CustomField({
    super.id,
    super.remoteId,
    this.name,
    this.type,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
    this.allowedValues,
    this.parent,
    this.organization,
  });

  CustomField copyWith({
    CustomFieldName? name,
    CustomFieldType? type,
    CustomFieldDeletedAt? deletedAt,
    CustomFieldCreatedAt? createdAt,
    CustomFieldUpdatedAt? updatedAt,
    List<CustomFieldAllowedValue>? allowedValues,
    CustomField? parent,
    Organization? organization,
  }) {
    return CustomField(
      id: id,
      remoteId: remoteId,
      name: name ?? this.name,
      type: type ?? this.type,
      deletedAt: deletedAt ?? this.deletedAt,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      allowedValues: allowedValues ?? this.allowedValues,
      parent: parent ?? this.parent,
      organization: organization ?? this.organization,
    );
  }

  @override
  Map<Field, dynamic> get fields => {
        CustomFieldField.id: id,
        CustomFieldField.remoteId: remoteId,
        CustomFieldField.name: name,
        CustomFieldField.type: type,
        CustomFieldField.deletedAt: deletedAt,
        CustomFieldField.updatedAt: updatedAt,
        CustomFieldField.createdAt: createdAt,
        CustomFieldField.allowedValues: allowedValues,
        CustomFieldField.parent: parent,
        CustomFieldField.organization: organization,
      };
}

enum CustomFieldField with Field {
  id,
  remoteId,
  name,
  type,
  deletedAt,
  updatedAt,
  createdAt,
  allowedValues,
  parent,
  organization,
}

const customFieldNestedModelFields = {
  CustomFieldField.allowedValues,
  CustomFieldField.parent,
  CustomFieldField.organization,
};
