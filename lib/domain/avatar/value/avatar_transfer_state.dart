import 'package:bitacora/domain/common/value_object/value_object.dart';

enum AvatarTransferStateValue { na, done, inProgress, failed }

class AvatarTransferState extends ValueObject<AvatarTransferStateValue> {
  static AvatarTransferState get na =>
      AvatarTransferState(AvatarTransferStateValue.na);

  static AvatarTransferState get done =>
      AvatarTransferState(AvatarTransferStateValue.done);

  static AvatarTransferState get inProgress =>
      AvatarTransferState(AvatarTransferStateValue.inProgress);

  static AvatarTransferState get failed =>
      AvatarTransferState(AvatarTransferStateValue.failed);

  AvatarTransferState(super.value);

  @override
  bool isSameType(Object other) {
    return other is AvatarTransferState;
  }

  factory AvatarTransferState.fromDbValue(int? value) {
    return AvatarTransferState(AvatarTransferStateValue.values[value ?? 0]);
  }

  @override
  int get dbValue => value.index;
}
