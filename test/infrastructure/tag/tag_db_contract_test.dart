import 'package:bitacora/infrastructure/tag/tag_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$TagDbContract tests', () {
    test('Create $TagDbContract', () {
      expectRemovingSpaces(
        const TagDbContract().create,
        '''
        CREATE TABLE tag (
          t_id INTEGER PRIMARY KEY AUTOINCREMENT,
          t_remoteId INTEGER UNIQUE,
          t_name TEXT NOT NULL,
          t_color INTEGER NOT NULL,
          t_organizationId INTEGER NOT NULL
        )
        ''',
      );
    });
  });
}
