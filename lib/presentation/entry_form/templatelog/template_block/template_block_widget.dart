import 'package:bitacora/domain/custom_field/value/custom_field_type.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_allowed_values_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_date_time_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_multi_value_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_checkbox.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_checkbox_group.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_date_time.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_model.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_number.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_radio.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_select.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_text.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/widgets/template_block_form_text_area.dart';
import 'package:bitacora/presentation/entry_form/templatelog/user_name_suggestion_repository_query.dart';
import 'package:bitacora/presentation/entry_form/util/project_name_suggestion_repository_query.dart';
import 'package:flutter/material.dart';

class TemplateBlockWidget extends StatelessWidget {
  final TemplateBlockFormController controller;
  final void Function(dynamic)? onFieldSubmitted;
  final TextInputAction? texInputAction;

  const TemplateBlockWidget({
    super.key,
    required this.controller,
    this.onFieldSubmitted,
    this.texInputAction,
  });

  @override
  Widget build(BuildContext context) {
    switch (
        controller.templateBlock.customFieldOptions!.customField!.type!.value) {
      case CustomFieldTypeValue.text:
        return TemplateBlockFormText(
          controller: controller as TemplateBlockTextFormController,
          onFieldSubmitted: onFieldSubmitted,
          texInputAction: texInputAction,
        );
      case CustomFieldTypeValue.textArea:
        return TemplateBlockFormTextArea(
          controller: controller as TemplateBlockTextFormController,
          onFieldSubmitted: onFieldSubmitted,
          texInputAction: texInputAction,
        );
      case CustomFieldTypeValue.intNumber:
      case CustomFieldTypeValue.floatNumber:
        return TemplateBlockFormNumber(
          controller: controller as TemplateBlockTextFormController,
          onFieldSubmitted: onFieldSubmitted,
          texInputAction: texInputAction,
        );
      case CustomFieldTypeValue.time:
      case CustomFieldTypeValue.date:
      case CustomFieldTypeValue.dateTime:
        return TemplateBlockFormDateTime(
          controller: controller as TemplateBlockDateTimeFormController,
          onFieldSubmitted: onFieldSubmitted,
          texInputAction: texInputAction,
        );
      case CustomFieldTypeValue.select:
        return TemplateBlockFormSelect(
          controller: controller as TemplateBlockAllowedValuesFormController,
          texInputAction: texInputAction,
          onFieldSubmitted: onFieldSubmitted,
        );
      case CustomFieldTypeValue.radio:
        return TemplateBlockFormRadio(
            controller: controller as TemplateBlockAllowedValuesFormController);
      case CustomFieldTypeValue.checkbox:
        return TemplateBlockFormCheckbox(
            controller: controller as TemplateBlockBoolFormController);
      case CustomFieldTypeValue.checkboxGroup:
        return TemplateBlockFormCheckboxGroup(
            controller: controller as TemplateBlockMultiValueFormController<
                CustomFieldAllowedValue>);
      case CustomFieldTypeValue.project:
        return TemplateBlockFormModel(
          suggestionQuery: const ProjectNameSuggestionRepositoryQuery(),
          controller: controller as TemplateBlockTextFormController,
          onFieldSubmitted: onFieldSubmitted,
          texInputAction: texInputAction,
        );
      case CustomFieldTypeValue.user:
        return TemplateBlockFormModel(
          suggestionQuery: const UserNameSuggestionRepositoryQuery(),
          controller: controller as TemplateBlockTextFormController,
          onFieldSubmitted: onFieldSubmitted,
          texInputAction: texInputAction,
          isNewable: false,
          allowNews: false,
          validator: (text) {
            final user = User.fromNameEmailDisplayValue(text);
            if (user == null) {
              return '';
            }

            return null;
          },
        );
    }
  }
}
