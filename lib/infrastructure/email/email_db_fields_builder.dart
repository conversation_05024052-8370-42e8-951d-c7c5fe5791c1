import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/email/email.dart';
import 'package:bitacora/domain/email/email_post_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/email/email_db_contract.dart';

class EmailDbFieldsBuilder extends DbFieldsBuilder
    implements EmailFieldsBuilder {
  EmailDbFieldsBuilder() {
    _id();
  }

  EmailDbContract get contract => const EmailDbContract();

  EmailDbFieldsBuilder _id() {
    addField(
      EmailField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  EmailDbFieldsBuilder remoteId() {
    addField(
      EmailField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  EmailDbFieldsBuilder value() {
    addField(
      EmailField.value,
      DbField(
        column: contract.value,
        valueBuilder: (v) => EmailValue(v),
      ),
    );
    return this;
  }

  @override
  EmailDbFieldsBuilder type() {
    addField(
      EmailField.type,
      DbField(
        column: contract.type,
        valueBuilder: (v) => PersonDetailContactType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  EmailDbFieldsBuilder updatedAt() {
    addField(
      EmailField.updatedAt,
      DbField(
        column: contract.updatedAt,
        valueBuilder: (v) =>
            EmailUpdatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  EmailDbFieldsBuilder createdAt() {
    addField(
      EmailField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) =>
            EmailCreatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  EmailFieldsBuilder personDetail(Fields fields) {
    addField(
      EmailField.personDetail,
      DbField(
        key: EmailField.personDetail,
        column: contract.personDetailId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.personDetail.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
