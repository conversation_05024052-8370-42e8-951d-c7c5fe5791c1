import 'package:bitacora/domain/common/repository_query_context.dart';

abstract class ProjectFieldsBuilder {
  ProjectFieldsBuilder remoteId();

  ProjectFieldsBuilder name();

  ProjectFieldsBuilder description();

  ProjectFieldsBuilder address();

  ProjectFieldsBuilder location();

  ProjectFieldsBuilder type();

  ProjectFieldsBuilder syncLastEntryUpdatedAt();

  ProjectFieldsBuilder syncLastSyncTime();

  ProjectFieldsBuilder syncNextPageToken();

  ProjectFieldsBuilder isSyncable();

  ProjectFieldsBuilder organization(Fields fields);

  Fields build();
}
