import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation.dart';
import 'package:bitacora/domain/outgoing_mutation/outgoing_mutation_fields_builder.dart';
import 'package:bitacora/domain/outgoing_mutation/value/outgoing_mutation_model_type.dart';

abstract class OutgoingMutationRepository<C extends RepositoryQueryContext,
        F extends OutgoingMutationFieldsBuilder>
    extends RepositoryTable<OutgoingMutation, C, F> {
  Future<OutgoingMutation?> findNext(C context, bool ignoreRetryLimit);

  Future<bool> hasPendingChanges(C context, bool ignoreRetryLimit);

  Future<List<OutgoingMutation>> findPending(C context);

  Future<bool> hasExistingOutgoingMutationForModel(
      C context, OutgoingMutationModelType modelType, LocalId modelId);

  Future<bool> hasExistingOutgoingMutationForModelRemoteId(
      C context, OutgoingMutationModelType modelType, RemoteId remoteId);
}
