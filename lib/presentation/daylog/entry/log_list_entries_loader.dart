import 'dart:async';

import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/presentation/daylog/entry/log_list_entries_repository_query.dart';
import 'package:bitacora/util/infinity_loader.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/limit_offset_cursor.dart';
import 'package:bitacora/util/list_entry/list_entry.dart';
import 'package:bitacora/util/throttler.dart';
import 'package:collection/collection.dart';

class LogListEntriesLoader {
  final Repository db;
  final User user;
  final bool isGhostEntriesEnabled;
  final bool isOpenEntries;
  final LocalId? assigneeId;
  final _mutationThrottler = ThrottlerInjector().get();

  InfinityLoader<ListEntry>? _infinityLoader;
  StreamSubscription? _mutationStreamSubscription;

  LogDay? _day;
  Project? _project;
  Organization? _org;
  CustomFieldMetadata? _metadataFilter;

  factory LogListEntriesLoader(
    Repository db,
    User user,
    bool isGhostEntriesEnabled, [
    bool isOpenEntries = false,
    LocalId? assigneeId,
  ]) =>
      inject(() => LogListEntriesLoader._(
            db,
            user,
            isGhostEntriesEnabled,
            isOpenEntries,
            assigneeId,
          ));

  LogListEntriesLoader._(
    this.db,
    this.user,
    this.isGhostEntriesEnabled,
    this.isOpenEntries,
    this.assigneeId,
  );

  set day(LogDay? value) {
    if (value != _day) {
      _day = value;
      _disposeInfinityLoader();
    }
  }

  set project(Project? value) {
    if (value?.id != _project?.id) {
      _disposeInfinityLoader();
    }
    _project = value;
  }

  set org(Organization? value) {
    if (value?.id != _org?.id) {
      _disposeInfinityLoader();
    }
    _org = value;
  }

  set metadataFilter(CustomFieldMetadata? value) {
    if (value?.value != _metadataFilter?.value ||
        value?.customField!.id! != _metadataFilter?.customField!.id!) {
      _disposeInfinityLoader();
    }
    _metadataFilter = value;
  }

  void loadMoreThan(int n) {
    _infinityLoader ??= InfinityLoader<ListEntry>((limit, offset) async {
      final rawList = await _loadPage(LimitOffsetCursor(limit, offset));
      return rawList.map((e) => ListEntry(e, _project)).toList(growable: false);
    });

    _mutationStreamSubscription ??= db.entry.getMutations().listen((event) {
      if (!_shouldReloadFromEntryMutation(event)) {
        return;
      }

      _mutationThrottler.add(() {
        _infinityLoader!.reloadToCurrentPage();
      });
    });
    _infinityLoader!.loadMoreThan(n);
  }

  Stream<List<ListEntry>>? get entries => _infinityLoader?.items;

  StreamState get streamState => _infinityLoader == null
      ? StreamState.staging
      : _infinityLoader!.streamState;

  void dispose() {
    _disposeInfinityLoader();
  }

  Future<List<Entry>> _loadPage(LimitOffsetCursor cursor) async {
    if (_org == null && _project == null) {
      return [];
    }

    if (!isOpenEntries && _day == null) {
      return [];
    }

    final queryContext = _metadataFilter == null
        ? db.context(
            cursor: cursor,
            queryScope: db.queryScope(
              userId: user.id,
              orgId: _org?.id,
              projectId: _project?.id,
            ),
          )
        : db.context(
            cursor: cursor,
            queryScope: db.queryScope(
              userId: user.id,
              orgId: _org?.id,
              metadataFilter: _metadataFilter,
            ),
          );

    return db.query(
      LogListEntriesRepositoryQuery(
        day: _day,
        isOpenEntries: isOpenEntries,
        isGhostEntriesEnabled: isGhostEntriesEnabled,
        assigneeId: assigneeId,
      ),
      context: queryContext,
    );
  }

  void _disposeInfinityLoader() {
    _infinityLoader?.dispose();
    _infinityLoader = null;
    _mutationStreamSubscription?.cancel();
    _mutationStreamSubscription = null;
  }

  bool _shouldReloadFromEntryMutation(Mutation<Entry> event) {
    switch (event.type.value) {
      case MutationTypeValue.insert:
        if (isOpenEntries) {
          return event.model!.openState != null &&
              event.model!.openState!.progress!.value < 100;
        }

        return event.model!.day! == _day || _day == null;
      case MutationTypeValue.update:
        if (_isEntryInCurrentCollection(event.id!)) {
          return true;
        }

        if (isOpenEntries) {
          // Can't turn entry into open entry.
          // We only reload if a completed openEntry becomes incomplete (100->x)
          return (event.model!.openState?.progress?.value ?? 100) < 100;
        }

        return event.model!.day == _day || _day == null;
      case MutationTypeValue.delete:
        return _isEntryInCurrentCollection(event.id!);
      case MutationTypeValue.unknown:
        return true;
    }
  }

  bool _isEntryInCurrentCollection(LocalId entryId) {
    if (_infinityLoader == null) {
      return false;
    }

    if (_infinityLoader!.streamState == StreamState.loading) {
      // We are currently loading and can't be sure that entryId is in the
      // collection. Assume true.
      return true;
    }

    final list = _infinityLoader!.results;
    return list.firstWhereOrNull((e) => e.entry.id == entryId) != null;
  }
}
