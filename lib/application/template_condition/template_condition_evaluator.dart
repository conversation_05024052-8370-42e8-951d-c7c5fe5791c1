import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/template_condition/template_condition.dart';
import 'package:bitacora/domain/template_condition/value/template_condition_operator.dart';

class TemplateConditionEvaluator {
  bool evaluate(dynamic value, TemplateCondition condition) {
    switch (condition.operator!.value) {
      case TemplateConditionOperatorValue.eq:
        return _equal(value, condition.conditionValue);
      case TemplateConditionOperatorValue.ne:
        return _notEqual(value, condition.conditionValue);
      case TemplateConditionOperatorValue.gt:
        return _greaterThan(value, condition.conditionValue);
      case TemplateConditionOperatorValue.ge:
        return _greaterThanOrEqual(value, condition.conditionValue);
      case TemplateConditionOperatorValue.lt:
        return _lessThan(value, condition.conditionValue);
      case TemplateConditionOperatorValue.le:
        return _lessThanOrEqual(value, condition.conditionValue);
      case TemplateConditionOperatorValue.nin:
        return _notInside(value, condition.conditionValue);
      case TemplateConditionOperatorValue.inside:
        return _inside(value, condition.conditionValue);
      case TemplateConditionOperatorValue.any:
      case TemplateConditionOperatorValue.like:
        throw 'TemplateConditionEvaluator: Operator [${condition.operator!.value}] not supported ';
    }
  }

  bool _equal(dynamic value, dynamic conditionValue) {
    if (value.runtimeType == conditionValue.runtimeType) {
      return value == conditionValue;
    }

    return _normalize(value) == _normalize(conditionValue);
  }

  bool _notEqual(dynamic value, dynamic conditionValue) {
    if (value.runtimeType == conditionValue.runtimeType) {
      return value != conditionValue;
    }

    return _normalize(value) != _normalize(conditionValue);
  }

  bool _greaterThan(dynamic value, dynamic conditionValue) {
    if (value.runtimeType == conditionValue.runtimeType && value is Comparable) {
      return value.compareTo(conditionValue) > 0;
    }

    final normalizedValue = _normalize(value);
    final normalizedConditionValue = _normalize(conditionValue);

    if (normalizedValue is Comparable && normalizedConditionValue is Comparable) {
      return normalizedValue.compareTo(normalizedConditionValue) > 0;
    }

    return false;
  }

  bool _greaterThanOrEqual(dynamic value, dynamic conditionValue) {
    if (value.runtimeType == conditionValue.runtimeType && value is Comparable) {
      return value.compareTo(conditionValue) >= 0;
    }

    final normalizedValue = _normalize(value);
    final normalizedConditionValue = _normalize(conditionValue);

    if (normalizedValue is Comparable && normalizedConditionValue is Comparable) {
      return normalizedValue.compareTo(normalizedConditionValue) >= 0;
    }

    return false;
  }

  bool _lessThan(dynamic value, dynamic conditionValue) {
    if (value.runtimeType == conditionValue.runtimeType && value is Comparable) {
      return value.compareTo(conditionValue) < 0;
    }

    final normalizedValue = _normalize(value);
    final normalizedConditionValue = _normalize(conditionValue);

    if (normalizedValue is Comparable && normalizedConditionValue is Comparable) {
      return normalizedValue.compareTo(normalizedConditionValue) < 0;
    }

    return false;
  }

  bool _lessThanOrEqual(dynamic value, dynamic conditionValue) {
    if (value.runtimeType == conditionValue.runtimeType && value is Comparable) {
      return value.compareTo(conditionValue) <= 0;
    }

    final normalizedValue = _normalize(value);
    final normalizedConditionValue = _normalize(conditionValue);

    if (normalizedValue is Comparable && normalizedConditionValue is Comparable) {
      return normalizedValue.compareTo(normalizedConditionValue) <= 0;
    }

    return false;
  }

  bool _notInside(dynamic value, List conditionValue) {
    final normalizedValue = _normalize(value);
    if (normalizedValue is List<String>) {
      final contain = normalizedValue.any((e) {
        return conditionValue.contains(e);
      });

      return !contain;
    }

    return !conditionValue.contains(normalizedValue);
  }

  bool _inside(dynamic value, List conditionValue) {
    final normalizedValue = _normalize(value);
    if (normalizedValue is List<String>) {
      final contain = normalizedValue.any((e) {
        return conditionValue.contains(e);
      });

      return contain;
    }

    return conditionValue.contains(normalizedValue);
  }

  dynamic _normalize(dynamic value) {
    if (value is bool) {
      return value ? 1 : 0;
    } else if (value is String) {
      final numValue = num.tryParse(value);
      if (numValue != null) {
        return numValue;
      }
      return value.toLowerCase();
    } else if (value is List<CustomFieldAllowedValue>) {
      return value.map((e) => e.value!.value).toList(growable: false);
    }
    return value;
  }
}
