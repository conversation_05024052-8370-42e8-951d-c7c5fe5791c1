Estrategias de sync


(1) Bitacora v1
 Hacer sync completo y mantener sincronizado en el fondo con notificaciones del servidor (pub-sub)

 Overfetching -> F reprobado
  Esto es, los datos a los que tiene acceso el usuario, pero no tiene interés (nunca observará).
  El potencial de overfetching de esta estrategia es máxima. i.e. se descarga toda.
  El problema de overfetching aplica con mayor probabilidad en el sync inicial.

 Capacidades locales -> S super
   Los datos se pueden ver offline.
   Se pueden hacer queries ~ complejos para obtener conteos/estadísticas/gráficas/sugerencias, etc.
   El desarrollo de features es muy simple porque podemos satisfacer las dependencias localmente.
   El UI es inmediato.

 Realtime -> A
   Funciona y es relativamente simple de implementar.

 Peso -> C
   El peso crece linealmente con el volumen de datos (tiempo, proyectos, tamaño de organización, etc.)
   El sync inicial es potencialmente pesado (usuarios/sesiones nuevas en organizaciones/proyectos viejos).
   El sync es potencialmente pesado.
   Durante el uso de la aplicación, el peso es muy ligero (no hay queries al api para resolver dependencias).

 Conclusiones:
   Esta solución es incompatible para organizaciones grandes que estén interesadas en alta transparencia
     i.e. que los usuarios en general tengan acceso a datos de otros equipos

   Los límites de usabilidad son desconocidos (1,000 registros por día?, 10,000?, 100,000?, etc.)
     i.e. performance de sync, queries, memoria, etc.

   Es compatible para organizaciones grandes secretivas, pero habría que instalar y clarificar los límites.

   Requiere lidiar con los zombies (apps que están sincronizando pero olvidadas por el usuario).

   Si el overfetching es 0% -> es la solución más eficiente
   Si el overfetching es 100% -> es la solución más ineficiente




(2) Head-sync
 Arrancar de cero, saltarnos el sync inicial (ignorar el pasado), pero mantener sincronizada la cabeza (todos los
 cambios a partir de x fecha).

 Overfetching -> D reprobado
   Se resuelve el problema de overfetching en el sync inicial, pero sigue existiendo en sus otras formas.

 Capacidades locales -> C
   Se pierde la capacidad de resolver todas las dependencias localmente y offline.
   Los queries locales pueden estar incompletos o equivocados.
   El desarrollo de algunos features sería más complejo.
     i.e. actualizar/filtrar sugerencias, doble swipe al día previo con datos, stats y gráficas, registros abiertos, etc
   El UI puede ser más lento.

 Realtime -> A
   Funciona y es relativamente simple de implementar.

 Peso -> C
   Se pierde el peso del sync inicial.
   Se gana el peso de todos los queries que se realizan para resolver los huecos en los datos.
   Muchos más queries comparado con estrategia (1) excepto por el sync inicial.

 Conclusiones:
   No se resuelven los problemas de escala.

   Se alivia el peso del sync inicial, pero no queda claro si se pierde por los queries subsecuentes.

   Se reducen drásticamente las capacidades locales.

   Parece ser un half-measure.




(3) On-demand
 Solo sincronizar datos selectos durante sesiones activas, lo demás se asume incompleto y se descarga bajo demanda.

 Overfetching -> A+ medalla de excelencia
   Únicamente los datos de interés se descargan, el potencial de overfetching es mínimo.

 Capacidades locales -> D reprobado
   Los mismos problemas de capacidades limitadas de estrategia (2), pero amplificados.
   La disponibilidad de datos depende siempre de la conexión a internet en el momento de la verdad.

 Realtime -> A
   Funciona, pero ocuparíamos suscripciones granulares e implementaciones ad-hoc.
    i.e. no usaríamos las mismas estrategias que en (1) y (2)

 Peso -> B
   Se pierde el peso del sync.
   Se gana el peso de todos los queries que se realizan para resolver los huecos en los datos.

 Conclusiones:
   Se resuelven los problemas de escala.

   Se reducen drásticamente las capacidades locales.

   Es potencialmente más ineficiente.

   Parece ser un extremo en la tendencia hacia evitar overfetching.




(4) Sincronización controlada
 Mantener sincronizadas colecciones que se consideren sincronizables (i.e. juzgando por su tamaño)
 La decisión de cuáles colecciones sincronizar pueden realizarse en el cliente o en el servidor.
 La solución más robusta implica que el servidor mantenga conocimiento del estado de la base de datos del cliente.
 Criterio determinista: si el tamaño de la colección es < X
 Criterio adaptivo: si el tamaño del hueco es < X (i.e. solo falta descargar X registros para estar sincronizados)
 Sincronizar usando un criterio adaptivo es difícil si no sabemos qué datos faltan. Soluciones:
  (a) Mantener conocimiento en el servidor del estado de la base de datos del cliente
     -> Suena a un proyecto grande, pero con mucho potencial.
  (b) Mandar un snapshot de la base de datos local (lista de IDs) para completar
     -> No escala.
  (c) Mantener conocimiento local de queries previos. i.e. (tablas con el estado de sincronización)
     -> Suena a un proyecto mediano, pero sí me parece factible poder cerrar esto de forma eficiente.

 Overfetching -> A
   El problema de over-fetching no aplicará en sus instancias problemáticas (colecciones no-sincronizables).
   Sigue existiendo para colecciones sincronizadas, pero esto no debe de representar un problema serio.

 Capacidades locales -> B
   Dependiente del porcentaje de la base de datos que está siendo sincronizada.
   Se habilitirían algunos features, y permitiría implementaciones simples para algunos otros.
   Para contenido sincronizado, el UI es inmediato.
   Habría posibilidad de habilitar algunos features de forma dinámica (dependiendo del estado de sincronización)

 Realtime -> A
   Igual que (3).

 Peso -> A
   Se facilita calibrar parámetros para mantener el peso en un estado aceptable.
   Las soluciones con criterios adaptivos nos permitirían soluciones eficientes.
   Es ligero en los contextos que sync es ligero, es ligero en los contextos en que on-demand es ligero.

 Conclusiones:
   Se resuelven los problemas de escala.

   Se pierden algunas capacidades locales.

   Es probablemente la solución más ligera.

   Las organizaciones chicas mantendrían sus capacidades, ya que su estado de sincronización sería elevado.

   Parece ser la solución ideal, pero requiere de más trabajo que (1), principalmente para llegar a la par en cuanto
   a las capacidades locales, y mejorar el desempeño con las sincronizaciones adaptivas.

   Si no nos interesan organizaciones grandes transparentes, la solución ideal sigue siendo la (1).





