import 'dart:async';

import 'package:amplify_flutter/amplify_flutter.dart';
import 'package:bitacora/analytics/analytics_events.dart' as event;
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/infrastructure/amplify/amplify_storage_util.dart';
import 'package:bitacora/infrastructure/attachment/s3_syncer/attachment_s3_syncer.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/logger/logger.dart';

class AttachmentS3SyncerFileUploader {
  final Repository db;
  final AnalyticsLogger analyticsLogger;
  final SaveTransferStateCallback saveTransferState;

  AttachmentS3SyncerFileUploader({
    required this.db,
    required this.analyticsLogger,
    required this.saveTransferState,
  });

  Future<void> upload(
    Attachment attachment,
    List<LocalId> failedUploads,
  ) async {
    try {
      await _upload(attachment, failedUploads);
    } catch (e) {
      logger.f('attachment:upload failed $e');

      final isSystemicException =
          e is StorageAccessDeniedException || e is NetworkException;
      await saveTransferState(
        attachment.s3Key!,
        AttachmentTransferState.failed,
        context: db.context(
          props: {kAttachmentTransferSystemicFail: isSystemicException},
        ),
      );

      if (isSystemicException) {
        rethrow;
      }

      failedUploads.add(attachment.id!);
      unawaited(analyticsLogger.logEvent(
        event.AnalyticsEvent.uploadAttachmentFailed,
        props: {
          event.kAnalyticsPropUploadAttachmentFailedError: e.toString(),
          event.kAnalyticsPropUploadAttachmentFailedS3:
              attachment.s3Key!.apiValue,
          event.kAnalyticsPropUploadAttachmentTransferAttempts:
              attachment.transferAttempts!.value,
        },
      ));
    }
  }

  Future<void> _upload(
    Attachment attachment,
    List<LocalId> failedUploads,
  ) async {
    final s3key = attachment.s3Key!;
    await saveTransferState(s3key, AttachmentTransferState.inProgress);
    final sourcePath =
        (await AttachmentUtils().getAbsolutePath(attachment.path!))!;

    if (await _maybeMarkAsMissing(sourcePath, attachment)) {
      logger.f('attachment:upload failed file does not exist');
      return;
    }

    await AmplifyStorageUtil().uploadFile(
      path: sourcePath,
      key: s3key.value,
    );
    logger.i('attachment:upload done key:${s3key.value} '
        's3key: ${attachment.s3Key}');

    // FIXME: maybe entry was deleted during upload?
    await saveTransferState(s3key, AttachmentTransferState.done);
    await db.entry.save(
      db.context(),
      Entry(id: attachment.entry!.id),
      requestSync: true,
    );
    unawaited(analyticsLogger.logEvent(
      event.AnalyticsEvent.uploadAttachmentSucceeded,
      props: {
        event.kAnalyticsPropUploadAttachmentSucceededS3:
            attachment.s3Key!.apiValue,
        event.kAnalyticsPropUploadAttachmentTransferAttempts:
            attachment.transferAttempts!.value,
      },
    ));
  }

  Future<bool> _maybeMarkAsMissing(String path, Attachment attachment) async {
    if (!FileSystemInjector.get().file(path).existsSync()) {
      await saveTransferState(
        attachment.s3Key!,
        AttachmentTransferState.failed,
        isDownloaded: AttachmentIsDownloaded(false),
        path: const AttachmentPath(null),
      );
      unawaited(
        analyticsLogger.logEvent(
          event.AnalyticsEvent.uploadAttachmentFailed,
          props: {
            event.kAnalyticsPropUploadAttachmentFailedError: 'File missing'
          },
        ),
      );
      return true;
    }
    return false;
  }
}
