import 'package:bitacora/domain/session/value/session_token.dart';
import 'package:bitacora/domain/user/user.dart';

export 'package:bitacora/domain/session/value/session_token.dart';

const kInvalidToken = 498;
const kInvalidApi = 426;

class Session {
  final SessionToken token;
  final User user;
  final int? invalidStatusCode;

  const Session({
    required this.token,
    required this.user,
    this.invalidStatusCode,
  });

  int get id => int.parse(token.value.split('_')[1]);

  bool get isValid => invalidStatusCode == null;
}
