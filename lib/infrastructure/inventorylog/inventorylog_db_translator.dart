import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/inventorylog/inventorylog_db_contract.dart';

class InventorylogDbTranslator implements DbTranslator<Inventorylog> {
  const InventorylogDbTranslator();

  @override
  Set<Field> get nestedModelFields => inventorylogNestedModelFields;

  @override
  Future<Inventorylog> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Inventorylog(
      id: fields[InventorylogField.id]?.value(map),
      remoteId: fields[InventorylogField.remoteId]?.value(map),
      type: fields[InventorylogField.type]?.value(map),
      quantity: fields[InventorylogField.quantity]?.value(map),
      itemName: fields[InventorylogField.itemName]?.value(map),
      sourceSublocation:
          fields[InventorylogField.sourceSublocation]?.value(map),
      destSublocation: fields[InventorylogField.destSublocation]?.value(map),
      provider: fields[InventorylogField.provider]?.value(map),
      costPrice: fields[InventorylogField.costPrice]?.value(map),
      salePrice: fields[InventorylogField.salePrice]?.value(map),
      reason: fields[InventorylogField.reason]?.value(map),
      paymentStatus: fields[InventorylogField.paymentStatus]?.value(map),
      priceIsUnit: fields[InventorylogField.priceIsUnit]?.value(map),
      sourceProject:
          await fields[InventorylogField.sourceProject]?.nested(context, map),
      destProject:
          await fields[InventorylogField.destProject]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, Inventorylog model) async {
    final map = <String, dynamic>{};
    const contract = InventorylogDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.type, model.type);
    addField(map, contract.quantity, model.quantity);
    addField(map, contract.itemName, model.itemName);
    addField(map, contract.sourceSublocation, model.sourceSublocation);
    addField(map, contract.destSublocation, model.destSublocation);
    addField(map, contract.provider, model.provider);
    addField(map, contract.costPrice, model.costPrice);
    addField(map, contract.salePrice, model.salePrice);
    addField(map, contract.reason, model.reason);
    addField(map, contract.paymentStatus, model.paymentStatus);
    addField(map, contract.priceIsUnit, model.priceIsUnit);

    await saveNestedModel<Project>(context, map, contract.destProjectId,
        context.db.project, model.destProject);
    await saveNestedModel<Project>(context, map, contract.sourceProjectId,
        context.db.project, model.sourceProject);
    return map;
  }
}
