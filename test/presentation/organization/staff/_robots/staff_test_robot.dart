import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/application/user_invite/user_invite_service.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/domain/user_invite/user_invite.dart';
import 'package:bitacora/domain/user_invite/value/user_invite_status.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/presentation/organization/staff/active_organization_staff_repository_query.dart';
import 'package:bitacora/presentation/organization/staff/staff_list.dart';
import 'package:bitacora/presentation/organization/staff/user_invites_for_org_page_repository_query.dart';
import 'package:bitacora/util/clipboard.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../../../application/api/mocks.dart';
import '../../../../application/cache/auth/mocks.dart';
import '../../../../application/cache/organization/mocks.dart';
import '../../../../application/sync/mocks.dart';
import '../../../../application/user_invite/mocks.dart';
import '../../../../base/base_robot.dart';
import '../../../../base/test_app.dart';
import '../../../../infrastructure/mocks.dart';
import '../../../../infrastructure/user/mocks.dart';
import '../../../../infrastructure/user_invite/mocks.dart';
import '../../../../util/mocks.dart';

class StaffTestRobot extends BaseRobot {
  final clipboard = mockClipboard();
  final userInviteService = mockUserInviteService();
  final userInviteDbTable = mockUserInviteDbTable();
  late final Repository repository;

  StaffTestRobot(super.tester) {
    repository = _mockRepository();
  }

  @override
  Future<void> pumpWidget({
    Organization? organization,
    List<User>? users,
    List<UserInvite>? userInvites,
  }) async {
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          Provider<Repository>(
              create: (_) =>
                  _mockRepository(users: users, userInvites: userInvites)),
          Provider<ApiHelper>(create: (_) => MockApiHelper()),
          Provider<SyncTrigger>(create: (_) => mockSyncTrigger()),
          ChangeNotifierProvider<ActiveOrganization>(
              create: (_) => mockActiveOrganization(
                  organization: organization ?? mockOrganization())),
          ChangeNotifierProvider<ActiveSession>(
              create: (_) => mockActiveSession(session: mockSession()))
        ],
        child: const TestApp(child: CustomScrollView(slivers: [StaffList()])),
      ),
    );

    await tester.pump();
  }

  Future<void> longPressUser(User user) async {
    return withInjected<Clipboard>(
      clipboard,
      () => longPress(find.text(user.email!.displayValue)),
    );
  }

  void verifyCopyEmail(User user) {
    verify(() => clipboard.copyText(user.email!.displayValue)).called(1);
  }

  Future<void> expandFailedUserInvite(UserInvite userInvite) async {
    final finder = find.text(userInvite.email!.displayValue);
    await tester.tap(finder);
    await tester.pumpAndSettle();
  }

  void verifyFailedUserInviteIsExpanded(UserInvite userInvite) {
    expect(find.byWidgetPredicate((w) {
      if (w is! ExpansionTile) {
        return false;
      }

      final title = w.title as Text;
      if (title.data != userInvite.email!.displayValue) {
        return false;
      }

      final padding = w.children[0] as Padding;
      final expandContent = padding.child as Row;
      final iconButtons = expandContent.children;

      final iconReplay = (iconButtons[0] as IconButton).icon as Icon;
      if (iconReplay.icon != Icons.replay) {
        return false;
      }

      final iconDelete = (iconButtons[1] as IconButton).icon as Icon;
      if (iconDelete.icon != Icons.delete) {
        return false;
      }

      return true;
    }), findsOneWidget);
  }

  Future<void> resendUserInvite(UserInvite userInvite) async {
    await withInjected<UserInviteService>(
      userInviteService,
      () async {
        await expandFailedUserInvite(userInvite);
        await tester.tap(find.byIcon(Icons.replay));
      },
    );
  }

  void verifyResendUserInvite(UserInvite userInvite) async {
    final captured =
        verify(() => userInviteService.sendUserInvite(captureAny(), any()));
    expect(captured.callCount, 1);
    expect(captured.captured.first, userInvite.id);
  }

  Future<void> deleteUserInvite(UserInvite userInvite) async {
    await expandFailedUserInvite(userInvite);
    await tester.tap(find.byIcon(Icons.delete));
  }

  void verifyDeleteUserInvite(UserInvite userInvite) async {
    final captured =
        verify(() => userInviteDbTable.delete(any(), captureAny()));
    expect(captured.callCount, 1);
    expect(captured.captured.first, userInvite.id);
  }

  void verifyStaff(List<User> users) {
    expect(find.byType(ListTile), findsNWidgets(users.length));
    users.map((user) {
      expect(find.text(user.name!.displayValue), findsOneWidget);
      expect(find.text(user.email!.displayValue), findsOneWidget);
    });
  }

  void verifyUserInviteList(List<UserInvite> userInvites) {
    var failedUserInvitesCount = 0;
    var inProgressUserInvitesCount = 0;
    for (final userInvite in userInvites) {
      if (userInvite.status!.value == UserInviteStatus.failed) {
        failedUserInvitesCount++;
      } else if (userInvite.status!.value == UserInviteStatus.pending) {
        inProgressUserInvitesCount++;
      }
    }

    expect(
      find.text('⚠️ Error sending invite'),
      findsNWidgets(failedUserInvitesCount),
    );
    expect(
        find.text('In Progress...'), findsNWidgets(inProgressUserInvitesCount));
    expect(
      find.byType(PlatformCircularProgressIndicator),
      findsNWidgets(inProgressUserInvitesCount),
    );
  }

  Repository _mockRepository({
    List<User>? users,
    List<UserInvite>? userInvites,
  }) {
    final mock = MockDbRepository();
    final userDbTable = mockUserDbTable();
    when(() => mock.user).thenReturn(userDbTable);
    when(() => mock.userInvite).thenReturn(userInviteDbTable);
    when(() => mock.context(queryScope: any(named: 'queryScope')))
        .thenReturn(DbContext(db: mock));
    when(() => mock.queryScope(orgId: any(named: 'orgId')))
        .thenReturn(const QueryScope(orgId: LocalId(1)));
    when(
      () => mock.query(
        const UserInvitesForOrgPageRepositoryQuery(),
        context: any(named: 'context'),
      ),
    ).thenAnswer((_) => Future.value(userInvites ?? <UserInvite>[]));
    when(
      () => mock.query(
        const OrganizationStaffRepositoryQuery(),
        context: any(named: 'context'),
      ),
    ).thenAnswer((_) => Future.value(users ?? <User>[mockUser()]));
    return mock;
  }
}
