import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:firebase_crashlytics/firebase_crashlytics.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';

class AnalyticsWidget extends StatefulWidget {
  final Widget child;

  const AnalyticsWidget({super.key, required this.child});

  @override
  State<AnalyticsWidget> createState() => _AnalyticsWidgetState();
}

class _AnalyticsWidgetState extends State<AnalyticsWidget> {
  RemoteId? lastUserId = const RemoteId(-1);

  @override
  Widget build(BuildContext context) {
    _onAuthCache(
      context.watch<ActiveSession>(),
      context.read<AnalyticsLogger>(),
    );

    return widget.child;
  }

  void _onAuthCache(
    ActiveSession activeSession,
    AnalyticsLogger analyticsLogger,
  ) {
    if (!activeSession.hasLoaded) {
      return;
    }

    final session = activeSession.value;
    if (session != null) {
      if (session.user.remoteId != lastUserId) {
        logger.d('analytics:widget onAuthCache with user id');
        final userId = '${session.user.remoteId!.value}';
        analyticsLogger.registerSuperprops({kAnalyticsPropUserId: userId});
        FirebaseCrashlytics.instance.setUserIdentifier(userId);
      }
      lastUserId = session.user.remoteId;
    } else {
      if (lastUserId != null) {
        logger.d('analytics:widget onAuthCache without user id');
        analyticsLogger.unregisterSuperprop(kAnalyticsPropUserId);
        analyticsLogger.setUserId(null);
        FirebaseCrashlytics.instance.setUserIdentifier('');
      }
      lastUserId = null;
    }
  }
}
