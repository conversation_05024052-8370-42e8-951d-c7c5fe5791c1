import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/resource/resource.dart';

class FeedResourcesRepositoryQuery extends RepositoryQuery<List<Resource>> {
  FeedResourcesRepositoryQuery();

  @override
  Future<List<Resource>> run(RepositoryQueryContext context) =>
      context.db.resource.findAll(context);

  @override
  Fields fields(Repository db) => db.resource.fieldsBuilder
      .s3Key()
      .name()
      .isDownloaded()
      .transferState()
      .path()
      .type()
      .metadata()
      .build();
}
