import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/product/product.dart';

class HasAiProductRepositoryQuery extends RepositoryQuery<bool> {
  final LocalId userId;
  final LocalId organizationId;
  final ProductUuid productUuid;

  HasAiProductRepositoryQuery(
    this.userId,
    this.organizationId,
    this.productUuid,
  );

  @override
  Future<bool> run(RepositoryQueryContext context) => context.db.product
      .hasProduct(context, userId, organizationId, productUuid);
}
