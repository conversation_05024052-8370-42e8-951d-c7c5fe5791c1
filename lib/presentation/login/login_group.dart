import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/auth_app_service.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/dev/perf/perf_test_monitor.dart';
import 'package:bitacora/presentation/login/validators.dart';
import 'package:bitacora/util/restart_widget.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class LoginGroup extends StatefulWidget {
  final VoidCallback onSignup;
  final VoidCallback onResetPassword;
  final bool isRecoverSession;

  const LoginGroup({
    super.key,
    required this.onSignup,
    required this.onResetPassword,
    required this.isRecoverSession,
  });

  @override
  State<LoginGroup> createState() => _LoginGroupState();
}

class _LoginGroupState extends State<LoginGroup> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoggingIn = false;
  bool _isDevHackDone = false;
  String? _errorMessage;
  bool _isRestartDataSet = false;

  @override
  void initState() {
    super.initState();
    if (!_maybeSetSessionEmail()) {
      _maybeApplyDevHack();
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _maybeSetRestartData();
    final secondaryOptionsLeadStyle = Theme.of(context)
        .textTheme
        .labelLarge!
        .copyWith(fontWeight: FontWeight.w300);
    final secondaryOptionsActionStyle = Theme.of(context)
        .textTheme
        .labelLarge!
        .copyWith(fontWeight: FontWeight.w500);

    return Form(
      key: _formKey,
      child: AutofillGroup(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            TextFormField(
              obscureText: false,
              autocorrect: false,
              keyboardType: TextInputType.emailAddress,
              textInputAction: TextInputAction.next,
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.email,
                fillColor: !widget.isRecoverSession
                    ? null
                    : Colors.grey.withValues(alpha: 0.3),
              ),
              controller: _emailController,
              validator: (v) => validateEmail(context, v),
              autofillHints: const [AutofillHints.email],
              onChanged: (_) => setState(() {
                _errorMessage = null;
              }),
            ),
            const SizedBox(height: 8.0),
            TextFormField(
              obscureText: true,
              autocorrect: false,
              keyboardType: TextInputType.text,
              decoration: InputDecoration(
                hintText: AppLocalizations.of(context)!.password,
              ),
              controller: _passwordController,
              validator: (v) => validatePassword(context, v),
              autofillHints: const [AutofillHints.password],
              onChanged: (_) => setState(() {
                _errorMessage = null;
              }),
              onFieldSubmitted: _isLoggingIn ? null : (_) => _login(context),
            ),
            if (_errorMessage != null) ...[
              const SizedBox(height: 8.0),
              Align(
                alignment: Alignment.centerLeft,
                child: Text(
                  _errorMessage!,
                  style: Theme.of(context)
                      .textTheme
                      .bodyMedium!
                      .copyWith(color: Theme.of(context).colorScheme.error),
                ),
              ),
            ],
            const SizedBox(height: 16.0),
            SizedBox(
              width: double.infinity,
              child: FilledButton(
                onPressed: _isLoggingIn ? null : () => _login(context),
                child: Text(AppLocalizations.of(context)!.login),
              ),
            ),
            TextButton(
              onPressed: _isLoggingIn ? null : widget.onResetPassword,
              child: Wrap(
                alignment: WrapAlignment.center,
                children: [
                  Text(
                    '${AppLocalizations.of(context)!.forgotPassword} ',
                    style: secondaryOptionsLeadStyle,
                  ),
                  Text(
                    AppLocalizations.of(context)!.reset,
                    style: secondaryOptionsActionStyle,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16.0),
            widget.isRecoverSession
                ? Column(
                    children: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child:
                            Text(AppLocalizations.of(context)!.leaveForLater),
                      ),
                      Text(
                        '(${AppLocalizations.of(context)!.youWontBeAbleToSyncData})',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  )
                : TextButton(
                    onPressed: _isLoggingIn ? null : widget.onSignup,
                    child: Wrap(
                      alignment: WrapAlignment.center,
                      children: [
                        Text(
                          '${AppLocalizations.of(context)!.dontHaveAccount} ',
                          style: secondaryOptionsLeadStyle,
                        ),
                        Text(
                          AppLocalizations.of(context)!.signup,
                          style: secondaryOptionsActionStyle,
                        ),
                      ],
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  void _login(BuildContext context) async {
    PerfTestMonitor().start(kPerfTestLogin);

    if (_formKey.currentState == null || !_formKey.currentState!.validate()) {
      return;
    }

    FocusScope.of(context).unfocus();

    setState(() {
      _isLoggingIn = true;
      _errorMessage = null;
    });

    final email = _emailController.text;
    final password = _passwordController.text;
    final locale = Localizations.localeOf(context).languageCode;

    final authAppService = context.read<AuthAppService>();
    final syncTrigger = context.read<SyncTrigger>();
    final navigatorState = Navigator.of(context);

    try {
      if (widget.isRecoverSession) {
        await authAppService.recoverSession(
            invalidSession: context.read<ActiveSession>().value!,
            email: email,
            password: password,
            locale: locale);
      } else {
        await authAppService.login(
          context: context,
          email: email,
          password: password,
          locale: locale,
        );
      }
    } catch (e) {
      setState(() {
        _isLoggingIn = false;
        _errorMessage = e is AuthError
            ? e.toString()
            : AppLocalizations.of(context)!.somethingWentWrong;
      });
      rethrow;
    }
    if (widget.isRecoverSession) {
      syncTrigger.trigger(const SyncTriggerEvent(
        SyncTriggerSource.session,
        SyncTriggerMode.fullSync,
      ));
      navigatorState.pop();
    }
  }

  void _maybeApplyDevHack() {
    assert(() {
      final appConfig = AppConfig();
      if (appConfig.isDevToolsEnabled && !_isDevHackDone) {
        _isDevHackDone = true;
        setState(() {
          final userPass = appConfig.devLoginHackUserPass.split('|');
          _emailController.text = userPass[0];
          _passwordController.text = userPass[1];
        });
      }
      return true;
    }());
  }

  bool _maybeSetSessionEmail() {
    final activeSession = context.read<ActiveSession>();
    if (widget.isRecoverSession) {
      _emailController.text = activeSession.value!.user.email!.value!;
      return true;
    }
    return false;
  }

  void _maybeSetRestartData() {
    if (_isRestartDataSet) {
      return;
    }
    _isRestartDataSet = true;
    final restartData =
        context.read<RestartData?>()?.props[kAuthRestartDataKey];
    if (restartData != null) {
      _errorMessage = AppLocalizations.of(context)!.somethingWentWrong;
      _emailController.text = restartData['email'];
      _passwordController.text = restartData['password'];
    }
  }
}
