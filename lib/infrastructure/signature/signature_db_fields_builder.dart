import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/domain/signature/signature_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/signature/signature_db_contract.dart';
import 'package:latlong2/latlong.dart';

class SignatureDbFieldsBuilder extends DbFieldsBuilder
    implements SignatureFieldsBuilder {
  SignatureDbFieldsBuilder() {
    _id();
  }

  SignatureDbContract get contract => const SignatureDbContract();

  SignatureDbFieldsBuilder _id() {
    addField(
      SignatureField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder remoteId() {
    addField(
      SignatureField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder doodle() {
    addField(
      SignatureField.doodle,
      DbField(
        column: contract.doodle,
        valueBuilder: (v) => SignatureDoodle(v),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder s3Key() {
    addField(
      SignatureField.s3Key,
      DbField(
        column: contract.s3Key,
        valueBuilder: (v) => SignatureS3Key(v),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder comments() {
    addField(
      SignatureField.comments,
      DbField(
        column: contract.comments,
        valueBuilder: (v) => SignatureComments(v),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder status() {
    addField(
      SignatureField.status,
      DbField(
        column: contract.status,
        valueBuilder: (v) => SignatureStatus.fromDb(v),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder ownerEmail() {
    addField(
      SignatureField.ownerEmail,
      DbField(
        column: contract.ownerEmail,
        valueBuilder: (v) => SignatureOwnerEmail(v),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder ownerName() {
    addField(
      SignatureField.ownerName,
      DbField(
        column: contract.ownerName,
        valueBuilder: (v) => SignatureOwnerName(v),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder location() {
    addField(
      SignatureField.location,
      DbField(
        columnAdder: (columns) =>
            columns.addAll([contract.latitude, contract.longitude]),
        multiValueBuilder: (values) {
          if (values[contract.latitude] == null ||
              values[contract.longitude] == null) {
            return const SignatureLocation(null);
          }

          return SignatureLocation(LatLng(
            (values[contract.latitude] as num).toDouble(),
            (values[contract.longitude] as num).toDouble(),
          ));
        },
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder entry(Fields fields) {
    addField(
      SignatureField.entry,
      DbField(
        key: SignatureField.entry,
        column: contract.entryId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.entry.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  SignatureFieldsBuilder user(Fields fields) {
    addField(
      SignatureField.user,
      DbField(
        key: SignatureField.user,
        column: contract.userId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.user.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
