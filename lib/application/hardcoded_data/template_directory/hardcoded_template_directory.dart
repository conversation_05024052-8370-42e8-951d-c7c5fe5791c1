import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/template_directory/template_directory.dart';

class HardcodedTemplateDirectory {
  static const List<RemoteId> enabledOrganizationIds = [
    /// TEST ORG
    RemoteId(164618),

    /// INGEMAQ FL S.A.S
    RemoteId(164901),
  ];

  static Map<RemoteId, List<TemplateDirectory>> directories = {
    enabledOrganizationIds[1]: [
      TemplateDirectory(
        id: const LocalId(1),
        name: TemplateDirectoryName('AGRICOLA'),
        path: TemplateDirectoryPath('AGRICOLA'),
      ),
      TemplateDirectory(
        id: const LocalId(2),
        name: TemplateDirectoryName('TRACTORES'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES'),
        parent: TemplateDirectory(id: const LocalId(1)),
      ),
      TemplateDirectory(
        id: const LocalId(3),
        name: TemplateDirectoryName('JOHN DEERE'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE'),
        parent: TemplateDirectory(id: const LocalId(2)),
      ),
      TemplateDirectory(
        id: const LocalId(4),
        name: TemplateDirectoryName('5075E'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/5075E'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(5),
        name: TemplateDirectoryName('5090E'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/5090E'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(6),
        name: TemplateDirectoryName('6155J'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/6155J'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(7),
        name: TemplateDirectoryName('6165J'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/6165J'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(8),
        name: TemplateDirectoryName('6170J'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/6170J'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(9),
        name: TemplateDirectoryName('6190J'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/6190J'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(10),
        name: TemplateDirectoryName('6603'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/6603'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(11),
        name: TemplateDirectoryName('7200R'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/7200R'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(12),
        name: TemplateDirectoryName('7215J'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/7215J'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(13),
        name: TemplateDirectoryName('8295R'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/8295R'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(14),
        name: TemplateDirectoryName('8320R'),
        path: TemplateDirectoryPath('AGRICOLA/TRACTORES/JOHN DEERE/8320R'),
        parent: TemplateDirectory(id: const LocalId(3)),
      ),
      TemplateDirectory(
        id: const LocalId(15),
        name: TemplateDirectoryName('CONSTRUCCION'),
        path: TemplateDirectoryPath('CONSTRUCCION'),
      ),
      TemplateDirectory(
        id: const LocalId(16),
        name: TemplateDirectoryName('HAMM'),
        path: TemplateDirectoryPath('CONSTRUCCION/HAMM'),
        parent: TemplateDirectory(id: const LocalId(15)),
      ),
      TemplateDirectory(
        id: const LocalId(17),
        name: TemplateDirectoryName('VIBROCOMPACTADORES'),
        path: TemplateDirectoryPath('CONSTRUCCION/HAMM/VIBROCOMPACTADORES'),
        parent: TemplateDirectory(id: const LocalId(16)),
      ),
      TemplateDirectory(
        id: const LocalId(18),
        name: TemplateDirectoryName('3410'),
        path:
            TemplateDirectoryPath('CONSTRUCCION/HAMM/VIBROCOMPACTADORES/3410'),
        parent: TemplateDirectory(id: const LocalId(17)),
      ),
      TemplateDirectory(
        id: const LocalId(19),
        name: TemplateDirectoryName('3411'),
        path:
            TemplateDirectoryPath('CONSTRUCCION/HAMM/VIBROCOMPACTADORES/3411'),
        parent: TemplateDirectory(id: const LocalId(17)),
      ),
      TemplateDirectory(
        id: const LocalId(20),
        name: TemplateDirectoryName('HC119'),
        path:
            TemplateDirectoryPath('CONSTRUCCION/HAMM/VIBROCOMPACTADORES/HC119'),
        parent: TemplateDirectory(id: const LocalId(17)),
      ),
      TemplateDirectory(
        id: const LocalId(21),
        name: TemplateDirectoryName('HITACHI'),
        path: TemplateDirectoryPath('CONSTRUCCION/HITACHI'),
        parent: TemplateDirectory(id: const LocalId(15)),
      ),
      TemplateDirectory(
        id: const LocalId(22),
        name: TemplateDirectoryName('EXCAVADORAS'),
        path: TemplateDirectoryPath('CONSTRUCCION/HITACHI/EXCAVADORAS'),
        parent: TemplateDirectory(id: const LocalId(21)),
      ),
      TemplateDirectory(
        id: const LocalId(23),
        name: TemplateDirectoryName('ZAXIS 210'),
        path:
            TemplateDirectoryPath('CONSTRUCCION/HITACHI/EXCAVADORAS/ZAXIS 210'),
        parent: TemplateDirectory(id: const LocalId(22)),
      ),
      TemplateDirectory(
        id: const LocalId(24),
        name: TemplateDirectoryName('ZAXIS 350'),
        path:
            TemplateDirectoryPath('CONSTRUCCION/HITACHI/EXCAVADORAS/ZAXIS 350'),
        parent: TemplateDirectory(id: const LocalId(22)),
      ),
      TemplateDirectory(
        id: const LocalId(25),
        name: TemplateDirectoryName('JOHN DEERE'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE'),
        parent: TemplateDirectory(id: const LocalId(15)),
      ),
      TemplateDirectory(
        id: const LocalId(26),
        name: TemplateDirectoryName('BULLDOZER'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE/BULLDOZER'),
        parent: TemplateDirectory(id: const LocalId(25)),
      ),
      TemplateDirectory(
        id: const LocalId(27),
        name: TemplateDirectoryName('750J'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE/BULLDOZER/750J'),
        parent: TemplateDirectory(id: const LocalId(26)),
      ),
      TemplateDirectory(
        id: const LocalId(28),
        name: TemplateDirectoryName('850J'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE/BULLDOZER/850J'),
        parent: TemplateDirectory(id: const LocalId(26)),
      ),
      TemplateDirectory(
        id: const LocalId(29),
        name: TemplateDirectoryName('EXCAVADORAS'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE/EXCAVADORAS'),
        parent: TemplateDirectory(id: const LocalId(25)),
      ),
      TemplateDirectory(
        id: const LocalId(30),
        name: TemplateDirectoryName('130G'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE/EXCAVADORAS/130G'),
        parent: TemplateDirectory(id: const LocalId(29)),
      ),
      TemplateDirectory(
        id: const LocalId(31),
        name: TemplateDirectoryName('210G'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE/EXCAVADORAS/210G'),
        parent: TemplateDirectory(id: const LocalId(29)),
      ),
      TemplateDirectory(
        id: const LocalId(32),
        name: TemplateDirectoryName('MOTONIVELADORA'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE/MOTONIVELADORA'),
        parent: TemplateDirectory(id: const LocalId(25)),
      ),
      TemplateDirectory(
        id: const LocalId(33),
        name: TemplateDirectoryName('620G'),
        path: TemplateDirectoryPath(
            'CONSTRUCCION/JOHN DEERE/MOTONIVELADORA/620G'),
        parent: TemplateDirectory(id: const LocalId(32)),
      ),
      TemplateDirectory(
        id: const LocalId(34),
        name: TemplateDirectoryName('RETROEXCAVADORAS'),
        path: TemplateDirectoryPath('CONSTRUCCION/JOHN DEERE/RETROEXCAVADORAS'),
        parent: TemplateDirectory(id: const LocalId(25)),
      ),
      TemplateDirectory(
        id: const LocalId(35),
        name: TemplateDirectoryName('310L'),
        path: TemplateDirectoryPath(
            'CONSTRUCCION/JOHN DEERE/RETROEXCAVADORAS/310L'),
        parent: TemplateDirectory(id: const LocalId(34)),
      ),
      TemplateDirectory(
        id: const LocalId(36),
        name: TemplateDirectoryName('MONTACARGAS'),
        path: TemplateDirectoryPath('MONTACARGAS'),
      ),
      TemplateDirectory(
        id: const LocalId(37),
        name: TemplateDirectoryName('UNICARRIERS'),
        path: TemplateDirectoryPath('MONTACARGAS/UNICARRIERS'),
        parent: TemplateDirectory(id: const LocalId(36)),
      ),
    ]
  };

  static final Map<RemoteId, LocalId> _templateDirectoriesRelations = {
    const RemoteId(86): const LocalId(4),
    const RemoteId(87): const LocalId(4),
    const RemoteId(88): const LocalId(4),
    const RemoteId(89): const LocalId(4),
    const RemoteId(90): const LocalId(4),
    const RemoteId(91): const LocalId(4),
    const RemoteId(92): const LocalId(4),
    const RemoteId(93): const LocalId(4),
    const RemoteId(94): const LocalId(4),
    const RemoteId(95): const LocalId(4),
    const RemoteId(96): const LocalId(4),
    const RemoteId(97): const LocalId(4),
    const RemoteId(98): const LocalId(4),
    const RemoteId(99): const LocalId(4),
    const RemoteId(100): const LocalId(4),
    const RemoteId(101): const LocalId(4),
    const RemoteId(102): const LocalId(4),
    const RemoteId(103): const LocalId(4),
    const RemoteId(104): const LocalId(4),
    const RemoteId(105): const LocalId(4),
    const RemoteId(106): const LocalId(4),
    const RemoteId(107): const LocalId(4),
    const RemoteId(108): const LocalId(4),
    const RemoteId(109): const LocalId(4),
    const RemoteId(110): const LocalId(4),
    const RemoteId(111): const LocalId(4),
    const RemoteId(112): const LocalId(5),
    const RemoteId(113): const LocalId(5),
    const RemoteId(114): const LocalId(5),
    const RemoteId(115): const LocalId(5),
    const RemoteId(116): const LocalId(5),
    const RemoteId(117): const LocalId(5),
    const RemoteId(118): const LocalId(5),
    const RemoteId(119): const LocalId(5),
    const RemoteId(120): const LocalId(5),
    const RemoteId(121): const LocalId(5),
    const RemoteId(122): const LocalId(5),
    const RemoteId(123): const LocalId(5),
    const RemoteId(124): const LocalId(5),
    const RemoteId(125): const LocalId(5),
    const RemoteId(126): const LocalId(5),
    const RemoteId(127): const LocalId(5),
    const RemoteId(128): const LocalId(5),
    const RemoteId(129): const LocalId(5),
    const RemoteId(130): const LocalId(5),
    const RemoteId(131): const LocalId(5),
    const RemoteId(132): const LocalId(5),
    const RemoteId(133): const LocalId(5),
    const RemoteId(134): const LocalId(5),
    const RemoteId(135): const LocalId(5),
    const RemoteId(136): const LocalId(5),
    const RemoteId(137): const LocalId(5),
    const RemoteId(138): const LocalId(6),
    const RemoteId(139): const LocalId(6),
    const RemoteId(140): const LocalId(6),
    const RemoteId(141): const LocalId(6),
    const RemoteId(142): const LocalId(6),
    const RemoteId(143): const LocalId(6),
    const RemoteId(144): const LocalId(6),
    const RemoteId(145): const LocalId(6),
    const RemoteId(146): const LocalId(6),
    const RemoteId(147): const LocalId(6),
    const RemoteId(148): const LocalId(6),
    const RemoteId(149): const LocalId(6),
    const RemoteId(150): const LocalId(6),
    const RemoteId(151): const LocalId(6),
    const RemoteId(152): const LocalId(6),
    const RemoteId(153): const LocalId(6),
    const RemoteId(154): const LocalId(6),
    const RemoteId(155): const LocalId(6),
    const RemoteId(156): const LocalId(6),
    const RemoteId(157): const LocalId(6),
    const RemoteId(158): const LocalId(6),
    const RemoteId(159): const LocalId(6),
    const RemoteId(160): const LocalId(6),
    const RemoteId(161): const LocalId(6),
    const RemoteId(162): const LocalId(6),
    const RemoteId(163): const LocalId(6),
    const RemoteId(164): const LocalId(7),
    const RemoteId(165): const LocalId(7),
    const RemoteId(166): const LocalId(7),
    const RemoteId(167): const LocalId(7),
    const RemoteId(168): const LocalId(7),
    const RemoteId(169): const LocalId(7),
    const RemoteId(170): const LocalId(7),
    const RemoteId(171): const LocalId(7),
    const RemoteId(172): const LocalId(7),
    const RemoteId(173): const LocalId(7),
    const RemoteId(174): const LocalId(7),
    const RemoteId(175): const LocalId(7),
    const RemoteId(176): const LocalId(7),
    const RemoteId(177): const LocalId(7),
    const RemoteId(178): const LocalId(7),
    const RemoteId(179): const LocalId(7),
    const RemoteId(180): const LocalId(7),
    const RemoteId(181): const LocalId(7),
    const RemoteId(182): const LocalId(7),
    const RemoteId(183): const LocalId(7),
    const RemoteId(184): const LocalId(7),
    const RemoteId(185): const LocalId(7),
    const RemoteId(186): const LocalId(7),
    const RemoteId(187): const LocalId(7),
    const RemoteId(188): const LocalId(7),
    const RemoteId(189): const LocalId(7),
    const RemoteId(190): const LocalId(8),
    const RemoteId(191): const LocalId(8),
    const RemoteId(192): const LocalId(8),
    const RemoteId(193): const LocalId(8),
    const RemoteId(194): const LocalId(8),
    const RemoteId(195): const LocalId(8),
    const RemoteId(196): const LocalId(8),
    const RemoteId(197): const LocalId(8),
    const RemoteId(198): const LocalId(8),
    const RemoteId(199): const LocalId(8),
    const RemoteId(200): const LocalId(8),
    const RemoteId(201): const LocalId(8),
    const RemoteId(202): const LocalId(8),
    const RemoteId(203): const LocalId(8),
    const RemoteId(204): const LocalId(8),
    const RemoteId(205): const LocalId(8),
    const RemoteId(206): const LocalId(8),
    const RemoteId(207): const LocalId(8),
    const RemoteId(208): const LocalId(8),
    const RemoteId(209): const LocalId(8),
    const RemoteId(210): const LocalId(8),
    const RemoteId(211): const LocalId(8),
    const RemoteId(212): const LocalId(8),
    const RemoteId(213): const LocalId(8),
    const RemoteId(214): const LocalId(8),
    const RemoteId(215): const LocalId(8),
    const RemoteId(216): const LocalId(9),
    const RemoteId(217): const LocalId(9),
    const RemoteId(218): const LocalId(9),
    const RemoteId(219): const LocalId(9),
    const RemoteId(220): const LocalId(9),
    const RemoteId(221): const LocalId(9),
    const RemoteId(222): const LocalId(9),
    const RemoteId(223): const LocalId(9),
    const RemoteId(224): const LocalId(9),
    const RemoteId(225): const LocalId(9),
    const RemoteId(226): const LocalId(9),
    const RemoteId(227): const LocalId(9),
    const RemoteId(228): const LocalId(9),
    const RemoteId(229): const LocalId(9),
    const RemoteId(230): const LocalId(9),
    const RemoteId(231): const LocalId(9),
    const RemoteId(232): const LocalId(9),
    const RemoteId(233): const LocalId(9),
    const RemoteId(234): const LocalId(9),
    const RemoteId(235): const LocalId(9),
    const RemoteId(236): const LocalId(9),
    const RemoteId(237): const LocalId(9),
    const RemoteId(238): const LocalId(9),
    const RemoteId(239): const LocalId(9),
    const RemoteId(240): const LocalId(9),
    const RemoteId(241): const LocalId(9),
    const RemoteId(242): const LocalId(10),
    const RemoteId(243): const LocalId(10),
    const RemoteId(244): const LocalId(10),
    const RemoteId(245): const LocalId(10),
    const RemoteId(246): const LocalId(10),
    const RemoteId(247): const LocalId(10),
    const RemoteId(248): const LocalId(10),
    const RemoteId(249): const LocalId(10),
    const RemoteId(250): const LocalId(10),
    const RemoteId(251): const LocalId(10),
    const RemoteId(252): const LocalId(10),
    const RemoteId(253): const LocalId(10),
    const RemoteId(254): const LocalId(10),
    const RemoteId(255): const LocalId(10),
    const RemoteId(256): const LocalId(10),
    const RemoteId(257): const LocalId(10),
    const RemoteId(258): const LocalId(10),
    const RemoteId(259): const LocalId(10),
    const RemoteId(260): const LocalId(10),
    const RemoteId(261): const LocalId(10),
    const RemoteId(262): const LocalId(10),
    const RemoteId(263): const LocalId(10),
    const RemoteId(264): const LocalId(10),
    const RemoteId(265): const LocalId(10),
    const RemoteId(266): const LocalId(10),
    const RemoteId(267): const LocalId(10),
    const RemoteId(268): const LocalId(11),
    const RemoteId(269): const LocalId(11),
    const RemoteId(270): const LocalId(11),
    const RemoteId(271): const LocalId(11),
    const RemoteId(272): const LocalId(11),
    const RemoteId(273): const LocalId(11),
    const RemoteId(274): const LocalId(11),
    const RemoteId(275): const LocalId(11),
    const RemoteId(276): const LocalId(11),
    const RemoteId(277): const LocalId(11),
    const RemoteId(278): const LocalId(11),
    const RemoteId(279): const LocalId(11),
    const RemoteId(280): const LocalId(11),
    const RemoteId(281): const LocalId(11),
    const RemoteId(282): const LocalId(11),
    const RemoteId(283): const LocalId(11),
    const RemoteId(284): const LocalId(11),
    const RemoteId(285): const LocalId(11),
    const RemoteId(286): const LocalId(11),
    const RemoteId(287): const LocalId(11),
    const RemoteId(288): const LocalId(11),
    const RemoteId(289): const LocalId(11),
    const RemoteId(290): const LocalId(11),
    const RemoteId(291): const LocalId(11),
    const RemoteId(292): const LocalId(11),
    const RemoteId(293): const LocalId(12),
    const RemoteId(294): const LocalId(12),
    const RemoteId(295): const LocalId(12),
    const RemoteId(296): const LocalId(12),
    const RemoteId(297): const LocalId(12),
    const RemoteId(298): const LocalId(12),
    const RemoteId(299): const LocalId(12),
    const RemoteId(300): const LocalId(12),
    const RemoteId(301): const LocalId(12),
    const RemoteId(302): const LocalId(12),
    const RemoteId(303): const LocalId(12),
    const RemoteId(304): const LocalId(12),
    const RemoteId(305): const LocalId(12),
    const RemoteId(306): const LocalId(12),
    const RemoteId(307): const LocalId(12),
    const RemoteId(308): const LocalId(12),
    const RemoteId(309): const LocalId(12),
    const RemoteId(310): const LocalId(12),
    const RemoteId(311): const LocalId(12),
    const RemoteId(312): const LocalId(12),
    const RemoteId(313): const LocalId(12),
    const RemoteId(314): const LocalId(12),
    const RemoteId(315): const LocalId(12),
    const RemoteId(316): const LocalId(12),
    const RemoteId(317): const LocalId(12),
    const RemoteId(318): const LocalId(12),
    const RemoteId(319): const LocalId(13),
    const RemoteId(320): const LocalId(13),
    const RemoteId(321): const LocalId(13),
    const RemoteId(322): const LocalId(13),
    const RemoteId(323): const LocalId(13),
    const RemoteId(324): const LocalId(13),
    const RemoteId(325): const LocalId(13),
    const RemoteId(326): const LocalId(13),
    const RemoteId(327): const LocalId(13),
    const RemoteId(328): const LocalId(13),
    const RemoteId(329): const LocalId(13),
    const RemoteId(330): const LocalId(13),
    const RemoteId(331): const LocalId(13),
    const RemoteId(332): const LocalId(13),
    const RemoteId(333): const LocalId(13),
    const RemoteId(334): const LocalId(13),
    const RemoteId(335): const LocalId(13),
    const RemoteId(336): const LocalId(13),
    const RemoteId(337): const LocalId(13),
    const RemoteId(338): const LocalId(13),
    const RemoteId(339): const LocalId(13),
    const RemoteId(340): const LocalId(13),
    const RemoteId(341): const LocalId(13),
    const RemoteId(342): const LocalId(13),
    const RemoteId(343): const LocalId(13),
    const RemoteId(344): const LocalId(13),
    const RemoteId(345): const LocalId(14),
    const RemoteId(346): const LocalId(14),
    const RemoteId(347): const LocalId(14),
    const RemoteId(348): const LocalId(14),
    const RemoteId(349): const LocalId(14),
    const RemoteId(350): const LocalId(14),
    const RemoteId(351): const LocalId(14),
    const RemoteId(352): const LocalId(14),
    const RemoteId(353): const LocalId(14),
    const RemoteId(354): const LocalId(14),
    const RemoteId(355): const LocalId(14),
    const RemoteId(356): const LocalId(14),
    const RemoteId(357): const LocalId(14),
    const RemoteId(358): const LocalId(14),
    const RemoteId(359): const LocalId(14),
    const RemoteId(360): const LocalId(14),
    const RemoteId(361): const LocalId(14),
    const RemoteId(362): const LocalId(14),
    const RemoteId(363): const LocalId(14),
    const RemoteId(364): const LocalId(14),
    const RemoteId(365): const LocalId(14),
    const RemoteId(366): const LocalId(14),
    const RemoteId(367): const LocalId(14),
    const RemoteId(368): const LocalId(14),
    const RemoteId(369): const LocalId(14),
    const RemoteId(370): const LocalId(14),
    const RemoteId(371): const LocalId(27),
    const RemoteId(372): const LocalId(27),
    const RemoteId(373): const LocalId(27),
    const RemoteId(374): const LocalId(27),
    const RemoteId(375): const LocalId(27),
    const RemoteId(376): const LocalId(27),
    const RemoteId(377): const LocalId(27),
    const RemoteId(378): const LocalId(27),
    const RemoteId(379): const LocalId(27),
    const RemoteId(380): const LocalId(27),
    const RemoteId(381): const LocalId(27),
    const RemoteId(382): const LocalId(27),
    const RemoteId(383): const LocalId(27),
    const RemoteId(384): const LocalId(27),
    const RemoteId(385): const LocalId(27),
    const RemoteId(386): const LocalId(27),
    const RemoteId(387): const LocalId(27),
    const RemoteId(388): const LocalId(27),
    const RemoteId(389): const LocalId(27),
    const RemoteId(390): const LocalId(27),
    const RemoteId(391): const LocalId(27),
    const RemoteId(392): const LocalId(27),
    const RemoteId(393): const LocalId(27),
    const RemoteId(394): const LocalId(27),
    const RemoteId(395): const LocalId(27),
    const RemoteId(396): const LocalId(28),
    const RemoteId(397): const LocalId(28),
    const RemoteId(398): const LocalId(28),
    const RemoteId(399): const LocalId(28),
    const RemoteId(400): const LocalId(28),
    const RemoteId(401): const LocalId(28),
    const RemoteId(402): const LocalId(28),
    const RemoteId(403): const LocalId(28),
    const RemoteId(404): const LocalId(28),
    const RemoteId(405): const LocalId(28),
    const RemoteId(406): const LocalId(28),
    const RemoteId(407): const LocalId(28),
    const RemoteId(408): const LocalId(28),
    const RemoteId(409): const LocalId(28),
    const RemoteId(410): const LocalId(28),
    const RemoteId(411): const LocalId(28),
    const RemoteId(412): const LocalId(28),
    const RemoteId(413): const LocalId(28),
    const RemoteId(414): const LocalId(28),
    const RemoteId(415): const LocalId(28),
    const RemoteId(416): const LocalId(28),
    const RemoteId(417): const LocalId(28),
    const RemoteId(418): const LocalId(28),
    const RemoteId(419): const LocalId(28),
    const RemoteId(420): const LocalId(28),
    const RemoteId(421): const LocalId(23),
    const RemoteId(422): const LocalId(23),
    const RemoteId(423): const LocalId(23),
    const RemoteId(424): const LocalId(23),
    const RemoteId(425): const LocalId(23),
    const RemoteId(426): const LocalId(23),
    const RemoteId(427): const LocalId(23),
    const RemoteId(428): const LocalId(23),
    const RemoteId(429): const LocalId(23),
    const RemoteId(430): const LocalId(23),
    const RemoteId(431): const LocalId(23),
    const RemoteId(432): const LocalId(23),
    const RemoteId(433): const LocalId(23),
    const RemoteId(434): const LocalId(23),
    const RemoteId(435): const LocalId(23),
    const RemoteId(436): const LocalId(23),
    const RemoteId(437): const LocalId(23),
    const RemoteId(438): const LocalId(23),
    const RemoteId(439): const LocalId(23),
    const RemoteId(440): const LocalId(23),
    const RemoteId(441): const LocalId(23),
    const RemoteId(442): const LocalId(23),
    const RemoteId(443): const LocalId(23),
    const RemoteId(444): const LocalId(23),
    const RemoteId(445): const LocalId(23),
    const RemoteId(446): const LocalId(24),
    const RemoteId(447): const LocalId(24),
    const RemoteId(448): const LocalId(24),
    const RemoteId(449): const LocalId(24),
    const RemoteId(450): const LocalId(24),
    const RemoteId(451): const LocalId(24),
    const RemoteId(452): const LocalId(24),
    const RemoteId(453): const LocalId(24),
    const RemoteId(454): const LocalId(24),
    const RemoteId(455): const LocalId(24),
    const RemoteId(456): const LocalId(24),
    const RemoteId(457): const LocalId(24),
    const RemoteId(458): const LocalId(24),
    const RemoteId(459): const LocalId(24),
    const RemoteId(460): const LocalId(24),
    const RemoteId(461): const LocalId(24),
    const RemoteId(462): const LocalId(24),
    const RemoteId(463): const LocalId(24),
    const RemoteId(464): const LocalId(24),
    const RemoteId(465): const LocalId(24),
    const RemoteId(466): const LocalId(24),
    const RemoteId(467): const LocalId(24),
    const RemoteId(468): const LocalId(24),
    const RemoteId(469): const LocalId(24),
    const RemoteId(470): const LocalId(24),
    const RemoteId(471): const LocalId(30),
    const RemoteId(472): const LocalId(30),
    const RemoteId(473): const LocalId(30),
    const RemoteId(474): const LocalId(30),
    const RemoteId(475): const LocalId(30),
    const RemoteId(476): const LocalId(30),
    const RemoteId(477): const LocalId(30),
    const RemoteId(478): const LocalId(30),
    const RemoteId(479): const LocalId(30),
    const RemoteId(480): const LocalId(30),
    const RemoteId(481): const LocalId(30),
    const RemoteId(482): const LocalId(30),
    const RemoteId(483): const LocalId(30),
    const RemoteId(484): const LocalId(30),
    const RemoteId(485): const LocalId(30),
    const RemoteId(486): const LocalId(30),
    const RemoteId(487): const LocalId(30),
    const RemoteId(488): const LocalId(30),
    const RemoteId(489): const LocalId(30),
    const RemoteId(490): const LocalId(30),
    const RemoteId(491): const LocalId(30),
    const RemoteId(492): const LocalId(30),
    const RemoteId(493): const LocalId(30),
    const RemoteId(494): const LocalId(30),
    const RemoteId(495): const LocalId(30),
    const RemoteId(496): const LocalId(31),
    const RemoteId(497): const LocalId(31),
    const RemoteId(498): const LocalId(31),
    const RemoteId(499): const LocalId(31),
    const RemoteId(500): const LocalId(31),
    const RemoteId(501): const LocalId(31),
    const RemoteId(502): const LocalId(31),
    const RemoteId(503): const LocalId(31),
    const RemoteId(504): const LocalId(31),
    const RemoteId(505): const LocalId(31),
    const RemoteId(506): const LocalId(31),
    const RemoteId(507): const LocalId(31),
    const RemoteId(508): const LocalId(31),
    const RemoteId(509): const LocalId(31),
    const RemoteId(510): const LocalId(31),
    const RemoteId(511): const LocalId(31),
    const RemoteId(512): const LocalId(31),
    const RemoteId(513): const LocalId(31),
    const RemoteId(514): const LocalId(31),
    const RemoteId(515): const LocalId(31),
    const RemoteId(516): const LocalId(31),
    const RemoteId(517): const LocalId(31),
    const RemoteId(518): const LocalId(31),
    const RemoteId(519): const LocalId(31),
    const RemoteId(520): const LocalId(31),
    const RemoteId(521): const LocalId(37),
    const RemoteId(522): const LocalId(37),
    const RemoteId(523): const LocalId(37),
    const RemoteId(524): const LocalId(37),
    const RemoteId(525): const LocalId(37),
    const RemoteId(526): const LocalId(37),
    const RemoteId(527): const LocalId(37),
    const RemoteId(528): const LocalId(37),
    const RemoteId(529): const LocalId(37),
    const RemoteId(530): const LocalId(37),
    const RemoteId(531): const LocalId(37),
    const RemoteId(532): const LocalId(37),
    const RemoteId(533): const LocalId(33),
    const RemoteId(534): const LocalId(33),
    const RemoteId(535): const LocalId(33),
    const RemoteId(536): const LocalId(33),
    const RemoteId(537): const LocalId(33),
    const RemoteId(538): const LocalId(33),
    const RemoteId(539): const LocalId(33),
    const RemoteId(540): const LocalId(33),
    const RemoteId(541): const LocalId(33),
    const RemoteId(542): const LocalId(33),
    const RemoteId(543): const LocalId(33),
    const RemoteId(544): const LocalId(33),
    const RemoteId(545): const LocalId(33),
    const RemoteId(546): const LocalId(33),
    const RemoteId(547): const LocalId(33),
    const RemoteId(548): const LocalId(33),
    const RemoteId(549): const LocalId(33),
    const RemoteId(550): const LocalId(33),
    const RemoteId(551): const LocalId(33),
    const RemoteId(552): const LocalId(33),
    const RemoteId(553): const LocalId(33),
    const RemoteId(554): const LocalId(33),
    const RemoteId(555): const LocalId(33),
    const RemoteId(556): const LocalId(33),
    const RemoteId(557): const LocalId(33),
    const RemoteId(558): const LocalId(35),
    const RemoteId(559): const LocalId(35),
    const RemoteId(560): const LocalId(35),
    const RemoteId(561): const LocalId(35),
    const RemoteId(562): const LocalId(35),
    const RemoteId(563): const LocalId(35),
    const RemoteId(564): const LocalId(35),
    const RemoteId(565): const LocalId(35),
    const RemoteId(566): const LocalId(35),
    const RemoteId(567): const LocalId(35),
    const RemoteId(568): const LocalId(35),
    const RemoteId(569): const LocalId(35),
    const RemoteId(570): const LocalId(35),
    const RemoteId(571): const LocalId(35),
    const RemoteId(572): const LocalId(35),
    const RemoteId(573): const LocalId(35),
    const RemoteId(574): const LocalId(35),
    const RemoteId(575): const LocalId(35),
    const RemoteId(576): const LocalId(35),
    const RemoteId(577): const LocalId(35),
    const RemoteId(578): const LocalId(35),
    const RemoteId(579): const LocalId(35),
    const RemoteId(580): const LocalId(35),
    const RemoteId(581): const LocalId(35),
    const RemoteId(582): const LocalId(35),
    const RemoteId(583): const LocalId(18),
    const RemoteId(584): const LocalId(18),
    const RemoteId(585): const LocalId(18),
    const RemoteId(586): const LocalId(18),
    const RemoteId(587): const LocalId(18),
    const RemoteId(588): const LocalId(18),
    const RemoteId(589): const LocalId(18),
    const RemoteId(590): const LocalId(18),
    const RemoteId(591): const LocalId(18),
    const RemoteId(592): const LocalId(18),
    const RemoteId(593): const LocalId(18),
    const RemoteId(594): const LocalId(18),
    const RemoteId(595): const LocalId(18),
    const RemoteId(596): const LocalId(18),
    const RemoteId(597): const LocalId(18),
    const RemoteId(598): const LocalId(18),
    const RemoteId(599): const LocalId(18),
    const RemoteId(600): const LocalId(18),
    const RemoteId(601): const LocalId(18),
    const RemoteId(602): const LocalId(18),
    const RemoteId(603): const LocalId(18),
    const RemoteId(604): const LocalId(18),
    const RemoteId(605): const LocalId(18),
    const RemoteId(606): const LocalId(18),
    const RemoteId(607): const LocalId(18),
    const RemoteId(608): const LocalId(19),
    const RemoteId(609): const LocalId(19),
    const RemoteId(610): const LocalId(19),
    const RemoteId(611): const LocalId(19),
    const RemoteId(612): const LocalId(19),
    const RemoteId(613): const LocalId(19),
    const RemoteId(614): const LocalId(19),
    const RemoteId(615): const LocalId(19),
    const RemoteId(616): const LocalId(19),
    const RemoteId(617): const LocalId(19),
    const RemoteId(618): const LocalId(19),
    const RemoteId(619): const LocalId(19),
    const RemoteId(620): const LocalId(19),
    const RemoteId(621): const LocalId(19),
    const RemoteId(622): const LocalId(19),
    const RemoteId(623): const LocalId(19),
    const RemoteId(624): const LocalId(19),
    const RemoteId(625): const LocalId(19),
    const RemoteId(626): const LocalId(19),
    const RemoteId(627): const LocalId(19),
    const RemoteId(628): const LocalId(19),
    const RemoteId(629): const LocalId(19),
    const RemoteId(630): const LocalId(19),
    const RemoteId(631): const LocalId(19),
    const RemoteId(632): const LocalId(19),
    const RemoteId(633): const LocalId(20),
    const RemoteId(634): const LocalId(20),
    const RemoteId(635): const LocalId(20),
    const RemoteId(636): const LocalId(20),
    const RemoteId(637): const LocalId(20),
    const RemoteId(638): const LocalId(20),
    const RemoteId(639): const LocalId(20),
    const RemoteId(640): const LocalId(20),
    const RemoteId(641): const LocalId(20),
    const RemoteId(642): const LocalId(20),
    const RemoteId(643): const LocalId(20),
    const RemoteId(644): const LocalId(20),
    const RemoteId(645): const LocalId(20),
    const RemoteId(646): const LocalId(20),
    const RemoteId(647): const LocalId(20),
    const RemoteId(648): const LocalId(20),
    const RemoteId(649): const LocalId(20),
    const RemoteId(650): const LocalId(20),
    const RemoteId(651): const LocalId(20),
    const RemoteId(652): const LocalId(20),
    const RemoteId(653): const LocalId(20),
    const RemoteId(654): const LocalId(20),
    const RemoteId(655): const LocalId(20),
    const RemoteId(656): const LocalId(20),
    const RemoteId(657): const LocalId(20),
  };

  static List<Template> maybeRelateWithDirectories(
    ActiveOrganization activeOrganization,
    List<Template> templates,
  ) {
    if (!enabledOrganizationIds.contains(activeOrganization.value!.remoteId)) {
      return templates;
    }

    final t = templates.map((e) {
      final directoryId = _templateDirectoriesRelations[e.remoteId!];
      return e.copyWith(
          directory:
              directoryId == null ? null : TemplateDirectory(id: directoryId));
    }).toList();

    return t;
  }
}
