import 'package:flutter/widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import 'package:provider/single_child_widget.dart';

import 'test_app.dart';

abstract class BaseRobot {
  final WidgetTester tester;
  BuildContext? _context;

  BaseRobot(this.tester);

  BuildContext get context => _context!;

  Future<void> pumpWidget();

  @protected
  Future<void> pumpTestApp({
    Widget Function(Widget child)? parent,
    List<SingleChildWidget> providers = const <SingleChildWidget>[],
    Widget? child,
    Widget? appBar,
  }) async {
    await tester.pumpWidget(
      _adoptApp(
        parent,
        _buildTestApp(child ?? Container(), appBar, providers),
      ),
    );
    await tester.pumpAndSettle();
  }

  Widget _adoptApp(
    Widget Function(Widget child)? parent,
    Widget app,
  ) {
    return parent != null ? parent(app) : app;
  }

  Widget _buildTestApp(
    Widget child,
    Widget? appBar,
    List<SingleChildWidget> providers,
  ) {
    final wrappedForContext = Builder(builder: (c) {
      _context = c;
      return child;
    });
    final app = TestApp(appBar: appBar, child: wrappedForContext);
    return providers.isEmpty
        ? app
        : MultiProvider(providers: providers, child: app);
  }

  Future<void> enterText(Finder finder, {required String text}) {
    return tester.enterText(finder, text);
  }

  Future<void> tap(Finder finder, {bool warnIfMissed = false}) {
    return tester.tap(finder, warnIfMissed: warnIfMissed);
  }

  Future<void> longPress(Finder finder) {
    return tester.longPress(finder);
  }
}
