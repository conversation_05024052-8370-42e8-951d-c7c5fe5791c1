import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/url_launcher.dart';
import 'package:bitacora/util/web_external_launcher.dart';
import 'package:mocktail/mocktail.dart';
import 'package:test/test.dart';
import 'package:url_launcher/url_launcher.dart';

import '../mocktail_fallback_values.dart';
import 'mocks.dart';

void main() {
  group('$WebExternalLauncher tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    test('Injects same', () {
      expect(WebExternalLauncher(), WebExternalLauncher());
    });

    test('Launch Youtube url', () async {
      final mockUrlLauncher = MockUrlLauncher();
      when(() => mockUrlLauncher.launch(any(), mode: any(named: 'mode')))
          .thenAnswer((invocation) => Future.value());
      final webExternalLauncher = WebExternalLauncher();

      await withInjected<UrlLauncher>(mockUrlLauncher,
          () => webExternalLauncher.launchYoutubeChannel('yOuTuBeId'));

      expect(
        verify(() => mockUrlLauncher.launch(captureAny(),
            mode: captureAny(named: 'mode'))).captured,
        [
          'https://youtube.com/channel/yOuTuBeId',
          LaunchMode.externalApplication
        ],
      );
    });

    test('Launch WhatsApp url', () async {
      final mockUrlLauncher = MockUrlLauncher();
      when(() => mockUrlLauncher.launch(any(), mode: any(named: 'mode')))
          .thenAnswer((invocation) => Future.value());
      final webExternalLauncher = WebExternalLauncher();

      await withInjected<UrlLauncher>(mockUrlLauncher,
          () => webExternalLauncher.launchWhatsapp('+51123456789', 'hello'));

      expect(
        verify(() => mockUrlLauncher.launch(captureAny(),
            mode: captureAny(named: 'mode'))).captured,
        [
          'https://wa.me/+51123456789?text=hello',
          LaunchMode.externalApplication
        ],
      );
    });

    test('Launch Email url', () async {
      final mockUrlLauncher = MockUrlLauncher();
      when(() => mockUrlLauncher.launch(any(), mode: any(named: 'mode')))
          .thenAnswer((invocation) => Future.value());
      final webExternalLauncher = WebExternalLauncher();

      await withInjected<UrlLauncher>(mockUrlLauncher,
          () => webExternalLauncher.launchEmail('<EMAIL>', 'hello'));

      expect(
        verify(() => mockUrlLauncher.launch(captureAny(),
            mode: captureAny(named: 'mode'))).captured,
        ['mailto:<EMAIL>?body=hello', LaunchMode.externalApplication],
      );
    });
  });
}
