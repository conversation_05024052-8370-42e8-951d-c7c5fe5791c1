import 'package:bitacora/l10n/app_localizations.dart';
import 'package:bitacora/presentation/my_activity/monthly_entry_count/monthly_entry_count.dart';
import 'package:bitacora/presentation/my_activity/weekly_entry_count/weekly_entry_count.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';

class MyActivityPage extends StatelessWidget {
  const MyActivityPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(AppLocalizations.of(context)!.myActiviy),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: kPageInsets,
          child: Column(
            children: [
              Container(
                padding: EdgeInsets.all(32.0),
                decoration: BoxDecoration(
                  color: Theme.of(context)
                      .colorScheme
                      .primary
                      .withValues(alpha: 0.15),
                  borderRadius: BorderRadius.circular(16.0),
                ),
                constraints: BoxConstraints(maxHeight: 300.0),
                child: WeeklyEntryCount(),
              ),
              SizedBox(height: 16.0),
              Container(
                padding: EdgeInsets.all(32.0).copyWith(bottom: 16),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primary,
                  borderRadius: BorderRadius.circular(16.0),
                ),
                child: MonthlyEntryCount(),
              )
            ],
          ),
        ),
      ),
    );
  }
}
