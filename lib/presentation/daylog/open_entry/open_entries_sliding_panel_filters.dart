import 'package:bitacora/shared_preferences_keys.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:shared_preferences/shared_preferences.dart';

enum OpenEntriesSlidingPanelFilter {
  all(1),
  assignedMe(2);

  final int key;

  const OpenEntriesSlidingPanelFilter(this.key);

  String getDisplayValue(BuildContext context) {
    switch (this) {
      case OpenEntriesSlidingPanelFilter.all:
        return AppLocalizations.of(context)!.all;
      case OpenEntriesSlidingPanelFilter.assignedMe:
        return AppLocalizations.of(context)!.assignedToMe;
    }
  }

  static OpenEntriesSlidingPanelFilter fromKey(int key) {
    switch (key) {
      case 1:
        return OpenEntriesSlidingPanelFilter.all;
      case 2:
        return OpenEntriesSlidingPanelFilter.assignedMe;
      default:
        throw 'OpenEntriesSlidingPanelFilter: key <$key> not supported';
    }
  }
}

class OpenEntriesSlidingPanelFilters extends StatefulWidget {
  final double bottomSheetPosition;
  final Function(OpenEntriesSlidingPanelFilter) onChange;

  const OpenEntriesSlidingPanelFilters(
      {super.key, required this.bottomSheetPosition, required this.onChange});

  @override
  State<OpenEntriesSlidingPanelFilters> createState() =>
      _OpenEntriesSlidingPanelFiltersState();
}

class _OpenEntriesSlidingPanelFiltersState
    extends State<OpenEntriesSlidingPanelFilters> {
  OpenEntriesSlidingPanelFilter _selected =
      OpenEntriesSlidingPanelFilter.values.first;

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  void _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();

    final selectedFromPrefs = OpenEntriesSlidingPanelFilter.fromKey(
      prefs.getInt(SharedPreferencesKeys.openEntriesSlidingPanelFilter) ??
          OpenEntriesSlidingPanelFilter.all.key,
    );

    if (_selected == selectedFromPrefs) {
      return;
    }

    _selected = selectedFromPrefs;
    widget.onChange(_selected);
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedSize(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      child: SizedBox(
        height: widget.bottomSheetPosition == 0 ? 0 : null,
        child: Opacity(
          opacity: widget.bottomSheetPosition,
          child: Padding(
            padding: const EdgeInsets.only(top: 8.0, left: 8.0),
            child: Wrap(
              spacing: 4.0,
              children: OpenEntriesSlidingPanelFilter.values
                  .map(
                    (e) => ChoiceChip(
                      label: Text(
                        e.getDisplayValue(context),
                        style: Theme.of(context).textTheme.bodySmall!.copyWith(
                              color: Theme.of(context).colorScheme.onSurface,
                            ),
                      ),
                      selected: _selected == e,
                      onSelected: (isSelected) {
                        if (isSelected) {
                          _onSelected(e);
                        }
                      },
                      backgroundColor: Colors.transparent,
                      shape: _selected == e
                          ? null
                          : StadiumBorder(
                              side: BorderSide(
                                color: Theme.of(context)
                                    .colorScheme
                                    .onSurface
                                    .withValues(alpha: 0.2),
                                width: 0.5,
                              ),
                            ),
                    ),
                  )
                  .toList(),
            ),
          ),
        ),
      ),
    );
  }

  void _onSelected(OpenEntriesSlidingPanelFilter filter) async {
    setState(() {
      _selected = filter;
    });
    widget.onChange(filter);

    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
        SharedPreferencesKeys.openEntriesSlidingPanelFilter, filter.key);
  }
}
