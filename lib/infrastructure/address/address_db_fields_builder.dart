import 'package:bitacora/domain/address/address.dart';
import 'package:bitacora/domain/address/address_post_fields_builder.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/infrastructure/address/address_db_contract.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';

class AddressDbFieldsBuilder extends DbFieldsBuilder
    implements AddressFieldsBuilder {
  AddressDbFieldsBuilder() {
    _id();
  }

  AddressDbContract get contract => const AddressDbContract();

  AddressDbFieldsBuilder _id() {
    addField(
      AddressField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  AddressDbFieldsBuilder remoteId() {
    addField(
      AddressField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  AddressDbFieldsBuilder value() {
    addField(
      AddressField.value,
      DbField(
        column: contract.value,
        valueBuilder: (v) => AddressValue(v),
      ),
    );
    return this;
  }

  @override
  AddressDbFieldsBuilder type() {
    addField(
      AddressField.type,
      DbField(
        column: contract.type,
        valueBuilder: (v) => PersonDetailContactType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  AddressDbFieldsBuilder updatedAt() {
    addField(
      AddressField.updatedAt,
      DbField(
        column: contract.updatedAt,
        valueBuilder: (v) =>
            AddressUpdatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  AddressDbFieldsBuilder createdAt() {
    addField(
      AddressField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) =>
            AddressCreatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  AddressFieldsBuilder personDetail(Fields fields) {
    addField(
      AddressField.personDetail,
      DbField(
        key: AddressField.personDetail,
        column: contract.personDetailId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.personDetail.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
