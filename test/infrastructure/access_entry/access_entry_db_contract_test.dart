import 'package:bitacora/infrastructure/access_entry/access_entry_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$AccessEntryDbContract tests', () {
    test('Create $AccessEntryDbContract', () {
      expectRemovingSpaces(
        const AccessEntryDbContract().create,
        '''
        CREATE TABLE access_entry (
          ae_entryId INTEGER PRIMARY KEY,
          ae_permission INTEGER NOT NULL
        )
        ''',
      );
    });
  });
}
