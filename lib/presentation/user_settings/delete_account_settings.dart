import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/nuke.dart';
import 'package:bitacora/util/dialog.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/toast/toast.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class DeleteAccountButton extends StatelessWidget {
  const DeleteAccountButton({super.key});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      textColor: Theme.of(context).colorScheme.error,
      title: Text(AppLocalizations.of(context)!.deleteAccountTitle),
      onTap: () => _onDeleteAccountPressed(context),
    );
  }

  void _onDeleteAccountPressed(BuildContext context) async {
    showDestructiveDialog(
      context: context,
      title: AppLocalizations.of(context)!.deleteAccountTitle,
      message: AppLocalizations.of(context)!.deleteAccountMessage,
      destroyText: AppLocalizations.of(context)!.delete,
      destroyer: () => _deleteAccount(context),
    );
  }

  void _deleteAccount(BuildContext context) async {
    final session = context.read<ActiveSession>();
    final apiHelper = context.read<ApiHelper>();
    final userId = session.value!.user.remoteId!.apiValue;
    logger.i('account:delete Deleting account $userId');

    try {
      final contextSnapshot = NukeContextSnapshot(context);
      await apiHelper.delete('users/delete_request');
      await Nuke().nuke(contextSnapshot);
    } catch (_) {
      Toast().showNetworkErrorToast();
      rethrow;
    }
  }
}
