import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group/value/entry_group_created_at.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/util/date_utils.dart';

class EntryGroupApiTranslator implements ModelTranslator<EntryGroup> {
  final ApiTranslator apiTranslator;

  const EntryGroupApiTranslator(this.apiTranslator);

  @override
  EntryGroup fromMap(Map<String, dynamic> data) {
    return EntryGroup(
      remoteId: RemoteId(data['id']),
      name: EntryGroupName(data['name']),
      organization: Organization(remoteId: RemoteId(data['organization_id'])),
      entries: (data['entries'] as List<dynamic>?)?.map<EntryGroupEntry>((e) {
        return apiTranslator.entryGroupEntry.fromMap(e);
      }).toList(growable: false),
      createdAt: EntryGroupCreatedAt(getDateTimeFromApi(data['created_at'])),
    );
  }

  @override
  Map<String, dynamic> toMap(EntryGroup model) {
    final map = <String, dynamic>{
      'name': model.name?.apiValue,
      'entries': model.entries
          ?.map((e) => apiTranslator.entryGroupEntry.toMap(e))
          .toList(growable: false),
    };

    if (model.remoteId?.value != null) {
      map['id'] = model.remoteId!.apiValue;
    } else {
      map['created_at'] = model.createdAt!.apiValue;
    }

    return map;
  }
}
