import 'package:flutter/material.dart';

class MapPathMarker extends StatelessWidget {
  final String label;
  final double? radius;
  final Color? color;

  const MapPathMarker({
    super.key,
    required this.label,
    this.radius,
    this.color,
  });

  @override
  Widget build(BuildContext context) {
    return CircleAvatar(
      backgroundColor: color ?? Theme.of(context).colorScheme.primary,
      radius: radius ?? 2,
      child: Text(label, style: const TextStyle(color: Colors.black)),
    );
  }
}
