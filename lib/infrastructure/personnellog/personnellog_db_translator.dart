import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/personnellog/personnellog.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/personnellog/personnellog_db_contract.dart';

class PersonnellogDbTranslator implements DbTranslator<Personnellog> {
  const PersonnellogDbTranslator();

  @override
  Set<Field> get nestedModelFields => personnellogNestedModelFields;

  @override
  Future<Personnellog> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Personnellog(
      id: fields[PersonnellogField.id]?.value(map),
      remoteId: fields[PersonnellogField.remoteId]?.value(map),
      name: fields[PersonnellogField.name]?.value(map),
      sublocation: fields[PersonnellogField.sublocation]?.value(map),
      entrance: fields[PersonnellogField.entrance]?.value(map),
      exit: fields[PersonnellogField.exit]?.value(map),
      minutes: fields[PersonnellogField.minutes]?.value(map),
      project: await fields[PersonnellogField.project]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, Personnellog model) async {
    final map = <String, dynamic>{};
    const contract = PersonnellogDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.name, model.name);
    addField(map, contract.sublocation, model.sublocation);
    addField(map, contract.entrance, model.entrance);
    addField(map, contract.exit, model.exit);
    addField(map, contract.minutes, model.minutes);

    await saveNestedModel<Project>(
        context, map, contract.projectId, context.db.project, model.project);
    return map;
  }
}
