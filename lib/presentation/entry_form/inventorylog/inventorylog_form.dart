import 'package:bitacora/domain/common/payment_status.dart';
import 'package:bitacora/domain/common/query/search_providers_repository_query.dart';
import 'package:bitacora/domain/common/value_object/centesimal_value_object.dart';
import 'package:bitacora/domain/common/value_object/currency_value_object.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_item_name_suggestion_repository_query.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_reason_suggestion_repository_query.dart';
import 'package:bitacora/presentation/entry_form/util/centesimal_text_input_formatter.dart';
import 'package:bitacora/presentation/entry_form/util/currency_text_input_formatter.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/newable_field_suggestion_typeahead.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class InventorylogForm extends StatefulWidget {
  final InventorylogFormController controller;

  const InventorylogForm({
    super.key,
    required this.controller,
  });

  @override
  State<InventorylogForm> createState() => _InventorylogFormState();
}

class _InventorylogFormState extends State<InventorylogForm> {
  final GlobalKey _itemNameKey = GlobalKey();
  final GlobalKey _providerKey = GlobalKey();
  final GlobalKey _reasonKey = GlobalKey();

  final FocusNode _quantityFocus = FocusNode();
  final FocusNode _itemNameFocus = FocusNode();
  final FocusNode _destProjectNameFocus = FocusNode();
  final FocusNode _destSublocationFocus = FocusNode();
  final FocusNode _sourceProjectNameFocus = FocusNode();
  final FocusNode _sourceSublocationFocus = FocusNode();
  final FocusNode _costPriceFocus = FocusNode();
  final FocusNode _salePriceFocus = FocusNode();
  final FocusNode _providerFocus = FocusNode();
  final FocusNode _reasonFocus = FocusNode();
  final FocusNode _commentsFocus = FocusNode();

  @override
  void dispose() {
    _quantityFocus.dispose();
    _itemNameFocus.dispose();
    _destProjectNameFocus.dispose();
    _destSublocationFocus.dispose();
    _sourceProjectNameFocus.dispose();
    _sourceSublocationFocus.dispose();
    _costPriceFocus.dispose();
    _salePriceFocus.dispose();
    _providerFocus.dispose();
    _reasonFocus.dispose();
    _commentsFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<InventorylogType>(
      valueListenable: widget.controller.inventorylogType,
      builder: (context, inventorylogType, _) => _InventorylogFormSkeleton(
        quantity: wrapWidgetForMeasure(TextFormField(
          controller: widget.controller.quantity,
          focusNode: _quantityFocus,
          textInputAction: TextInputAction.next,
          // Text capitalization set here due to iOS bug where the
          // capitalization prop is cached from previous textfields.
          textCapitalization: TextCapitalization.sentences,
          keyboardType: const TextInputType.numberWithOptions(
              decimal: true, signed: true),
          inputFormatters: [CentesimalTextInputFormatter()],
          decoration: InputDecoration(
            labelText: AppLocalizations.of(context)!.formQuantity,
            errorStyle: const TextStyle(height: 0, fontSize: 0),
          ),
          validator: getRequiredFieldValidator(),
          onFieldSubmitted: (value) => maybeRequestFocus(
              _itemNameFocus, widget.controller.itemName.text, true),
        )),
        itemName: NewableFieldSuggestionTypeahead(
          key: _itemNameKey,
          suggestionsQuery:
              const InventorylogItemNameSuggestionRepositoryQuery(),
          queryScope: getOrgScope(context),
          onSelect: (_) => _onItemNameDone(false),
          textFieldConfiguration: TypeaheadTextFieldConfiguration(
            focusNode: _itemNameFocus,
            controller: widget.controller.itemName,
            textInputAction: TextInputAction.next,
            textCapitalization: TextCapitalization.sentences,
            decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.itemName),
            onSubmitted: (_) => _onItemNameDone(true),
            validator: getRequiredFieldValidator(),
          ),
        ),
        source: _getSourceRow(context, inventorylogType),
        dest: _getDestRow(context, inventorylogType),
        costPrice: _getCostPriceRow(context, inventorylogType),
        reason: _getReasonRow(context, inventorylogType),
        comments: getCommentsRow(
          context: context,
          focusNode: _commentsFocus,
          controller: widget.controller,
        ),
      ),
    );
  }

  Widget _getSourceRow(
      BuildContext context, InventorylogType inventorylogType) {
    final hasSourceProject = inventorylogType != InventorylogType.incoming;
    final hasDestProject = inventorylogType != InventorylogType.outgoing;
    return hasSourceProject
        ? getProjectSublocationRow(
            context: context,
            prefix: AppLocalizations.of(context)!.from_.toUpperCase(),
            prefixColor: inventorylogType.color,
            projectFocusNode: _sourceProjectNameFocus,
            projectController: widget.controller.sourceProjectName,
            sublocationFocusNode: _sourceSublocationFocus,
            sublocationController: widget.controller.sourceSublocation,
            nextController: hasDestProject
                ? widget.controller.destProjectName
                : widget.controller.provider,
            nextFocusNode:
                hasDestProject ? _destProjectNameFocus : _providerFocus,
          )
        : _getProviderRecipientRow(context, inventorylogType);
  }

  Widget _getDestRow(BuildContext context, InventorylogType inventorylogType) {
    final hasSourceProject = inventorylogType != InventorylogType.incoming;
    final hasDestProject = inventorylogType != InventorylogType.outgoing;
    return hasDestProject
        ? getProjectSublocationRow(
            context: context,
            prefix: AppLocalizations.of(context)!.to_.toUpperCase(),
            prefixColor: inventorylogType.color,
            projectFocusNode: _destProjectNameFocus,
            projectController: widget.controller.destProjectName,
            sublocationFocusNode: _destSublocationFocus,
            sublocationController: widget.controller.destSublocation,
            nextController:
                hasSourceProject ? null : widget.controller.costPrice,
            nextFocusNode: hasSourceProject ? null : _costPriceFocus,
          )
        : _getProviderRecipientRow(context, inventorylogType);
  }

  Widget _getProviderRecipientRow(
      BuildContext context, InventorylogType inventorylogType) {
    final isRecipient = inventorylogType == InventorylogType.outgoing;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            flex: 2,
            child: Padding(
              // FIXME: hacky
              padding: const EdgeInsets.only(left: 8.0),
              child: Text(
                isRecipient
                    ? AppLocalizations.of(context)!.to_.toUpperCase()
                    : AppLocalizations.of(context)!.from_.toUpperCase(),
                style: Theme.of(context)
                    .textTheme
                    .bodySmall!
                    .copyWith(color: inventorylogType.color),
              ),
            )),
        Expanded(
          flex: 8,
          child: NewableFieldSuggestionTypeahead(
            key: _providerKey,
            suggestionsQuery: const SearchProvidersRepositoryQuery(),
            queryScope: getOrgScope(context),
            onSelect: (_) => _onProviderRecipientDone(false),
            textFieldConfiguration: TypeaheadTextFieldConfiguration(
              focusNode: _providerFocus,
              controller: widget.controller.provider,
              textInputAction: TextInputAction.next,
              textCapitalization: TextCapitalization.sentences,
              decoration: InputDecoration(
                  labelText: isRecipient
                      ? AppLocalizations.of(context)!.recipient
                      : AppLocalizations.of(context)!.provider),
              onSubmitted: (_) => _onProviderRecipientDone(true),
            ),
          ),
        ),
      ],
    );
  }

  Widget? _getCostPriceRow(
      BuildContext context, InventorylogType inventorylogType) {
    final hasCost = inventorylogType == InventorylogType.incoming;
    final hasSale = inventorylogType == InventorylogType.outgoing;
    if (!hasCost && !hasSale) {
      return null;
    }
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: ValueListenableBuilder<double>(
            valueListenable: getFormFieldHeight(context),
            builder: (_, height, child) => SizedBox(
              height: height,
              child: child,
            ),
            child: ValueListenableBuilder<bool>(
              valueListenable: widget.controller.priceIsUnit,
              builder: (context, priceIsUnit, __) => TextButton(
                onPressed: () => _togglePriceIsUnit(),
                child: Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    (priceIsUnit
                        ? AppLocalizations.of(context)!.unit_
                        : AppLocalizations.of(context)!.total_),
                    style: TextStyle(color: inventorylogType.color),
                  ),
                ),
              ),
            ),
          ),
        ),
        Expanded(
          flex: 4,
          child: TextFormField(
            controller: hasCost
                ? widget.controller.costPrice
                : widget.controller.salePrice,
            focusNode: hasCost ? _costPriceFocus : _salePriceFocus,
            textInputAction: inventorylogType == InventorylogType.outgoing
                ? TextInputAction.next
                : TextInputAction.done,
            // Text capitalization set here due to iOS bug where the
            // capitalization prop is cached from previous textfields.
            textCapitalization: TextCapitalization.sentences,
            keyboardType: const TextInputType.numberWithOptions(
                decimal: true, signed: true),
            inputFormatters: [CurrencyTextInputFormatter()],
            decoration: InputDecoration(
                labelText: hasCost
                    ? AppLocalizations.of(context)!.expense
                    : AppLocalizations.of(context)!.income),
            onFieldSubmitted: (_) => _onCostSaleDone(),
          ),
        ),
        const SizedBox(width: kFormHorizontalSpacing),
        Expanded(
          flex: 4,
          child: ValueListenableBuilder<double>(
            valueListenable: getFormFieldHeight(context),
            builder: (_, value, child) => SizedBox(
              height: value,
              child: child,
            ),
            child: DropdownButtonFormField<PaymentStatus>(
              iconSize: 0,
              value: widget.controller.paymentStatus.value,
              decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.status),
              items: PaymentStatus.values
                  .map((e) => DropdownMenuItem<PaymentStatus>(
                      value: e,
                      child: Text(
                        e.getDisplayValue(context),
                        style: TextStyle(
                          color: inventorylogType.color ??
                              Theme.of(context).colorScheme.primary,
                        ),
                      )))
                  .toList(growable: false),
              onChanged: (value) =>
                  widget.controller.paymentStatus.value = value!,
            ),
          ),
        ),
      ],
    );
  }

  Widget? _getReasonRow(
      BuildContext context, InventorylogType inventorylogType) {
    if (inventorylogType != InventorylogType.outgoing) {
      return null;
    }
    return NewableFieldSuggestionTypeahead(
      key: _reasonKey,
      suggestionsQuery: const InventorylogReasonSuggestionRepositoryQuery(),
      queryScope: getOrgScope(context),
      textFieldConfiguration: TypeaheadTextFieldConfiguration(
        focusNode: _reasonFocus,
        controller: widget.controller.reason,
        textInputAction: TextInputAction.done,
        textCapitalization: TextCapitalization.sentences,
        decoration:
            InputDecoration(labelText: AppLocalizations.of(context)!.reason),
      ),
    );
  }

  void _togglePriceIsUnit() {
    widget.controller.priceIsUnit.value = !widget.controller.priceIsUnit.value;
    if (widget.controller.quantity.text.isEmpty) {
      return;
    }
    final quantity =
        CentesimalValueObject.parse(widget.controller.quantity.text);
    if (quantity == 0) {
      return;
    }

    final shouldMultiply = !widget.controller.priceIsUnit.value;
    _maybeAdjustPrice(
        widget.controller.salePrice, shouldMultiply, quantity / 100);
    _maybeAdjustPrice(
        widget.controller.costPrice, shouldMultiply, quantity / 100);
  }

  void _maybeAdjustPrice(
    TextEditingController price,
    bool shouldMultiply,
    double quantity,
  ) {
    final intPrice = CurrencyValueObject.parse(price.text);
    if (intPrice == null) {
      return;
    }

    final newIntPrice =
        shouldMultiply ? intPrice * quantity : intPrice / quantity;
    price.text = CurrencyValueObject(newIntPrice.round()).displayValue;
  }

  void _onItemNameDone(bool wasSubmitted) {
    if (widget.controller.inventorylogType.value == InventorylogType.incoming) {
      maybeRequestFocus(
        _providerFocus,
        widget.controller.provider.text,
        wasSubmitted,
      );
    } else {
      maybeRequestFocus(
        _sourceProjectNameFocus,
        widget.controller.sourceProjectName.text,
        wasSubmitted,
      );
    }
  }

  void _onProviderRecipientDone(bool wasSubmitted) {
    return widget.controller.inventorylogType.value == InventorylogType.outgoing
        ? maybeRequestFocus(
            _salePriceFocus,
            widget.controller.salePrice.text,
            wasSubmitted,
          )
        : maybeRequestFocus(
            _destProjectNameFocus,
            widget.controller.destProjectName.text,
            wasSubmitted,
          );
  }

  void _onCostSaleDone() {
    return widget.controller.inventorylogType.value == InventorylogType.outgoing
        ? maybeRequestFocus(_reasonFocus, widget.controller.reason.text, true)
        : null;
  }
}

class NonEditableInventorylogForm extends StatelessWidget {
  final InventorylogFormController controller;

  const NonEditableInventorylogForm({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return IgnorePointer(
      child: Theme(
        data: Theme.of(context).copyWith(
          inputDecorationTheme: const InputDecorationTheme(
            contentPadding: EdgeInsets.only(),
            border: InputBorder.none,
            isDense: true,
          ),
        ),
        child: _InventorylogFormSkeleton(
          isEditable: false,
          quantity: TextFormField(
            controller: controller.quantity,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.formQuantity,
            ),
          ),
          itemName: TextFormField(
            controller: controller.itemName,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context)!.itemName,
            ),
          ),
          source: _getNonEditableSourceRow(context),
          dest: _getNonEditableDestRow(context),
          costPrice: _getNonEditableCostPriceRow(context),
          reason: _getNonEditableReasonRow(context),
          comments: controller.comments.text.isEmpty
              ? null
              : TextFormField(
                  controller: controller.comments,
                  decoration: InputDecoration(
                    labelText: AppLocalizations.of(context)!.formComments,
                  ),
                ),
        ),
      ),
    );
  }

  Widget? _getNonEditableSourceRow(BuildContext context) {
    final inventorylogType = controller.inventorylogType.value;
    final hasSourceProject = inventorylogType != InventorylogType.incoming;
    return hasSourceProject
        ? getNonEditableProjectSublocationRow(
            context: context,
            prefix: AppLocalizations.of(context)!.from_.toUpperCase(),
            projectController: controller.sourceProjectName,
            sublocationController: controller.sourceSublocation,
          )
        : _getNonEditableProviderRecipientRow(context);
  }

  Widget? _getNonEditableDestRow(BuildContext context) {
    final inventorylogType = controller.inventorylogType.value;
    final hasDestProject = inventorylogType != InventorylogType.outgoing;
    return hasDestProject
        ? getNonEditableProjectSublocationRow(
            context: context,
            prefix: AppLocalizations.of(context)!.to_.toUpperCase(),
            projectController: controller.destProjectName,
            sublocationController: controller.destSublocation,
          )
        : _getNonEditableProviderRecipientRow(context);
  }

  Widget? _getNonEditableProviderRecipientRow(BuildContext context) {
    if (controller.provider.text.isEmpty) {
      return null;
    }
    final isRecipient =
        controller.inventorylogType.value == InventorylogType.outgoing;
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Expanded(
            flex: 2,
            child: Text(
              isRecipient
                  ? AppLocalizations.of(context)!.to_.toUpperCase()
                  : AppLocalizations.of(context)!.from_.toUpperCase(),
              style: Theme.of(context).textTheme.bodySmall,
            )),
        Expanded(
          flex: 8,
          child: TextFormField(
            controller: controller.provider,
            decoration: InputDecoration(
                labelText: isRecipient
                    ? AppLocalizations.of(context)!.recipient
                    : AppLocalizations.of(context)!.provider),
          ),
        ),
      ],
    );
  }

  Widget? _getNonEditableCostPriceRow(BuildContext context) {
    final inventorylogType = controller.inventorylogType.value;
    final hasCost = inventorylogType == InventorylogType.incoming;
    final hasSale = inventorylogType == InventorylogType.outgoing;
    if (!hasCost && !hasSale) {
      return null;
    }

    final priceIsUnit = controller.priceIsUnit.value;
    final paymentStatus = controller.paymentStatus.value;
    final priceController =
        hasCost ? controller.costPrice : controller.salePrice;
    if (priceController.text.isEmpty) {
      return null;
    }
    return Row(
      children: [
        Expanded(
          flex: 2,
          child: Text(priceIsUnit
              ? AppLocalizations.of(context)!.unit_
              : AppLocalizations.of(context)!.total_),
        ),
        Expanded(
          flex: 4,
          child: TextFormField(
            controller: priceController,
            decoration: InputDecoration(
                labelText: hasCost
                    ? AppLocalizations.of(context)!.expense
                    : AppLocalizations.of(context)!.income),
          ),
        ),
        const SizedBox(width: kFormHorizontalSpacing),
        Expanded(
          flex: 4,
          child: TextFormField(
            initialValue: paymentStatus.getDisplayValue(context),
            decoration: InputDecoration(
                labelText: AppLocalizations.of(context)!.status),
          ),
        ),
      ],
    );
  }

  Widget? _getNonEditableReasonRow(BuildContext context) {
    if (controller.inventorylogType.value != InventorylogType.outgoing) {
      return null;
    }
    if (controller.reason.text.isEmpty) {
      return null;
    }

    return TextFormField(
      controller: controller.reason,
      decoration:
          InputDecoration(labelText: AppLocalizations.of(context)!.reason),
    );
  }
}

class _InventorylogFormSkeleton extends StatelessWidget {
  final Widget quantity;
  final Widget itemName;
  final Widget? source;
  final Widget? dest;
  final Widget? costPrice;
  final Widget? reason;
  final Widget? comments;
  final bool isEditable;

  const _InventorylogFormSkeleton({
    required this.quantity,
    required this.itemName,
    required this.source,
    required this.dest,
    this.costPrice,
    this.reason,
    this.comments,
    this.isEditable = true,
  });

  @override
  Widget build(BuildContext context) {
    final children = [
      Row(
        children: [
          Expanded(
            flex: 3,
            child: quantity,
          ),
          const SizedBox(width: kFormHorizontalSpacing),
          Expanded(
            flex: 7,
            child: itemName,
          ),
        ],
      ),
      if (source != null) ...[
        const SizedBox(height: kFormVerticalSpacing),
        source!,
      ],
      if (dest != null) ...[
        const SizedBox(height: kFormVerticalSpacing),
        dest!,
      ],
      if (costPrice != null) ...[
        const SizedBox(height: kFormVerticalSpacing),
        costPrice!,
      ],
      if (reason != null) ...[
        const SizedBox(height: kFormVerticalSpacing),
        reason!,
      ],
      if (comments != null) ...[
        const SizedBox(height: kFormVerticalSpacing),
        comments!,
      ]
    ];

    return isEditable
        ? SliverList(delegate: SliverChildListDelegate.fixed(children))
        : Column(children: children);
  }
}
