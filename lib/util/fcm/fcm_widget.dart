import 'dart:async';

import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/util/fcm/fcm_organization_topic_subscriptions_monitor.dart';
import 'package:bitacora/util/fcm/fcm_utils.dart';
import 'package:bitacora/util/fcm/firebase_messaging.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';

class FcmWidget extends StatefulWidget {
  final Widget child;

  const FcmWidget({
    super.key,
    required this.child,
  });

  @override
  State<FcmWidget> createState() => _FcmWidgetState();
}

class _FcmWidgetState extends State<FcmWidget> {
  FcmOrganizationTopicSubscriptionsMonitor? _topicSubscriptionsMonitor;
  late final StreamSubscription onMessageSubscription;

  @override
  void initState() {
    super.initState();

    final firebaseMessaging = FirebaseMessaging();

    firebaseMessaging.onBackgroundMessage(FcmUtils.backgroundHandler);

    onMessageSubscription = firebaseMessaging.onMessage().listen(_onMessage);
  }

  void _onMessage(payload) {
    FcmUtils().foregroundHandler(context, payload);
  }

  @override
  void dispose() {
    onMessageSubscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _topicSubscriptionsMonitor ??= FcmOrganizationTopicSubscriptionsMonitor(
      context.watch<Repository>(),
    );
    return widget.child;
  }
}
