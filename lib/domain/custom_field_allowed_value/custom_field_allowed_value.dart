import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_label.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_value.dart';

class CustomFieldAllowedValue extends Model {
  final CustomFieldAllowedValueValue? value;
  final CustomFieldAllowedValueLabel? label;

  final CustomField? customField;
  final CustomFieldAllowedValue? parent;

  CustomFieldAllowedValue({
    super.id,
    super.remoteId,
    this.value,
    this.label,
    this.customField,
    this.parent,
  });

  @override
  Map<Field, dynamic> get fields => {
        CustomFieldAllowedValueField.id: id,
        CustomFieldAllowedValueField.remoteId: remoteId,
        CustomFieldAllowedValueField.value: value,
        CustomFieldAllowedValueField.label: label,
        CustomFieldAllowedValueField.customField: customField,
        CustomFieldAllowedValueField.parent: parent,
      };
}

enum CustomFieldAllowedValueField with Field {
  id,
  remoteId,
  value,
  label,
  customField,
  parent,
}

const customFieldAllowedValueNestedModelFields = <Field>{
  CustomFieldAllowedValueField.customField,
  CustomFieldAllowedValueField.parent,
};
