import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';

class EntryUrlUtilContextSnapshot extends ContextSnapshot {
  EntryUrlUtilContextSnapshot(super.context);

  @override
  List<ValueShot> get valueShots => [
        ValueShot.navigator(),
        ValueShot.localeOf(),
        ValueShot.provider<ActiveSession>(),
        ValueShot.provider<ApiHelper>(),
        ValueShot.provider<AnalyticsLogger>(),
        ValueShot.provider<RecoverSessionLauncher>(),
      ];
}
