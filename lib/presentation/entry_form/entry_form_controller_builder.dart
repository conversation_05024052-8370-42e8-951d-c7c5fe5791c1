import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller.dart';
import 'package:bitacora/presentation/entry_form/entry_form_controller_context_snapshot.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/personnellog/personnellog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/progresslog/progresslog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/simplelog/simplelog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/templatelog/templatelog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/type_selector/entry_form_page_type.dart';
import 'package:bitacora/presentation/entry_form/worklog/worklog_form_controller.dart';
import 'package:flutter/widgets.dart';

class EntryFormControllerBuilder {
  const EntryFormControllerBuilder();

  EntryFormController build(
    BuildContext context,
    EntryFormPageType entryFormPageType,
    EntryFormProps props,
  ) {
    final contextSnapshot = EntryFormControllerContextSnapshot(context);

    switch (entryFormPageType.extensionType) {
      case ExtensionType.worklog:
        return WorklogFormController(contextSnapshot, props);
      case ExtensionType.inventorylog:
        return InventorylogFormController(contextSnapshot, props);
      case ExtensionType.personnellog:
        return PersonnellogFormController(contextSnapshot, props);
      case ExtensionType.progresslog:
        return ProgresslogFormController(contextSnapshot, props);
      case ExtensionType.templatelog:
        return TemplatelogFormController(
          contextSnapshot,
          props,
          entryFormPageType.template!,
        );
      case ExtensionType.simplelog:
        return SimplelogFormController(contextSnapshot, props);
    }
  }
}
