import 'dart:io';

import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/bug_report/bug_report_service.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/presentation/widgets/app_bar_action_button.dart';
import 'package:bitacora/presentation/widgets/top_snack_bar.dart';
import 'package:bitacora/util/share.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

import '../../l10n/app_localizations.dart';

class BugReportFormPage extends StatefulWidget {
  const BugReportFormPage({super.key});

  @override
  State<BugReportFormPage> createState() => _BugReportFormPageState();
}

class _BugReportFormPageState extends State<BugReportFormPage> {
  final TextEditingController controller = TextEditingController();

  bool _isSending = false;
  bool _isSharing = false;
  bool _didFail = false;
  int? _currentStep;
  int? _totalSteps;
  File? _report;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: FocusScope.of(context).unfocus,
      child: Scaffold(
        appBar: AppBar(
          actions: [
            AppBarActionButton(
              text: AppLocalizations.of(context)!.send,
              icon: Icons.send,
              onPressed: _isBusy ? null : () => _onTapSend(context),
            ),
          ],
          centerTitle: false,
          title: Text(AppLocalizations.of(context)!.bugReport),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _BugReportTopSnackBar(
              isCreating: _isSending,
              isSharing: _isSharing,
              didFail: _didFail,
              report: _report,
              onShare: _share,
              step: _currentStep != null && _totalSteps != null
                  ? '($_currentStep/$_totalSteps)'
                  : '',
            ),
            Expanded(
              child: _isBusy
                  ? Opacity(
                      opacity: 0.5,
                      child: IgnorePointer(
                        child: _BugReportForm(controller: controller),
                      ),
                    )
                  : _BugReportForm(controller: controller),
            ),
          ],
        ),
      ),
    );
  }

  bool get _isBusy => _isSending;

  void _onTapSend(BuildContext context) async {
    setState(() {
      _didFail = false;
      _isSending = true;
      _report = null;
    });

    try {
      final service = BugReportService();
      _report = await service.createAndSend(
        context.read<Repository>(),
        context.read<ActiveSession>().value,
        context.read<AnalyticsLogger>(),
        description: controller.text,
        showNotification: false,
        callback: (step, total) => setState(() {
          _currentStep = step;
          _totalSteps = total;
        }),
      );
    } catch (_) {
      setState(() {
        _isSending = false;
        _didFail = true;
      });
      return;
    }

    setState(() {
      _isSending = false;
    });
  }

  Future<void> _share() async {
    setState(() {
      _isSharing = true;
    });
    await Share().shareFiles([_report!.path]);
    setState(() {
      _isSharing = false;
    });
  }
}

class _BugReportTopSnackBar extends StatelessWidget {
  final bool isCreating;
  final bool isSharing;
  final bool didFail;
  final File? report;
  final VoidCallback onShare;
  final String step;

  const _BugReportTopSnackBar({
    required this.isCreating,
    required this.isSharing,
    required this.didFail,
    required this.onShare,
    required this.step,
    this.report,
  });

  @override
  Widget build(BuildContext context) {
    if (didFail) {
      return TopSnackBar(
        title:
            Text('${AppLocalizations.of(context)!.reportFailedToCreate} $step'),
        isError: true,
      );
    }

    if (isSharing) {
      return TopSnackBar(
          title: Text('${AppLocalizations.of(context)!.sharing}...'));
    }

    if (report != null) {
      return TopSnackBar(
        title: Text(AppLocalizations.of(context)!.sent),
        action: OutlinedButton(
          onPressed: onShare,
          child: Text(AppLocalizations.of(context)!.share),
        ),
      );
    }

    if (isCreating) {
      return TopSnackBar(
          title: Text('${AppLocalizations.of(context)!.sending} $step...'));
    }

    return const SizedBox();
  }
}

class _BugReportForm extends StatefulWidget {
  final TextEditingController controller;

  const _BugReportForm({required this.controller});

  @override
  State<_BugReportForm> createState() => _BugReportFormState();
}

class _BugReportFormState extends State<_BugReportForm> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Padding(
        padding: kPageInsets,
        child: Form(
          child: Column(
            children: [
              TextFormField(
                controller: widget.controller,
                minLines: 3,
                maxLines: null,
                keyboardType: TextInputType.multiline,
                textInputAction: TextInputAction.newline,
                textCapitalization: TextCapitalization.sentences,
                decoration: InputDecoration(
                  labelText: AppLocalizations.of(context)!.bugReportHint,
                  alignLabelWithHint: true,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
