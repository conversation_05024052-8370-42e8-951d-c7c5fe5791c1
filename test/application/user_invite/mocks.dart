import 'package:bitacora/application/user_invite/user_invite_service.dart';
import 'package:mocktail/mocktail.dart';

class MockUserInviteServiceInjector extends Mock
    implements UserInviteServiceInjector {}

class MockUserInviteService extends Mock implements UserInviteService {}

MockUserInviteService mockUserInviteService() {
  final mock = MockUserInviteService();
  when(() => mock.invite(any(), any(), any()))
      .thenAnswer((_) => Future.value(true));
  when(() => mock.sendUserInvite(any(), any()))
      .thenAnswer((_) => Future.value(true));

  return mock;
}
