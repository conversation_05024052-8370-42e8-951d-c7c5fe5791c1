import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/open_state_progresslogs_db_query.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/progresslog_detail_in_open_state_option.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:provider/provider.dart';

class NonEditableEntryOpenStateOption extends StatefulWidget {
  final EntryFormDateTimeOptionController controller;

  const NonEditableEntryOpenStateOption({
    super.key,
    required this.controller,
  });

  @override
  State<NonEditableEntryOpenStateOption> createState() =>
      _NonEditableEntryOpenStateOptionState();
}

class _NonEditableEntryOpenStateOptionState
    extends State<NonEditableEntryOpenStateOption> {
  bool _hasQueriedProgresslogs = false;
  List<Entry>? _progresslogs;

  Future<void> _initFromBuild(BuildContext context) async {
    if (_hasQueriedProgresslogs) {
      return;
    }
    _hasQueriedProgresslogs = true;

    final db = context.read<Repository>();
    final result = await const OpenStateProgresslogsDbQuery()
        .run(db.context(), _getOpenEntry().id!);
    setState(() {
      _progresslogs = result;
    });
  }

  @override
  Widget build(BuildContext context) {
    _initFromBuild(context);

    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: kFormVerticalSpacing,
        horizontal: kFormHorizontalSpacing,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context)!.scheduledEntry,
            style: Theme.of(context).textTheme.bodySmall,
          ),
          const SizedBox(height: kFormVerticalSpacing),
          Row(
            children: [
              Text(
                AppLocalizations.of(context)!.date,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(width: kFormHorizontalSpacing),
              Text(_getDatesText(context)),
            ],
          ),
          Row(
            children: [
              Text(
                AppLocalizations.of(context)!.type,
                style: Theme.of(context).textTheme.bodySmall,
              ),
              const SizedBox(width: kFormHorizontalSpacing),
              Text(_getTypeText(context)),
            ],
          ),
          if (_progresslogs != null && _progresslogs!.isNotEmpty)
            ..._getProgresslogsHeader(context),
          if (_progresslogs != null && _progresslogs!.isNotEmpty)
            ..._progresslogs!
                .map((e) => ProgresslogDetailInOpenStateOption(entry: e))
        ],
      ),
    );
  }

  String _getDatesText(BuildContext context) {
    final start = _getDateText(_getOpenState().startDay);
    final end = _getDateText(_getOpenState().endDay);
    return '$start${end.isEmpty || start == end ? '' : ' -> $end'}';
  }

  String _getDateText(LogDay? day) {
    if (day == null) {
      return '';
    }
    return kLogDayDisplayDateFormat.format(getDateTimeFromLogDay(day));
  }

  String _getTypeText(BuildContext context) {
    return _getOpenState().progressive!.value
        ? AppLocalizations.of(context)!.progressive
        : AppLocalizations.of(context)!.complete;
  }

  List<Widget> _getProgresslogsHeader(BuildContext context) {
    return <Widget>[
      const SizedBox(height: kFormVerticalSpacing * 2),
      Text(
        _getOpenState().progressive!.value
            ? AppLocalizations.of(context)!.progress
            : AppLocalizations.of(context)!.completed,
        style: Theme.of(context).textTheme.bodySmall,
      ),
      const SizedBox(height: kFormVerticalSpacing),
    ];
  }

  Entry _getOpenEntry() {
    final inputEntry = widget.controller.liveEntry.value!;
    if (inputEntry.openState != null) {
      return inputEntry;
    }
    return inputEntry.progresslog!.entry!;
  }

  OpenState _getOpenState() {
    return _getOpenEntry().openState!;
  }
}
