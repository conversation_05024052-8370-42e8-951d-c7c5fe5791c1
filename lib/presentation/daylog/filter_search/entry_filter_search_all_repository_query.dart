import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/filter/entry_filter.dart';
import 'package:bitacora/presentation/entry_form/entry_form_page_load_repository_query.dart';

class EntryFilterSearchAllRepositoryQuery extends RepositoryQuery<List<Entry>> {
  final String pattern;
  final EntryFilter filter;

  const EntryFilterSearchAllRepositoryQuery({
    required this.pattern,
    required this.filter,
  });

  @override
  Future<List<Entry>> run(RepositoryQueryContext context) =>
      context.db.entry.searchAll(context, pattern, filter);

  @override
  Fields fields(Repository db) => EntryFormPageLoadRepositoryQuery.sFields(db);
}
