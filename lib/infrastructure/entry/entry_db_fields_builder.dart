import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/lat_lng_value_object.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/entry_fields_builder.dart';
import 'package:bitacora/domain/entry/value/entry_timer_status.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/open_state/open_state.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry/entry_db_contract.dart';
import 'package:latlong2/latlong.dart';

class EntryDbFieldsBuilder extends DbFieldsBuilder
    implements EntryFieldsBuilder {
  EntryDbFieldsBuilder() {
    _id();
  }

  EntryDbContract get contract => const EntryDbContract();

  EntryDbFieldsBuilder _id() {
    addField(
      EntryField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder remoteId() {
    addField(
      EntryField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder day() {
    addField(
      EntryField.day,
      DbField(
        column: contract.day,
        valueBuilder: (v) => LogDay(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder time() {
    addField(
      EntryField.time,
      DbField(
        column: contract.time,
        valueBuilder: (v) => LogTime(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder startDate() {
    addField(
      EntryField.startDate,
      DbField(
        column: contract.startDate,
        valueBuilder: (v) => NullableLogDay(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder endDate() {
    addField(
      EntryField.endDate,
      DbField(
        column: contract.endDate,
        valueBuilder: (v) => NullableLogDay(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder startTime() {
    addField(
      EntryField.startTime,
      DbField(
        column: contract.startTime,
        valueBuilder: (v) => NullableLogTime(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder endTime() {
    addField(
      EntryField.endTime,
      DbField(
        column: contract.endTime,
        valueBuilder: (v) => NullableLogTime(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder comments() {
    addField(
      EntryField.comments,
      DbField(
        column: contract.comments,
        valueBuilder: (v) => EntryComments(v),
      ),
    );
    return this;
  }

  @override
  EntryFieldsBuilder location() {
    addField(
      EntryField.location,
      DbField(
        columnAdder: (columns) =>
            columns.addAll([contract.locLatitude, contract.locLongitude]),
        multiValueBuilder: (values) {
          if (values[contract.locLongitude] == null ||
              values[contract.locLatitude] == null) {
            return const LatLngValueObject(null);
          }

          return LatLngValueObject(
            LatLng(values[contract.locLatitude], values[contract.locLongitude]),
          );
        },
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder createdAt() {
    addField(
      EntryField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) =>
            EntryCreatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder updatedAt() {
    addField(
      EntryField.updatedAt,
      DbField(
        column: contract.updatedAt,
        valueBuilder: (v) =>
            EntryUpdatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder syncVersion() {
    addField(
      EntryField.syncVersion,
      DbField(
        column: contract.syncVersion,
        valueBuilder: (v) => EntrySyncVersion(v),
      ),
    );
    return this;
  }

  @override
  EntryFieldsBuilder timerStatus() {
    addField(
      EntryField.timerStatus,
      DbField(
        column: contract.timerStatus,
        valueBuilder: (v) => v == null
            ? const EntryTimerStatus(null)
            : EntryTimerStatus.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder assignee(Fields fields) {
    addField(
      EntryField.assignee,
      DbField(
        key: EntryField.assignee,
        column: contract.assigneeId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.user.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder author(Fields fields) {
    addField(
      EntryField.author,
      DbField(
        key: EntryField.author,
        column: contract.authorId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.user.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder inventorylog(Fields fields) {
    addField(
      EntryField.inventorylog,
      DbField(nestedFields: fields as DbFields),
    );
    return _extension();
  }

  @override
  EntryDbFieldsBuilder personnellog(Fields fields) {
    addField(
      EntryField.personnellog,
      DbField(nestedFields: fields as DbFields),
    );
    return _extension();
  }

  @override
  EntryDbFieldsBuilder worklog(Fields fields) {
    addField(
      EntryField.worklog,
      DbField(nestedFields: fields as DbFields),
    );
    return _extension();
  }

  @override
  EntryDbFieldsBuilder progresslog(Fields fields) {
    addField(
      EntryField.progresslog,
      DbField(nestedFields: fields as DbFields),
    );
    return _extension();
  }

  @override
  EntryDbFieldsBuilder templatelog(Fields fields) {
    addField(
      EntryField.templatelog,
      DbField(nestedFields: fields as DbFields),
    );
    return _extension();
  }

  @override
  EntryDbFieldsBuilder openState(Fields fields) {
    addField(
      EntryField.openState,
      DbField(
        key: EntryField.openState,
        column: contract.openStateId,
        nestedFields: fields as DbFields,
        multiNestedBuilder: (context, values, props) async {
          final id = values[contract.openStateId];
          if (id == null) {
            return null;
          }
          return context.db.openState.find(
            context.nested(EntryField.openState, props: props)!,
            LocalId(id),
          );
        },
      ),
    );

    if (fields.map[OpenStateField.startDay] != null) {
      return day();
    }
    return this;
  }

  @override
  EntryDbFieldsBuilder entryGroupEntry(Fields fields) {
    addField(
      EntryField.entryGroupEntry,
      DbField(
        key: EntryField.entryGroupEntry,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext
            .db.entryGroupEntry
            .findByEntry(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  EntryFieldsBuilder attachments(Fields fields) {
    addField(
      EntryField.attachments,
      DbField(
        key: EntryField.attachments,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.attachment.findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  EntryFieldsBuilder tags(Fields fields) {
    addField(
      EntryField.tags,
      DbField(
        key: EntryField.tags,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.tag.findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder locationTracking(Fields fields) {
    addField(
      EntryField.locationTracking,
      DbField(
        key: EntryField.locationTracking,
        column: contract.locationTrackingId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext
            .db.locationTracking
            .find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  EntryDbFieldsBuilder signatures(Fields fields) {
    addField(
      EntryField.signatures,
      DbField(
        key: EntryField.signatures,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.signature.findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  EntryDbFieldsBuilder _extension() {
    addField(
      EntryField.extension,
      DbField(
        columnAdder: (list) {
          list.add(contract.extensionId);
          list.add(contract.extensionType);
        },
        multiNestedBuilder: (context, values, _) async {
          final extensionType =
              ExtensionType.fromDbValue(values[contract.extensionType]);
          final id = LocalId(values[contract.extensionId]);

          final db = context.db;
          switch (extensionType) {
            case ExtensionType.worklog:
              final nestedContext = context.nested(EntryField.worklog);
              return nestedContext == null
                  ? null
                  : db.worklog.find(nestedContext, id);
            case ExtensionType.inventorylog:
              final nestedContext = context.nested(EntryField.inventorylog);
              return nestedContext == null
                  ? null
                  : db.inventorylog.find(nestedContext, id);
            case ExtensionType.personnellog:
              final nestedContext = context.nested(EntryField.personnellog);
              return nestedContext == null
                  ? null
                  : db.personnellog.find(nestedContext, id);
            case ExtensionType.progresslog:
              final nestedContext = context.nested(EntryField.progresslog);
              return nestedContext == null
                  ? null
                  : db.progresslog.find(nestedContext, id);
            case ExtensionType.templatelog:
              final nestedContext = context.nested(EntryField.templatelog);
              return nestedContext == null
                  ? null
                  : db.templatelog.find(nestedContext, id);
            case ExtensionType.simplelog:
              throw 'Extension <${ExtensionType.simplelog}> not supported';
          }
        },
      ),
    );
    return this;
  }

  @override
  EntryFieldsBuilder source(Fields fields) {
    addField(
      EntryField.source,
      DbField(
        key: EntryField.source,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext.db.entrySource
            .findByEntryId(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  EntryFieldsBuilder metadata(Fields fields) {
    addField(
      EntryField.metadata,
      DbField(
        key: EntryField.metadata,
        column: contract.id,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext.db.entryMetadata
            .findAll(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
