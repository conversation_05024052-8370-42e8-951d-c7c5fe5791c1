import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/infrastructure/db_context.dart';

abstract class DbTranslator<T extends Model> {
  // Which of the model's fields are also models?
  Set<Field> get nestedModelFields;

  // Create a model from db query results. Might require loading children from
  // db to complete query.
  Future<T> fromDb(DbContext context, Map<String, dynamic> map);

  // Creates the map to save to db. Might save children internally to resolve
  // children ids.
  Future<Map<String, dynamic>> toDb(DbContext context, T model);
}

void addField(
  Map<String, dynamic> map,
  String key,
  ValueObject? valueObject,
) {
  if (valueObject != null) {
    map[key] = valueObject.dbValue;
  }
}

Future<void> saveNestedModel<T extends Model>(
  DbContext context,
  Map<String, dynamic> map,
  String nestedModelIdColumn,
  RepositoryTable<T, DbContext, dynamic> nestedModelRepository,
  T? nestedModel, {
  bool skipIfContainsId = false,
}) async {
  if (nestedModel == null) {
    return;
  }

  if (skipIfContainsId && nestedModel.id != null) {
    map[nestedModelIdColumn] = nestedModel.id!.dbValue;
    return;
  }

  map[nestedModelIdColumn] =
      (await nestedModelRepository.save(context, nestedModel))?.dbValue;
}
