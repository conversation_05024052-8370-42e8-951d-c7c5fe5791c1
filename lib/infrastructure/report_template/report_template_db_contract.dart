import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class ReportTemplateDbContract extends DbContract {
  static const String _ = 'rt_';
  static const String _tableName = 'reportTemplate';

  final String id = '${_}id';
  final String active = '${_}active';
  final String description = '${_}description';
  final String option = '${_}option';
  final String order = '${_}order';
  final String organizationId = '${_}organizationId';

  const ReportTemplateDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithReportTemplateTable;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $active INTEGER,
    $description TEXT NOT NULL,
    $option TEXT NOT NULL,
    $order INTEGER NOT NULL,
    $organizationId INTEGER NOT NULL
  )
  ''';
}
