import 'package:bitacora/application/api/auth_interceptor.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/dev/api_tools/dev_api_man_in_the_middle.dart';
import 'package:bitacora/dev/api_tools/mock_man_in_the_middle.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/util/dio/mini_log_interceptor.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:dio/dio.dart';
import 'package:dio/io.dart';
import 'package:flutter/foundation.dart';
import 'package:logger/logger.dart';

final _kLocationTrackingServiceBaseUrl =
    '${AppConfig().locationTrackingApiUrl}/v1/location-trackings/';

class LocationTrackingApiHelper extends DioForNative {
  LocationTrackingApiHelper(
    AuthRepository authRepository,
    Organization organization,
  ) : super(BaseOptions(baseUrl: _kLocationTrackingServiceBaseUrl)) {
    final appConfig = AppConfig();
    interceptors.add(AuthInterceptor(
      authHeaderKey: 'x-Authorization',
      interceptors: interceptors,
      authRepository: authRepository,
    ));
    interceptors.add(
      appConfig.logLevel == Level.trace && kDebugMode
          ? LogInterceptor(
              requestBody: true,
              responseBody: true,
              logPrint: (object) => logger.log(appConfig.logLevel, object),
            )
          : MiniLogInterceptor(
              logPrint: (object) => logger.log(appConfig.logLevel, object)),
    );
    options.headers["x-Current-Org"] = organization.remoteId!.apiValue;

    if (appConfig.isDevToolsEnabled) {
      interceptors.add(DevApiManInTheMiddle());
    }
    if (appConfig.isIntegrationTest) {
      interceptors.add(MockManInTheMiddle());
    }
  }
}
