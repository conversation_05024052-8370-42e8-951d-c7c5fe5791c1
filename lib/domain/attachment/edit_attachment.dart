import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/attachment/value/attachment_doodle.dart';
import 'package:bitacora/domain/common/edit/edit_model.dart';
import 'package:flutter/foundation.dart';

class EditAttachment extends EditModel<Attachment> {
  AttachmentComments? _comments;
  AttachmentDoodle? _doodle;

  EditAttachment(super.liveModel);

  set comments(AttachmentComments? value) {
    if (_comments == value) {
      return;
    }

    _comments = value;
    onEdit();
  }

  set doodle(AttachmentDoodle? value) {
    if (_doodle == value) {
      return;
    }

    _doodle = value;
    onEdit();
  }

  @override
  @protected
  Attachment? get edit =>
      liveModel.value?.copyWith(comments: _comments, doodle: _doodle);

  @override
  bool hasEdits<T>() {
    if (_comments is T) {
      return true;
    }

    if (_doodle is T) {
      return true;
    }

    return false;
  }
}
