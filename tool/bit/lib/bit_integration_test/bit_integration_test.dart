import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _usage = '''
Usage:
 
  bit integration-test <filepath>
  bit integration-test <filepath> --profile
''';

const _help = '''
A tool for running integration tests.

Runs `flutter run` with correct flags for integration test.

$_usage

''';

const _undefinedFile = '''
Undefined File

You must specify the integration test file as a parameter.

$_usage

''';

Future<int> bitIntegrationTest([List<String> args = const <String>[]]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  if (args.isEmpty) {
    writeln(_undefinedFile, TextStyle.error);
    return 1;
  }

  writeln('Running integration test...', TextStyle.highlighted);

  final flutterArgs = [...args];
  flutterArgs.removeWhere((e) => e == '--no-header');

  final run = await startProcess(
    'flutter',
    ['run', ..._getRunFlags(flutterArgs)],
    ProcessStartMode.inheritStdio,
  );
  return run.exitCode;
}

List<String> _getRunFlags(List<String> flutterArgs) {
  return [
    '--dart-define=APP_NAME=Integracora.io',
    '--dart-define=APPLICATION_ID=com.bitacora.locust',
    '--dart-define=INTEGRATION_TEST=true',
    ...flutterArgs,
  ];
}
