import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/templatelog/value/templatelog_template_group_name.dart';

import 'package:mocktail/mocktail.dart';

import '../template/mocks.dart';

class MockTemplatelog extends Mock implements Templatelog {}

MockTemplatelog mockTemplatelog({
  TemplatelogTemplateName? templateName,
  List<CustomFieldMetadata>? fieldsMetadata,
  Template? template,
  Project? defaultProject,
}) {
  final mock = MockTemplatelog();
  final project = defaultProject ?? mockProject();
  final mockedTemplate = template ?? mockTemplate();
  when(() => mock.templateName)
      .thenReturn(templateName ?? TemplatelogTemplateName('Example'));
  when(() => mock.fieldsMetadata).thenReturn(fieldsMetadata ?? []);
  when(() => mock.template).thenReturn(mockedTemplate);
  when(() => mock.defaultProject).thenReturn(project);
  when(() => mock.extensionType).thenReturn(ExtensionType.templatelog);
  return mock;
}
