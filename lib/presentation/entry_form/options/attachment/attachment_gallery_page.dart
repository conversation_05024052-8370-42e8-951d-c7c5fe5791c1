import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/edit/edit_collection.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/attachment_gallery_page_item.dart';
import 'package:bitacora/presentation/entry_form/options/attachment/attachment_gallery_page_item_controller.dart';
import 'package:bitacora/presentation/widgets/text_field_done.dart';
import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/trim_text_on_focus_lost.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:share_plus/share_plus.dart';

const _kSwitchThemeDuration = Duration(milliseconds: 100);

class AttachmentGalleryPage extends StatefulWidget {
  final EditCollection<Attachment> attachments;
  final int initialPage;

  const AttachmentGalleryPage({
    super.key,
    required this.attachments,
    this.initialPage = 0,
  });

  @override
  State<AttachmentGalleryPage> createState() => _AttachmentGalleryPageState();
}

class _AttachmentGalleryPageState extends State<AttachmentGalleryPage> {
  List<AttachmentGalleryPageItemController> controllers = [];
  final FocusNode _commentsFocus = FocusNode();
  final ValueNotifier<bool> _isShowingDoodleEditor = ValueNotifier(false);
  late final PageController _pageController;
  bool _isShowingOverlay = true;
  late int _pageIndex;

  @override
  void initState() {
    super.initState();
    SystemChrome.setPreferredOrientations([]);
    _pageIndex = widget.initialPage;
    _pageController = PageController(initialPage: widget.initialPage);

    widget.attachments.addListener(onAttachmentsUpdated);
    _setControllers();
  }

  @override
  void dispose() {
    _pageController.dispose();
    widget.attachments.removeListener(onAttachmentsUpdated);
    for (var e in controllers) {
      e.dispose();
    }
    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
    super.dispose();
  }

  void onAttachmentsUpdated() {
    if (widget.attachments.value.isEmpty) {
      Navigator.of(context).pop();
      return;
    }

    setState(() {
      _maybeFixPageIndex();
      _setControllers();
    });
  }

  void _maybeFixPageIndex() {
    final currentAttachment = controllers[_pageIndex].editAttachment;
    final attachments = widget.attachments.value;
    for (var i = 0; i < attachments.length; i++) {
      if (attachments[i] == currentAttachment) {
        _pageIndex = i;
        break;
      }
    }
    if (_pageIndex >= attachments.length) {
      _pageIndex = attachments.length - 1;
    }

    _pageController.jumpToPage(_pageIndex);
  }

  void _setControllers() {
    for (final controller in controllers) {
      controller.dispose();
    }
    controllers = widget.attachments.value
        .map<AttachmentGalleryPageItemController>(
            (e) => AttachmentGalleryPageItemController(context, e))
        .toList(growable: false);
  }

  @override
  Widget build(BuildContext context) {
    final currentTheme = Theme.of(context);
    final doodleTheme = currentTheme.copyWith(
      brightness: Brightness.dark,
      colorScheme: ColorScheme.fromSeed(
        seedColor: currentTheme.colorScheme.primary,
        brightness: Brightness.dark,
      ),
      scaffoldBackgroundColor: Colors.black,
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.black,
      ),
    );
    return ValueListenableBuilder<bool>(
        valueListenable: _isShowingDoodleEditor,
        builder: (context, showDoodleEditor, _) {
          return AnimatedTheme(
            duration: _kSwitchThemeDuration,
            data: showDoodleEditor ? doodleTheme : currentTheme,
            child: Scaffold(
              extendBodyBehindAppBar: true,
              appBar: !_isShowingOverlay
                  ? null
                  : showDoodleEditor
                      ? null
                      : AppBar(
                          actions: [
                            _buildDoodleIcon(),
                            _buildShareIcon(),
                          ],
                          elevation: 0,
                        ),
              body: Stack(
                children: [
                  GestureDetector(
                    behavior: HitTestBehavior.opaque,
                    onTap: _onToggleOverlay,
                    child: PageView(
                      controller: _pageController,
                      physics: showDoodleEditor
                          ? const NeverScrollableScrollPhysics()
                          : const PageScrollPhysics(),
                      children: controllers
                          .map<Widget>(
                            (controller) => AttachmentGalleryPageItem(
                              controller: controller,
                              onDownloadAttachment:
                                  AttachmentUtils().downloadAttachment,
                              isShowingDoodleEditor: _isShowingDoodleEditor,
                            ),
                          )
                          .toList(growable: false),
                      onPageChanged: (int pageIndex) {
                        if (_commentsFocus.hasFocus) {
                          _commentsFocus.unfocus();
                        }

                        setState(() {
                          _pageIndex = pageIndex;
                        });
                      },
                    ),
                  ),
                  if (_isShowingOverlay && !showDoodleEditor)
                    _bottomPanel(context),
                ],
              ),
            ),
          );
        });
  }

  void _onToggleOverlay() {
    if (_isShowingDoodleEditor.value) {
      return;
    }

    if (_commentsFocus.hasFocus) {
      _commentsFocus.unfocus();
    } else {
      setState(() => _isShowingOverlay = !_isShowingOverlay);
    }
  }

  Widget _bottomPanel(BuildContext context) {
    final controller = controllers[_pageIndex];
    return Align(
      alignment: Alignment.bottomCenter,
      child: Stack(
        children: [
          Positioned(
            bottom: 0,
            width: MediaQuery.sizeOf(context).width,
            height: MediaQuery.paddingOf(context).bottom,
            child: Container(
              color: Theme.of(context).canvasColor,
            ),
          ),
          SafeArea(
            child: ClipRRect(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(4),
                topRight: Radius.circular(4),
              ),
              child: Material(
                color: Theme.of(context).canvasColor,
                child: AnimatedSize(
                  curve: Curves.easeIn,
                  duration: const Duration(milliseconds: 100),
                  child: Padding(
                    padding: kAttachmentGalleryPageInsets,
                    child: TextFieldDone(
                      focusNode: _commentsFocus,
                      child: TrimTextOnFocusLost(
                        focusNode: _commentsFocus,
                        controller: controller.commentsController,
                        child: TextField(
                          focusNode: _commentsFocus,
                          controller: controller.commentsController,
                          onChanged: controller.onCommentsChanged,
                          minLines: 1,
                          maxLines: 5,
                          keyboardType: TextInputType.multiline,
                          textInputAction: TextInputAction.newline,
                          textCapitalization: TextCapitalization.sentences,
                          decoration: InputDecoration(
                            floatingLabelBehavior: FloatingLabelBehavior.never,
                            labelText:
                                AppLocalizations.of(context)!.addAComment,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildShareIcon() {
    if (widget.attachments.value.isEmpty) {
      return const SizedBox();
    }

    return ValueListenableBuilder(
      valueListenable: controllers[_pageIndex].editAttachment,
      builder: (BuildContext context, Attachment? value, Widget? child) =>
          value == null
              ? const SizedBox()
              : IconButton(
                  onPressed: _canBeShared(value) ? () => _share(value) : null,
                  icon: const Icon(Icons.share),
                ),
    );
  }

  Widget _buildDoodleIcon() {
    if (widget.attachments.value.isEmpty) {
      return const SizedBox();
    }

    return IconButton(
      onPressed: () =>
          _isShowingDoodleEditor.value = !_isShowingDoodleEditor.value,
      icon: const Icon(Icons.draw),
    );
  }

  bool _canBeShared(Attachment attachment) {
    return attachment.isDownloaded?.value ?? false;
  }

  Future<void> _share(Attachment attachment) async {
    final path = await AttachmentUtils().getAbsolutePath(attachment.path!);

    if (path == null) {
      return;
    }
    await Share.shareXFiles([XFile(path)]);
  }
}
