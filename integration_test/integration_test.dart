import 'package:bitacora/dev/perf/perf_stats.dart';
import 'package:bitacora/dev/perf/perf_test_config.dart';
import 'package:bitacora/dev/perf/perf_test_monitor.dart';
import 'package:flutter_test/flutter_test.dart';

void integrationTestWidget(String description, WidgetTesterCallback callback) {
  testWidgets(description, (tester) async {
    if (!PerfTestConfig.isPerfTest ||
        (PerfTestConfig.runCount == 1 && !PerfTestConfig.isAbTest)) {
      return callback(tester);
    }

    const runCount =
        PerfTestConfig.runCount * (PerfTestConfig.isAbTest ? 2 : 1);

    // First run is ignored due to high test variability.
    _print('First run is ignored...');
    await callback(tester);

    for (var i = 0; i < runCount; i++) {
      _print(
        '`${PerfTestConfig.perfTest}`'
        ' performance test run ${i + 1} of $runCount',
      );
      if (PerfTestConfig.isAbTest) {
        PerfTestConfig.isAbTestControl = i % 2 == 0;
        _print('${PerfTestConfig.isAbTestControl ? 'Control' : 'Test'} Run');
      }
      await callback(tester);
    }

    _print('');
    _print('`${PerfTestConfig.perfTest}` performance test done.');
    _print('');

    final stats = PerfTestMonitor().stats;
    stats.removeAt(0);

    if (PerfTestConfig.isAbTest) {
      _printAbTestResults(stats);
    } else {
      PerfStats.average(stats).printSummary(_print, 'Average');
    }
  });
}

void _printAbTestResults(List<PerfStats> stats) {
  final controlResults = <PerfStats>[];
  final testResults = <PerfStats>[];
  for (var i = 0; i < stats.length; i++) {
    if (i % 2 == 0) {
      controlResults.add(stats[i]);
    } else {
      testResults.add(stats[i]);
    }
  }

  final control = PerfStats.average(controlResults);
  control.printSummary(_print, 'Control');
  final test = PerfStats.average(testResults);
  test.printSummary(_print, 'Test');

  final deltaStats = PerfStats.delta(
    control: control,
    test: test,
  );
  deltaStats.printSummary(_print, 'A|B Deltas');
}

void _print(Object o) {
  // ignore: avoid_print
  print(o);
}
