import 'package:bitacora/domain/common/value_object/color_value_object.dart';
import 'package:flutter/material.dart';

class TagColor extends NonNullableColorValueObject {
  const TagColor(super.value);

  @override
  bool isSameType(Object other) {
    return other is TagColor;
  }

  Color opaqueValue(BuildContext context) {
    return Color.lerp(
        Theme.of(context).brightness == Brightness.dark
            ? Colors.black
            : Colors.white,
        value,
        0.6)!;
  }
}
