import 'package:bitacora/domain/common/repository_query_context.dart';

abstract class ProductFieldsBuilder {
  ProductFieldsBuilder remoteId();

  ProductFieldsBuilder productUuid();

  ProductFieldsBuilder productDescription();

  ProductFieldsBuilder quantity();

  ProductFieldsBuilder mode();

  ProductFieldsBuilder expirationDate();

  ProductFieldsBuilder organization(Fields fields);

  ProductFieldsBuilder users(Fields fields);

  Fields build();
}
