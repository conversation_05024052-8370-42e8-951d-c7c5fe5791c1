import 'package:bitacora/domain/common/value_object/value_object.dart';
import 'package:bitacora/domain/report_template/report_template.dart';

enum OrganizationActivePlanValue {
  free(0, 'FREE'),
  pro(1, 'PRO'),
  advanced(2, 'ADVANCED'),
  trial(3, 'ADVANCED'),
  personal(4, 'PERSONAL');

  final int dbValue;
  final String displayValue;

  const OrganizationActivePlanValue(this.dbValue, this.displayValue);

  static OrganizationActivePlanValue fromApiValue(String value) {
    switch (value) {
      case 'FREE':
        return free;
      case 'PRO':
        return pro;
      case 'ADVANCED':
        return advanced;
      case 'ADVANCED_TRIAL':
        return trial;
      case 'PERSONAL':
        return personal;
    }
    return pro;
  }
}

class OrganizationActivePlan extends ValueObject<OrganizationActivePlanValue> {
  static const OrganizationActivePlan free =
      OrganizationActivePlan(OrganizationActivePlanValue.free);
  static const OrganizationActivePlan pro =
      OrganizationActivePlan(OrganizationActivePlanValue.pro);
  static const OrganizationActivePlan advanced =
      OrganizationActivePlan(OrganizationActivePlanValue.advanced);
  static const OrganizationActivePlan trial =
      OrganizationActivePlan(OrganizationActivePlanValue.trial);
  static const OrganizationActivePlan personal =
      OrganizationActivePlan(OrganizationActivePlanValue.personal);
  static const List<OrganizationActivePlan> _values = [
    free,
    pro,
    advanced,
    trial,
    personal
  ];

  const OrganizationActivePlan(super.value);

  factory OrganizationActivePlan.fromDbValue(int dbValue) => _values[dbValue];

  factory OrganizationActivePlan.fromApiValue(String apiValue) =>
      OrganizationActivePlan(
          OrganizationActivePlanValue.fromApiValue(apiValue));

  @override
  bool isSameType(Object other) {
    return other is OrganizationActivePlan;
  }

  @override
  int get dbValue => value.dbValue;

  @override
  int get apiValue => dbValue;

  bool get isShowFreePlanIndicatorInAppDrawer =>
      value == OrganizationActivePlanValue.free ||
      value == OrganizationActivePlanValue.trial;

  bool get isUploadEnabled => value != OrganizationActivePlanValue.free;

  bool get isReportEnabled => value != OrganizationActivePlanValue.free;

  bool get isUserTrackingEnabled => value != OrganizationActivePlanValue.free;

  bool get isMultipleUsersAllowed => value != OrganizationActivePlanValue.free;

  bool get isEntrySignatureAllowed =>
      value == OrganizationActivePlanValue.trial ||
      value == OrganizationActivePlanValue.advanced;

  List<ReportTemplate> filterAvailableReportTemplates(
      List<ReportTemplate> templates) {
    switch (value) {
      case OrganizationActivePlanValue.free:
        return <ReportTemplate>[];
      case OrganizationActivePlanValue.pro:
      case OrganizationActivePlanValue.personal:
        return templates
            .where((e) => [
                  kReportTemplateDefaultOptionBase,
                  kReportTemplateDefaultOptionCompact
                ].contains(e.option!.value))
            .toList();
      case OrganizationActivePlanValue.trial:
      case OrganizationActivePlanValue.advanced:
      return templates;
    }
  }
}
