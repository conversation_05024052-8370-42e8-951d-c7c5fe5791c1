import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/util/time_utils.dart';
import 'package:test/test.dart';

void main() {
  group('Time Utils test', () {
    test('getAudioTimestamp', () {
      const duration = Duration(minutes: 10, seconds: 50);

      final audioTimestamp = getTimerTimestamp(duration);

      expect(audioTimestamp, '10:50');
    });

    test('getDateTimeFromLogTime', () {
      const logTime = LogTime(0908);

      final dateTime = getDateTimeFromLogTime(logTime);

      expect(dateTime, DateTime(0, 0, 0, logTime.hour, logTime.minute));
    });
  });
}
