import 'dart:async';

import 'package:bitacora/application/notification/daily_reminder/daily_reminder_notification_scheduler.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/util/background_work/background_provider.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/workmanager/workmanager_runnable.dart';

class DailyReminderNotificationPeriodicTask implements WorkmanagerRunnable {
  factory DailyReminderNotificationPeriodicTask() =>
      inject(() => const DailyReminderNotificationPeriodicTask._());

  const DailyReminderNotificationPeriodicTask._();

  @override
  Future<bool> run(BackgroundContext context) async {
    logger.i('daily-reminder-monitor Starting');
    final session = await context.read<Future<Session?>>();

    if (session == null || !session.isValid) {
      logger.i('daily-reminder-monitor Ending. Invalid session.');
      return true;
    }

    await DailyReminderNotificationScheduler()
        .maybeCreateTomorrowNotification();

    logger.i('daily-reminder-monitor Ending.');
    return true;
  }
}
