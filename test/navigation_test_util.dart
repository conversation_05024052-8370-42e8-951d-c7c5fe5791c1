import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'test_util.dart';

Future<void> testNavigation({
  required WidgetTester tester,
  required List<Provider> providers,
  required BuildContextCallback onNavigate,
  required String expectedPushedRouteName,
  bool Function(Object? pushedRouteArguments) onTestArguments = _alwaysTrue,
  bool isFailTest = false,
}) async {
  RouteSettings? pushedRouteSettings;
  await tester.pumpWidget(testAppForContextCallback(
    providers: providers,
    onGenerateRoute: (settings) {
      pushedRouteSettings = settings;
      return MaterialPageRoute(builder: (_) => Container());
    },
    callback: onNavigate,
  ));
  await tester.tap(find.text('Run Test'));

  if (isFailTest) {
    expect(pushedRouteSettings, null);
  } else {
    expect(pushedRouteSettings!.name, expectedPushedRouteName);
    expect(onTestArguments(pushedRouteSettings!.arguments), true);
  }
}

bool _alwaysTrue(Object? _) => true;
