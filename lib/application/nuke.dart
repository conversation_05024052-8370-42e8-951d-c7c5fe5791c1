import 'package:bitacora/application/notification/awesome_notifications_action_handler.dart';
import 'package:bitacora/application/restart.dart';
import 'package:bitacora/domain/auth/auth_repository.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/util/awesome_notifications/awesome_notifications_utils.dart';
import 'package:bitacora/util/context_snapshot/context_snapshot.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/restart_widget.dart';
import 'package:bitacora/util/storage/storage_utils.dart';

class Nuke {
  factory Nuke() => inject(() => const Nuke._());

  const Nuke._();

  Future<void> nuke(
    NukeContextSnapshot contextSnapshot, [
    RestartData? restartData,
  ]) async {
    await contextSnapshot.read<Repository>().nuke();
    await contextSnapshot.read<AuthRepository>().nuke();
    await StorageUtils().deleteDirectory(StorageSubdirectory.attachments);
    await StorageUtils().deleteDirectory(StorageSubdirectory.reports);
    await StorageUtils().deleteDirectory(StorageSubdirectory.resources);
    await StorageUtils().deleteDirectory(StorageSubdirectory.avatars);
    AwesomeNotificationsUtils().nuke();
    AwesomeNotificationsActionHandler().nuke();
    Restart().restartForState(
        contextSnapshot.read<RestartWidgetState>(), restartData);
  }
}

class NukeContextSnapshot extends ContextSnapshot {
  NukeContextSnapshot(super.context);

  @override
  List<ValueShot> get valueShots => [
        ValueShot.provider<Repository>(),
        ValueShot.provider<AuthRepository>(),
        ValueShot.state<RestartWidgetState>(),
      ];
}
