import 'package:bitacora/util/context_snapshot/value_shot.dart';
import 'package:flutter/material.dart';

export 'package:bitacora/util/context_snapshot/value_shot.dart';

abstract class ContextSnapshot {
  final Map<Type, dynamic> _valueSnapshots = {};

  List<ValueShot> get valueShots;

  ContextSnapshot(BuildContext context) {
    if (valueShots.isEmpty) {
      throw 'Context snapshot must have at least one valueShot';
    }

    for (final valueShot in valueShots) {
      _takeValueSnapshotFromBuildContext(context, valueShot);
    }
  }

  void _takeValueSnapshotFromBuildContext(
      BuildContext context, ValueShot valueShot) {
    _valueSnapshots[valueShot.type] = valueShot.shot(context);
  }

  ContextSnapshot.fromSnapshot(ContextSnapshot snapshot) {
    if (valueShots.isEmpty) {
      throw 'Context snapshot must have at least one valueShot';
    }

    for (final valueShot in valueShots) {
      _takeValueSnapshotFromSnapshot(snapshot, valueShot);
    }
  }

  void _takeValueSnapshotFromSnapshot(
      ContextSnapshot snapshot, ValueShot valueShot) {
    _valueSnapshots[valueShot.type] = snapshot._valueSnapshots[valueShot.type];
  }

  T read<T>() {
    final value = _valueSnapshots[T];

    if (value == null) {
      throw 'The value of type $T is not found in the context snapshot';
    }

    return value;
  }

  T? readNullable<T>() {
    return _valueSnapshots[T];
  }
}
