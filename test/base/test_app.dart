import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

enum ScaffoldParam { body, appBar, floatingActionButton }

class TestApp extends StatelessWidget {
  final Widget? child;
  final Widget? appBar;
  final RouteFactory? onGenerateRoute;

  const TestApp({super.key, this.child, this.appBar, this.onGenerateRoute});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Localizations(
        locale: const Locale('en'),
        delegates: AppLocalizations.localizationsDelegates,
        child: Scaffold(
          body: child,
          appBar: appBar != null
              ? PreferredSize(
                  preferredSize: const Size.fromHeight(50.0),
                  child: appBar!,
                )
              : null,
        ),
      ),
      theme: buildLightThemeData(bitacoraGreen),
      onGenerateRoute: onGenerateRoute,
    );
  }
}
