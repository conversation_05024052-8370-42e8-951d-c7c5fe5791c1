import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/infrastructure/attachment/pending_attachment_upload_repository_query.dart';
import 'package:bitacora/presentation/user_settings/has_pending_outgoing_mutations_repository_query.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/clock.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SyncUtils {
  factory SyncUtils() => inject(() => const SyncUtils._());

  const SyncUtils._();

  Future<void> saveLastSyncTimeToNow() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt(
      SharedPreferencesKeys.lastSyncTime,
      Clock().now().millisecondsSinceEpoch,
    );
  }

  Future<DateTime?> getLastSyncTime() async {
    final prefs = await SharedPreferences.getInstance();
    final lastSyncTime = prefs.getInt(
      SharedPreferencesKeys.lastSyncTime,
    );

    if (lastSyncTime == null) {
      return null;
    }

    return DateTime.fromMillisecondsSinceEpoch(lastSyncTime);
  }

  Future<bool> hasPendingSync(Repository db) async {
    if (await hasPendingFullSync() || await hasPendingSuperSync()) {
      logger.i('sync:hasPending Pending due to pref');
      return true;
    }

    if (await _hasPendingOutgoingMutation(db)) {
      logger.i('sync:hasPending Pending due to outgoing mutation');
      return true;
    }

    if (await hasPendingAttachmentUpload(db)) {
      logger.i('sync:hasPending Pending due to attachment upload');
      return true;
    }

    logger.i('sync:hasPending No pending sync.');
    return false;
  }

  Future<SyncTriggerMode> getPendingSyncTriggerMode() async {
    if (await hasPendingSuperSync()) {
      return SyncTriggerMode.superSync;
    } else if (await hasPendingFullSync()) {
      return SyncTriggerMode.fullSync;
    }
    return SyncTriggerMode.normal;
  }

  Future<bool> hasPendingFullSync() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SharedPreferencesKeys.hasPendingSync) ?? false;
  }

  Future<bool> hasPendingSuperSync() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(SharedPreferencesKeys.hasPendingSuperSync) ?? false;
  }

  Future<bool> _hasPendingOutgoingMutation(Repository db) {
    return db.query(
      const HasPendingOutgoingMutationsRepositoryQuery(ignoreRetryLimit: false),
    );
  }

  Future<bool> hasPendingAttachmentUpload(
    Repository db, {
    bool ignoreRetryLimit = false,
  }) async {
    final nextAttachment = await db.query(
      PendingAttachmentUploadRepositoryQuery(
        ignoreRetryLimit: ignoreRetryLimit,
      ),
    );

    return nextAttachment != null;
  }
}
