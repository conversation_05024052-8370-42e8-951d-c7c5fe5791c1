import 'package:bitacora/domain/attachment/doodle_drawn_line.dart';
import 'package:flutter/material.dart';

const _kLineBaseWidth = 4.0;

class DoodleSketcher extends CustomPainter {
  final double canvasScale;
  final List<DoodleDrawnLine> lines;
  final bool scaleLines;

  const DoodleSketcher({
    required this.canvasScale,
    this.lines = const [],
    this.scaleLines = false,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..isAntiAlias = true
      ..strokeCap = StrokeCap.round
      ..strokeJoin = StrokeJoin.round
      ..style = PaintingStyle.stroke;

    for (final line in lines) {
      paint.color = line.color;
      paint.strokeWidth =
          _kLineBaseWidth * (!scaleLines ? 1 : canvasScale / line.scale);

      if (line.points.length == 1) {
        _drawPoint(canvas, paint, line);
      } else {
        _drawBezierCurve(canvas, paint, line);
      }
    }
  }

  void _drawBezierCurve(Canvas canvas, Paint paint, DoodleDrawnLine line) {
    final path = Path();
    path.moveTo(line.points.first!.dx, line.points.first!.dy);
    for (var i = 0; i < line.points.length - 2; i += 2) {
      if (line.points[i] != null &&
          line.points[i + 1] != null &&
          line.points[i + 2] != null) {
        path.cubicTo(
          line.points[i]!.dx,
          line.points[i]!.dy,
          line.points[i + 1]!.dx,
          line.points[i + 1]!.dy,
          line.points[i + 2]!.dx,
          line.points[i + 2]!.dy,
        );
      }
    }
    canvas.drawPath(path, paint);
  }

  void _drawPoint(Canvas canvas, Paint paint, DoodleDrawnLine line) {
    canvas.drawLine(line.points.first!, line.points.first!, paint);
  }

  @override
  bool shouldRepaint(DoodleSketcher oldDelegate) {
    return true;
  }
}
