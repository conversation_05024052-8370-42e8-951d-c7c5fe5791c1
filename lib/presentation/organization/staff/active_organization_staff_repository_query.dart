import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/user/user.dart';

class OrganizationStaffRepositoryQuery extends RepositoryQuery<List<User>> {
  const OrganizationStaffRepositoryQuery();

  @override
  Future<List<User>> run(RepositoryQueryContext context) {
    return context.db.user.getOrgStaff(context);
  }

  @override
  Fields fields(Repository db) => db.user.fieldsBuilder
      .name()
      .email()
      .person(
        db.person.fieldsBuilder
            .firstName()
            .lastName()
            .birthDate()
            .detail(
              db.personDetail.fieldsBuilder
                  .avatar(
                    db.avatar.fieldsBuilder
                        .s3Key()
                        .path()
                        .isDownloaded()
                        .build(),
                  )
                  .phones(db.phone.fieldsBuilder.type().number().build())
                  .emails(db.email.fieldsBuilder.type().value().build())
                  .addresses(db.address.fieldsBuilder.type().value().build())
                  .company()
                  .area()
                  .description()
                  .build(),
            )
            .build(),
      )
      .build();
}
