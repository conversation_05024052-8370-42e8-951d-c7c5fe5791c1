import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/util/date_utils.dart';

class CustomFieldMetadataApiTranslator
    implements ModelTranslator<CustomFieldMetadata> {
  const CustomFieldMetadataApiTranslator();

  @override
  CustomFieldMetadata fromMap(Map<String, dynamic> data) {
    final type = CustomFieldTypeValue.fromApiValue(data['custom_field_type']);
    final allowedValue = (type == CustomFieldTypeValue.select ||
                type == CustomFieldTypeValue.radio) &&
            data['value'] is Map
        ? CustomFieldAllowedValue(remoteId: RemoteId(data['value']['id']))
        : null;
    return CustomFieldMetadata(
      remoteId: RemoteId(data['id']),
      value: CustomFieldMetadataValue.fromApiValue(
        data['value'],
        type,
      ),
      fieldName: CustomFieldMetadataFieldName(data['field_name']),
      project: data['project_id'] == null
          ? null
          : Project(
              remoteId: RemoteId(data['project_id']),
              name: ProjectName(data['project_name']),
              organization:
                  Organization(remoteId: RemoteId(data['organization_id'])),
            ),
      user: data['user_id'] == null
          ? null
          : User(remoteId: RemoteId(data['user_id'])),
      customField: CustomField(remoteId: RemoteId(data['custom_field_id'])),
      templatelog: Templatelog(remoteId: RemoteId(data['templatelog_id'])),
      allowedValue: allowedValue,
      updatedAt:
          CustomFieldMetadataUpdatedAt(getDateTimeFromApi(data['updated_at'])),
      createdAt:
          CustomFieldMetadataCreatedAt(getDateTimeFromApi(data['created_at'])),
    );
  }

  @override
  Map<String, dynamic> toMap(CustomFieldMetadata model) {
    final value = model.allowedValue != null
        ? {
            'id': model.allowedValue!.remoteId!.apiValue,
            'label': model.allowedValue!.label!.apiValue,
            'value': model.allowedValue!.value!.apiValue,
            'parent_id': model.allowedValue!.parent?.id?.apiValue,
          }
        : model.customField!.type != CustomFieldType.time || model.value == null
            ? model.value?.apiValue
            : model.value!.value.toString().padLeft(4, '0');

    final map = {
      /// FIXME: It should not be necessary to specify the time type if the server accepts it as an integer.
      'value': value,
      'project_name': model.project?.name?.apiValue,
      'user_id': model.user?.remoteId?.apiValue,
      'custom_field_id': model.customField?.remoteId?.apiValue,
      'field_name': model.fieldName?.apiValue,
    };

    if (model.remoteId!.apiValue != null) {
      map['id'] = model.remoteId!.apiValue;
    }

    return map;
  }
}
