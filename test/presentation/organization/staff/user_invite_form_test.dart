import 'package:bitacora/presentation/organization/staff/user_invite_form.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../mocktail_fallback_values.dart';
import '_robots/user_invite_form_test_robot.dart';

void main() {
  group('$UserInviteForm tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Validate email format', (tester) async {
      const email = 'josephmail.com';
      final robot = UserInviteFormTestRobot(tester);
      await robot.pumpWidget();

      await robot.enterEmail(email);
      await robot.invite();

      robot.verifyInvalidEmailMessage();
    });

    testWidgets('Invite', (tester) async {
      const email = '<EMAIL>';
      final robot = UserInviteFormTestRobot(tester);
      await robot.pumpWidget();

      await robot.enterEmail(email);
      await robot.invite();

      robot.verifyInvite(email);
    });
  });
}
