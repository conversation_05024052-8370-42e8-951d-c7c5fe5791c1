import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/worklog/worklog_form_controller.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../mocktail_fallback_values.dart';
import '../entry_form_controller_robot.dart';

void main() {
  group('$WorklogFormController tests', () {
    setUpAll(() {
      MocktailFallbackValues.ensureInitialized();
    });

    testWidgets('Verify options', (tester) async {
      final props = EntryFormProps(
          inputEntry: mockEntry(withId: true, extension: mockWorklog()));
      final robot = EntryFormControllerRobot(
        tester,
        props: props,
        controllerType: WorklogFormController,
      );

      await robot.pumpWidget();

      robot.verifyOptions();
    });

    testWidgets('Read worklogEntry', (tester) async {
      final props = EntryFormProps(
          inputEntry: mockEntry(withId: true, extension: mockWorklog()));
      final robot = EntryFormControllerRobot(
        tester,
        props: props,
        controllerType: WorklogFormController,
      );

      await robot.pumpWidget();

      await robot.verifyRead(props.inputEntry!);
    });
  });
}
