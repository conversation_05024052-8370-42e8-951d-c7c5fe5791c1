import 'dart:io';

import 'package:bitacora/domain/common/value_object/log_time.dart';
import 'package:datetime_picker_formfield_new/datetime_picker_formfield.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:intl/intl.dart';

final DateFormat kTimeOfDayDateFormat = DateFormat.jm(Platform.localeName);

String getFormattedHours(int? minutes, {String suffix = ' hrs'}) {
  if (minutes == null) {
    return '';
  }

  // FIXME: localize suffix, maybe use pattern
  final hours = minutes / 60;

  if (hours == 0) {
    return '${hours.toInt()}$suffix';
  }

  return '${hours.toStringAsFixed(2)}$suffix';
}

String getTimerTimestamp(Duration duration) {
  return '${duration.inMinutes.toString().padLeft(2, '0')}'
      ':${(duration.inSeconds % 60).toString().padLeft(2, '0')}';
}

DateTime getDateTimeFromLogTime(LogTime logTime) {
  return DateTime(0, 0, 0, logTime.hour, logTime.minute);
}

String getFormattedTimeRange(int? startMinutes, int? endMinutes) {
  if (startMinutes != null && endMinutes != null) {
    return '${getFormattedTimeOfDay(startMinutes)} -> '
        '${getFormattedTimeOfDay(endMinutes)}';
  } else if (startMinutes != null) {
    return '${getFormattedTimeOfDay(startMinutes)} ->';
  } else if (endMinutes != null) {
    return '<- ${getFormattedTimeOfDay(endMinutes)}';
  }
  return '';
}

int getMinutesFromFormattedTimeRange(String time) {
  final date = kTimeOfDayDateFormat.parse(time);
  return date.hour * 60 + date.minute;
}

String getFormattedTimeOfDay(int minutes) {
  return kTimeOfDayDateFormat
      .format(DateTime(0, 0, 0, minutes ~/ 60, minutes % 60));
}

Future<DateTime?> pickTime(BuildContext context, DateTime? currentValue) async {
  final initialDateTime = currentValue ?? DateTime.now();
  final initialTime = TimeOfDay.fromDateTime(initialDateTime);

  if (isCupertino(context)) {
    return showCupertinoTimePicker(context, initialDateTime);
  }
  final selectedTime = await showTimePicker(
    context: context,
    initialTime: initialTime,
  );
  return DateTimeField.convert(selectedTime);
}

Future<DateTime> showCupertinoTimePicker(
    BuildContext context, DateTime initialDateTime) async {
  var pickedDate = initialDateTime;
  await showPlatformModalSheet(
    context: context,
    builder: (context) {
      return SizedBox(
        height: MediaQuery.sizeOf(context).height / 3,
        child: CupertinoTheme(
          data: CupertinoThemeData(brightness: Theme.of(context).brightness),
          child: Container(
            color: Theme.of(context).colorScheme.surface,
            child: CupertinoDatePicker(
              mode: CupertinoDatePickerMode.time,
              backgroundColor: Theme.of(context).colorScheme.surface,
              initialDateTime: initialDateTime,
              onDateTimeChanged: (DateTime date) {
                pickedDate = date;
              },
            ),
          ),
        ),
      );
    },
  );
  return pickedDate;
}
