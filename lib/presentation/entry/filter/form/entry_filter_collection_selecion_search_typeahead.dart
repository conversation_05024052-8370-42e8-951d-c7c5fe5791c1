import 'package:bitacora/presentation/entry/filter/form/entry_filter_form_mode.dart';
import 'package:bitacora/util/collection_selection/collection_selection_search_typeahead.dart';
import 'package:bitacora/util/collection_selection/collection_selection_search_typeahead_controller.dart';
import 'package:flutter/material.dart';

class EntryFilterCollectionSelectionSearchTypeahead<T> extends StatefulWidget {
  final CollectionSelectionSearchTypeaheadController<T> controller;
  final EntryFilterFormMode mode;

  const EntryFilterCollectionSelectionSearchTypeahead({
    super.key,
    required this.controller,
    required this.mode,
  });

  @override
  State<EntryFilterCollectionSelectionSearchTypeahead<T>> createState() =>
      _EntryFilterCollectionSelectionSearchTypeaheadState<T>();
}

class _EntryFilterCollectionSelectionSearchTypeaheadState<T>
    extends State<EntryFilterCollectionSelectionSearchTypeahead<T>> {
  late bool _isCollapsed;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _isCollapsed =
        widget.mode.isCollapsable && widget.controller.collection.value.isEmpty;
    widget.controller.isFocused.addListener(_onFocusChanged);
    widget.controller.collection.addListener(_onCollectionChanged);
  }

  @override
  void dispose() {
    widget.controller.isFocused.removeListener(_onFocusChanged);
    widget.controller.collection.removeListener(_onCollectionChanged);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<List<T>>(
      valueListenable: widget.controller.collection,
      builder: (context, collection, _) {
        final isCollectionEmpty = widget.controller.collection.value.isEmpty;
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (widget.mode.isCollapsable) ...[
              InkWell(
                onTap: _expand,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      widget.controller.textFieldHint,
                      style: Theme.of(context).textTheme.bodySmall!.copyWith(
                            color: !isCollectionEmpty
                                ? Theme.of(context).colorScheme.primary
                                : null,
                          ),
                    ),
                    IgnorePointer(
                      ignoring: _isCollapsed,
                      child: IconButton(
                        onPressed: _collapse,
                        constraints: const BoxConstraints(),
                        padding: EdgeInsets.zero,
                        icon: Icon(
                          _isCollapsed ? Icons.add : Icons.clear,
                          size: 18,
                          color: !isCollectionEmpty
                              ? Theme.of(context).colorScheme.primary
                              : Colors.grey,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (!_isCollapsed) ...[
              Container(
                margin: const EdgeInsets.symmetric(vertical: 4.0),
                child: CollectionSelectionSearchTypeahead<T>(
                  controller: widget.controller,
                  showLabelText: !widget.mode.isCollapsable,
                  hidesBorderWhenNotFocus: widget.mode.isCollapsable,
                ),
              ),
              if (widget.mode.isCollapsable) const SizedBox(height: 8),
            ]
          ],
        );
      },
    );
  }

  void _focus() {
    setState(() {
      _isFocused = true;
    });
    widget.controller.focusNode.requestFocus();
  }

  void _unFocus() {
    setState(() {
      _isFocused = false;
    });
    widget.controller.focusNode.unfocus();
  }

  void _onFocusChanged() {
    _isFocused = widget.controller.isFocused.value;

    if (!_isFocused &&
        widget.mode.isCollapsable &&
        widget.controller.collection.value.isEmpty) {
      _isCollapsed = true;
    }

    setState(() {});
  }

  void _expand() {
    if (!_isCollapsed) {
      return;
    }

    _isCollapsed = false;
    _focus();

    setState(() {});
  }

  void _collapse() {
    if (_isCollapsed) {
      return;
    }

    _isCollapsed = true;
    widget.controller.collection.value = [];
    _unFocus();

    setState(() {});
  }

  void _onCollectionChanged() {
    if (!widget.mode.isCollapsable) {
      return;
    }

    if (widget.controller.collection.value.isEmpty && !_isCollapsed) {
      setState(() {
        _isCollapsed = true;
      });
    }
  }
}
