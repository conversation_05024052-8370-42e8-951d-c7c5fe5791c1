import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/attachment/attachment_fields_builder.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/entry/entry.dart';

const kAttachmentMaxTransferAttempts = 5;

abstract class AttachmentRepository<C extends RepositoryQueryContext,
        F extends AttachmentFieldsBuilder>
    extends RepositoryTable<Attachment, C, F> {
  Future<List<Attachment>> findAll(C context, LocalId entryId);

  Future<bool> hasPendingUploads(C context);

  Future<Attachment?> findNextPendingUpload(C context, bool ignoreRetryLimit);

  Future<List<Attachment>> findPendingUpload(C context);

  @override
  Future<LocalId?> save(C context, Attachment model,
      {bool requestSync = false, bool persistTransferDetails = true});

  Future<void> saveAll(C context, Mutation<Entry> entryMutation);

  Future<void> deleteAll(C context, LocalId entryId);

  void download(Attachment attachment);

  Stream<Attachment> get downloadRequests;

  Future<void> saveTransferDetails(C context, Attachment attachment);

  Future<List<Attachment>> findMissing(C context);
}
