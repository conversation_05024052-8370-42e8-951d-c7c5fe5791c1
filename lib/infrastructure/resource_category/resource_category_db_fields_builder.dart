import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/resource_category/resource_category.dart';
import 'package:bitacora/domain/resource_category/resource_category_fields_builder.dart';
import 'package:bitacora/domain/resource_category/value/resource_category_name.dart';
import 'package:bitacora/domain/resource_category/value/resource_category_order.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_db_contract.dart';

class ResourceCategoryDbFieldsBuilder extends DbFieldsBuilder
    implements ResourceCategoryFieldsBuilder {
  ResourceCategoryDbFieldsBuilder() {
    _id();
  }

  ResourceCategoryDbContract get contract => const ResourceCategoryDbContract();

  ResourceCategoryDbFieldsBuilder _id() {
    addField(
      ResourceCategoryField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  ResourceCategoryDbFieldsBuilder remoteId() {
    addField(
      ResourceCategoryField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  ResourceCategoryDbFieldsBuilder name() {
    addField(
      ResourceCategoryField.name,
      DbField(
        column: contract.name,
        valueBuilder: (v) => ResourceCategoryName(v),
      ),
    );
    return this;
  }

  @override
  ResourceCategoryFieldsBuilder order() {
    addField(
      ResourceCategoryField.order,
      DbField(
        column: contract.order,
        valueBuilder: (v) => ResourceCategoryOrder(v),
      ),
    );
    return this;
  }

  @override
  ResourceCategoryFieldsBuilder parent(Fields fields) {
    addField(
      ResourceCategoryField.parent,
      DbField(
        key: ResourceCategoryField.parent,
        column: contract.parentId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) => nestedContext
            .db.resourceCategory
            .find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }

  @override
  ResourceCategoryFieldsBuilder organization(Fields fields) {
    addField(
      ResourceCategoryField.organization,
      DbField(
        key: ResourceCategoryField.organization,
        column: contract.organizationId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.organization.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
