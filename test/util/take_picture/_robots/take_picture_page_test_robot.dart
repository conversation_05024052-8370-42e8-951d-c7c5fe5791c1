import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/camera/camera_utils.dart';
import 'package:bitacora/util/clipboard.dart';
import 'package:bitacora/util/image/image_utils.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/take_picture/take_picture_page.dart';
import 'package:file/memory.dart';
import 'package:flutter/material.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:native_device_orientation/native_device_orientation.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../base/base_robot.dart';
import '../../camera/mocks.dart';
import '../../native_device_orientation/mocks.dart';

class TakePicturePageTestRobot extends BaseRobot {
  late FileSystem _fileSystem;
  late CameraUtils _cameraUtils;
  late CameraController _controller;
  late List<CameraDescription> _cameras;

  TakePicturePageTestRobot(
    super.tester, {
    FileSystem? fileSystem,
    CameraUtils? cameraUtils,
    CameraController? controller,
    List<CameraDescription>? cameras,
  }) {
    _fileSystem = fileSystem ?? MemoryFileSystem.test();
    _controller = controller ?? mockCameraController();
    _cameraUtils =
        cameraUtils ?? mockCameraUtils(cameraController: _controller);
    _cameras = cameras ?? List.generate(2, (i) => mockCameraDescription(i));
  }

  @override
  Future<void> pumpWidget() async {
    await withInjected4<CameraUtils, CameraController, FileSystem,
        NativeDeviceOrientationCommunicator>(
      _cameraUtils,
      _controller,
      _fileSystem,
      mockNativeDeviceOrientationCommunicator(),
      () => tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: TakePicturePage(cameras: _cameras),
          ),
        ),
      ),
    );
  }

  void verifyLoading() {
    expect(find.byType(PlatformCircularProgressIndicator), findsOneWidget);
  }

  void verifyInitialization() {
    verify(() => _controller.dispose()).called(1);
    verify(() => _controller.addListener(any())).called(1);
    verify(() => _controller.initialize()).called(1);
    verify(() => _controller.getMaxZoomLevel()).called(1);
    verify(() => _controller.getMinZoomLevel()).called(1);
  }

  void verifyInitializedUi() {
    expect(find.byType(PlatformCircularProgressIndicator), findsNothing);
    expect(find.byIcon(Icons.flash_off), findsOneWidget);
    expect(find.byIcon(Icons.flip_camera_ios), findsOneWidget);
    expect(find.byIcon(Icons.camera_alt), findsOneWidget);
    expect(find.byType(CameraPreview), findsOneWidget);
  }

  Future<void> toggleFlashMode(
      {FlashMode from = FlashMode.off, FlashMode to = FlashMode.auto}) async {
    await withInjected2<CameraUtils, CameraController>(
        _cameraUtils, _controller, () async {
      await tap(find.byIcon(_getFlashIcon(from)));
      await tester.pumpAndSettle();
    });
  }

  Future<void> verifyFlashMode({FlashMode to = FlashMode.auto}) async {
    final prefs = await SharedPreferences.getInstance();
    verify(() => _controller.setFlashMode(to)).called(1);
    expect(
      prefs.getInt(SharedPreferencesKeys.defaultFlashMode),
      to.index,
    );
  }

  Future<void> toggleCamera() async {
    await withInjected2<CameraUtils, CameraController>(_cameraUtils,
        _controller, () => tap(find.byIcon(Icons.flip_camera_ios)));
  }

  Future<void> verifyToggleCamera({int from = 0, int to = 1}) async {
    final prefs = await SharedPreferences.getInstance();
    verifyInitializedUi();
    final newDescription = verify(() => _cameraUtils.buildCameraController(
          captureAny(),
          any(),
          enableAudio: false,
          imageFormatGroup: ImageFormatGroup.jpeg,
        )).captured.last;
    expect(newDescription, _cameras[to]);
    expect(prefs.getInt(SharedPreferencesKeys.defaultCameraLens), to);
  }

  Future<void> takePicture() async {
    await withInjected3<CameraUtils, CameraController,
        NativeDeviceOrientationCommunicator>(
      _cameraUtils,
      _controller,
      mockNativeDeviceOrientationCommunicator(),
      () async {
        await tap(find.byIcon(Icons.camera_alt));
      },
    );
    await tester.pumpAndSettle();
  }

  void verifyTakePicture() {
    verify(() => _controller.takePicture()).called(1);
    expect(find.byType(Image), findsOneWidget);
    expect(find.byIcon(Icons.rotate_90_degrees_ccw), findsOneWidget);
    expect(find.byIcon(Icons.delete), findsOneWidget);
    expect(find.byIcon(Icons.done), findsOneWidget);
  }

  Future<void> deletePicture() async {
    await withInjected<FileSystem>(
        _fileSystem, () => tap(find.byIcon(Icons.delete)));
    await tester.pumpAndSettle();
  }

  void verifyDeletePicture() {
    expect(find.byType(Image), findsNothing);
    expect(find.byType(Image), findsNothing);
    verifyInitializedUi();
  }

  Future<void> rotatePicture() async {
    await tap(find.byIcon(Icons.rotate_90_degrees_ccw));
    await tester.pumpAndSettle();
  }

  void verifyRotatePicture(ImageUtils imageUtils) {
    verify(() => imageUtils.decodeImage(any())).called(1);
    verify(() => imageUtils.copyRotate(any(), angle: -90)).called(1);
    verify(() => imageUtils.encodeJpg(any(), quality: 85)).called(1);
  }

  Future<void> approvePicture(ImageUtils imageUtils) async {
    await withInjected3<CameraController, NativeDeviceOrientationCommunicator,
        ImageUtils>(
      _controller,
      mockNativeDeviceOrientationCommunicator(),
      imageUtils,
      () => tap(find.byIcon(Icons.done)),
    );
    await tester.pumpAndSettle();
  }

  void verifyApprovePicture() {
    verify(() => _controller.dispose()).called(1);
  }
}

IconData _getFlashIcon(FlashMode flashMode) {
  switch (flashMode) {
    case FlashMode.off:
      return Icons.flash_off;
    case FlashMode.auto:
      return Icons.flash_auto;
    case FlashMode.always:
      return Icons.flash_on;
    case FlashMode.torch:
      return Icons.highlight;
  }
}
