import 'dart:io' as io;

import 'package:bitacora/util/attachment/attachment_utils.dart';
import 'package:bitacora/util/file_system.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path/path.dart' as path;

class AttachmentCompression {
  factory AttachmentCompression() =>
      inject(() => const AttachmentCompression._());

  const AttachmentCompression._();

  /// Tries to compress image.
  /// Returns null if:
  /// (a) compress library returns null
  /// (b) compress library throws
  /// (c) result is larger than original
  /// Otherwise returns a file in the destination dir with the same filename.
  Future<File?> maybeCompressImage(File file, String destDirPath, [quality = kJpegQuality]) async {
    final extension = path.extension(file.path).toLowerCase();
    final isPng = extension == '.png';
    final isHeic = extension == '.heic';

    final targetPath = path.join(
      destDirPath,
      '${path.basenameWithoutExtension(file.basename)}'
      '${isPng ? '.png' : '.jpg'}',
    );
    logger.i('attachment:compression Compressing ${file.path} -> $targetPath');

    // FIXME: target path replaces current attachment with same filename
    // i.e.
    // 1. Pick attachment /a/a.jpg
    // 2. Pick attachment /b/a.jpg
    // 3. Original attachment will be replaced as opposed to appended.

    try {
      final result = await FlutterImageCompressUtil().compressAndGetFile(
        file.absolute.path,
        targetPath,
        minWidth: 1080,
        minHeight: 1080,
        quality: quality,
        format: isPng ? CompressFormat.png : CompressFormat.jpeg,
        keepExif: true,
      );

      if (result == null) {
        logger.e('attachment:compression Null result when compressing.');
        return null;
      }

      final resultLength = await result.length();
      final fileLength = await file.length();
      if (fileLength < resultLength && !isHeic) {
        logger.i('attachment:compression Keeping original file.');
        await result.delete();
        return null;
      }

      return FileSystemInjector.get().file(result.path);
    } catch (e) {
      logger.e('attachment:compression Error compressing image $e');
      return null;
    }
  }
}

class FlutterImageCompressUtil {
  factory FlutterImageCompressUtil() =>
      inject(() => const FlutterImageCompressUtil._());

  const FlutterImageCompressUtil._();

  Future<io.File?> compressAndGetFile(
    String path,
    String targetPath, {
    int minWidth = 1920,
    int minHeight = 1080,
    int quality = 95,
    CompressFormat format = CompressFormat.jpeg,
    bool keepExif = false,
  }) async {
    final compressed = await FlutterImageCompress.compressAndGetFile(
      path,
      targetPath,
      minWidth: minWidth,
      minHeight: minHeight,
      quality: quality,
      format: format,
      keepExif: keepExif,
    );

    if (compressed == null) {
      return null;
    }

    return io.File(compressed.path);
  }
}
