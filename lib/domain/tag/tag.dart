import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/tag/value/tag_color.dart';
import 'package:bitacora/domain/tag/value/tag_name.dart';

export 'package:bitacora/domain/tag/value/tag_color.dart';
export 'package:bitacora/domain/tag/value/tag_name.dart';

class Tag extends Model {
  final TagName? name;
  final TagColor? color;

  final Organization? organization;

  const Tag({
    super.id,
    super.remoteId,
    this.name,
    this.color,
    this.organization,
  });

  Tag copyWith({Organization? organization}) {
    return Tag(
      id: id,
      remoteId: remoteId,
      name: name,
      color: color,
      organization: organization ?? this.organization,
    );
  }

  @override
  Map<Field, dynamic> get fields => {
        TagField.id: id,
        TagField.remoteId: remoteId,
        TagField.name: name,
        TagField.color: color,
        TagField.organization: organization,
      };
}

enum TagField with Field {
  id,
  remoteId,
  name,
  color,
  organization,
}

const tagNestedModelFields = {
  TagField.organization,
};
