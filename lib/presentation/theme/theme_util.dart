import 'package:flutter/material.dart';
import 'package:material_color_utilities/material_color_utilities.dart';

const _kSmallScreenWidthBreakpoint = 350; // [0, 350]
const _kLargeScreenWidthBreakpoint = 400; // [400, infinity]

const double kPageMargin = 16.0;
const EdgeInsets kPageInsets = EdgeInsets.all(kPageMargin);
const EdgeInsets kPageHorizontalInsets =
    EdgeInsets.symmetric(horizontal: kPageMargin);
const EdgeInsets kPageVerticalInsets =
    EdgeInsets.symmetric(vertical: kPageMargin);
const double kInputDecorationPadding = 8.0;
const double kCircularProgressIndicatorSize = 16.0;

const double _kBorderRadiusValue = 8.0;
const BorderRadius kBorderRadius =
    BorderRadius.all(Radius.circular(_kBorderRadiusValue));

ThemeData _wrapThemeData(ThemeData themeData) {
  return themeData.copyWith(
    appBarTheme: AppBarTheme(
        iconTheme: IconThemeData(color: themeData.colorScheme.primary)),
    listTileTheme: themeData.listTileTheme.copyWith(
      iconColor: themeData.colorScheme.onSurface,
    ),
    badgeTheme: const BadgeThemeData(
      textStyle: TextStyle(
        fontSize: 11,
        fontWeight: FontWeight.w500,
      ),
    ),
    inputDecorationTheme: themeData.inputDecorationTheme.copyWith(
      isDense: true,
      labelStyle: const TextStyle(fontSize: 13),
      border: const OutlineInputBorder(),
      contentPadding: const EdgeInsets.all(kInputDecorationPadding),
      enabledBorder: OutlineInputBorder(
        borderSide: BorderSide(
          color: themeData.colorScheme.brightness == Brightness.light
              ? Colors.black45
              : Colors.white.withValues(alpha: 0.13),
        ),
      ),
    ),
  );
}

ThemeData overrideThemeData(MaterialColor primarySwatch, BuildContext context) {
  return Theme.of(context).brightness == Brightness.light
      ? buildLightThemeData(primarySwatch)
      : buildDarkThemeData(primarySwatch);
}

ThemeData buildLightThemeData(MaterialColor seedColor) {
  final colorScheme = ColorScheme.fromSeed(seedColor: seedColor);
  return _wrapThemeData(ThemeData(
    brightness: Brightness.light,
    colorScheme: colorScheme,
    bottomSheetTheme: BottomSheetThemeData(
      surfaceTintColor: colorScheme.surface,
      modalBackgroundColor: getSurfaceBright(seedColor: seedColor),
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: Colors.grey[900],
      foregroundColor: Colors.grey[200],
    ),
    inputDecorationTheme: const InputDecorationTheme(
      filled: true,
      fillColor: Colors.white,
    ),
  ));
}

ThemeData buildDarkThemeData(MaterialColor seedColor) {
  final colorScheme = ColorScheme.fromSeed(
    seedColor: seedColor,
    brightness: Brightness.dark,
  );

  return _wrapThemeData(ThemeData(
    brightness: Brightness.dark,
    colorScheme: colorScheme,
    bottomSheetTheme: BottomSheetThemeData(
      modalBackgroundColor: getSurfaceContainer(
        seedColor: seedColor,
        brightness: Brightness.dark,
      ),
    ),
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: Colors.grey[200],
      foregroundColor: Colors.grey[900],
    ),
    inputDecorationTheme: InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surfaceContainer,
    ),
  ));
}

Color? colorWithOffset(BuildContext context, MaterialColor color, int offset) {
  final isDark = Theme.of(context).brightness == Brightness.dark;
  return color[500 + (isDark ? -1 : 1) * offset];
}

bool isSmallScreen(BuildContext context) {
  final size = MediaQuery.sizeOf(context);
  return size.width <= _kSmallScreenWidthBreakpoint;
}

bool isLargeScreen(BuildContext context) {
  final size = MediaQuery.sizeOf(context);
  return size.width >= _kLargeScreenWidthBreakpoint;
}

Color getSurfaceBright({
  required Color seedColor,
  Brightness brightness = Brightness.light,
}) {
  CorePalette p = CorePalette.of(seedColor.toARGB32());
  return Color(p.neutral.get(brightness == Brightness.dark ? 24 : 99));
}

Color getSurfaceContainer({
  required Color seedColor,
  Brightness brightness = Brightness.light,
}) {
  CorePalette p = CorePalette.of(seedColor.toARGB32());
  return Color(p.neutral.get(brightness == Brightness.dark ? 10 : 92));
}
