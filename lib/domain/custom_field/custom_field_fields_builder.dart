import 'package:bitacora/domain/common/repository_query_context.dart';

abstract class CustomFieldFieldsBuilder {
  CustomFieldFieldsBuilder remoteId();

  CustomFieldFieldsBuilder name();

  CustomFieldFieldsBuilder type();

  CustomFieldFieldsBuilder deletedAt();

  CustomFieldFieldsBuilder updatedAt();

  CustomFieldFieldsBuilder createdAt();

  CustomFieldFieldsBuilder allowedValues(Fields fields);

  CustomFieldFieldsBuilder parent(Fields fields);

  CustomFieldFieldsBuilder organization(Fields fields);

  Fields build();
}
