import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:bitacora/infrastructure/report_template/report_template_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

import '../../test_util.dart';
import '../mocks.dart';

void main() {
  group('$ReportTemplateDbContract tests', () {
    test('Create $ReportTemplateDbContract', () {
      expectRemovingSpaces(
        const ReportTemplateDbContract().create,
        '''
        CREATE TABLE reportTemplate (
          rt_id INTEGER PRIMARY KEY AUTOINCREMENT,
          rt_active INTEGER,
          rt_description TEXT NOT NULL,
          rt_option TEXT NOT NULL,
          rt_order INTEGER NOT NULL,
          rt_organizationId INTEGER NOT NULL
        )
        ''',
      );
    });

    test('Migrates create', () async {
      final db = MockDatabase();
      when(() => db.execute(any())).thenAnswer((_) => Future.value(null));
      const contract = ReportTemplateDbContract();

      await contract.upgradeOrCreate(db, 1, kDbVersionWithReportTemplateTable);

      verify(() => db.execute(contract.create)).called(1);
    });
  });
}
