import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/custom_field_options/custom_field_options.dart';
import 'package:bitacora/domain/custom_field_options/custom_field_options_fields_builder.dart';

abstract class CustomFieldOptionsRepository<C extends RepositoryQueryContext,
        F extends CustomFieldOptionsFieldsBuilder>
    extends RepositoryTable<CustomFieldOptions, C, F> {}
