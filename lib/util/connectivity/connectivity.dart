import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:connectivity_plus/connectivity_plus.dart' as plus;
import 'package:flutter/material.dart';

export 'package:connectivity_plus/connectivity_plus.dart'
    show ConnectivityResult;

class Connectivity {
  static final Connectivity _instance = Connectivity._();

  final ValueNotifier<List<plus.ConnectivityResult>> notifier =
      ValueNotifier<List<plus.ConnectivityResult>>(
          AppConfig().isIntegrationTest ? [plus.ConnectivityResult.wifi] : []);

  factory Connectivity() => inject(() => _instance);

  Connectivity._() {
    if (AppConfig().isIntegrationTest) {
      return;
    }

    final connectivity = plus.Connectivity();
    (connectivity.checkConnectivity()).then((value) {
      logger.i('connectivity Initial check: $value');
      _setCurrent(value);
    });
    connectivity.onConnectivityChanged.listen((event) {
      logger.i('connectivity Changed: $event');
      _setCurrent(event);
    });
  }

  void onResume() {
    (plus.Connectivity().checkConnectivity()).then((value) {
      logger.i('connectivity onResume check: $value');
      _setCurrent(value);
    });
  }

  void _setCurrent(List<plus.ConnectivityResult> value) {
    notifier.value = value;
  }

  bool hasConnectivity() {
    final result = notifier.value;
    return result.isNotEmpty && !result.contains(plus.ConnectivityResult.none);
  }
}
