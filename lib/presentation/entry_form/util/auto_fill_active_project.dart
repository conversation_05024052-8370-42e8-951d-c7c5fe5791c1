import 'package:bitacora/presentation/entry_form/entry_form_controller_context_snapshot.dart';
import 'package:flutter/material.dart';

typedef AutoFillCacheGetter = Function(EntryFormControllerContextSnapshot);

mixin AutoFillerFromCache {
  Map<TextEditingController, AutoFillCacheGetter> get fillMap;

  final List<TextEditingController> _filledControllers = [];

  @protected
  void fillFromCache(EntryFormControllerContextSnapshot contextSnapshot) {
    fillMap.forEach((textController, cacheGetter) {
      textController.text = cacheGetter(contextSnapshot) ?? '';
      _filledControllers.add(textController);
    });
  }

  @protected
  void reFillFromCache(EntryFormControllerContextSnapshot contextSnapshot) {
    for (final controller in _filledControllers) {
      controller.text = '';
    }
    _filledControllers.clear();

    fillFromCache(contextSnapshot);
  }
}
