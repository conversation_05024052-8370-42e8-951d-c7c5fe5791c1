import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_metadata/custom_field_metadata.dart';
import 'package:bitacora/domain/template_block/template_block.dart';
import 'package:bitacora/presentation/entry_form/entry_form_props.dart';
import 'package:bitacora/presentation/entry_form/templatelog/template_block/controller/template_block_multi_value_form_controller.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../../../../domain/custom_field/mocks.dart';
import '../../../../../../domain/custom_field_options/mocks.dart';
import '../../../../../../domain/template_block/mocks.dart';
import '../../../../../../domain/templatelog/mocks.dart';

class TemplateBlockMultiValueFormControllerTestRobot {
  TemplateBlockMultiValueFormController? _instance;
  TemplateBlock? _templateBlock;
  List<CustomFieldMetadata>? _fieldsMetadata;

  void build<T>({List<CustomFieldAllowedValue> allowedValues = const []}) {
    _templateBlock = mockTemplateBlock(
        customFieldOptions: mockCustomFieldOptions(
            customField: mockCustomField(
      allowedValues: allowedValues,
    )));
    final props = _buildEntryProps();
    _instance = TemplateBlockMultiValueFormController<T>(
      _templateBlock!,
      props,
    ) as TemplateBlockMultiValueFormController;
  }

  void read<T>(
    CustomFieldMetadata? metadata,
    CustomFieldMetadata? lastMetadata,
    String emptyString,
  ) {
    _instance!.read((notifier, readValue, lastReadValue, [defaultValue]) {
      if (_instance != null) {
        notifier.value = readValue as List<T>?;
      }
    }, metadata, lastMetadata, emptyString);
  }

  void verifyValue<T>(List<T> expectedValue) {
    expect(_instance!.value.value, expectedValue);
    expect(_instance!.dbValue, expectedValue);
  }

  EntryFormProps _buildEntryProps() {
    return EntryFormProps(
        inputEntry: mockEntry(
            withId: true,
            extension: mockTemplatelog(
              fieldsMetadata: _fieldsMetadata,
            )));
  }

  void setFieldsMetadata(List<CustomFieldMetadata> fieldsMetadata) {
    _fieldsMetadata = fieldsMetadata;
  }

  void verifyInstance<T>() {
    expect(_instance!.value.value, []);
    expect(_instance!.dbValue, []);
  }

  void dispose() {}
}
