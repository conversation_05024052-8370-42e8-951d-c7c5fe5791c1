import 'dart:async';
import 'dart:io';

import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/url_launcher.dart';
import 'package:url_launcher/url_launcher.dart';

class WebExternalLauncher {
  factory WebExternalLauncher() => inject(() => const WebExternalLauncher._());

  const WebExternalLauncher._();

  Future<void> launchPrivacyPolicy() async {
    final localNameStarWithEs = Platform.localeName.startsWith('es');
    final params = Uri(
        scheme: 'https',
        host: 'www.bitacora.io',
        path: localNameStarWithEs ? 'politica-de-privacidad' : 'privacy-policy',
        queryParameters: localNameStarWithEs ? null : {'lang': 'en'});
    final url = params.toString();

    await _launch(url);
  }

  Future<void> launchYoutubeChannel(String channelId) async {
    final params = Uri(
      scheme: 'https',
      host: 'youtube.com',
      path: 'channel/$channelId',
    );
    final url = params.toString();

    await _launch(url);
  }

  Future<void> launchWhatsapp(String phoneNumber, String message) async {
    final params = Uri(
      scheme: 'https',
      host: 'wa.me',
      path: phoneNumber,
      queryParameters: {'text': message},
    );
    final url = params.toString();

    await _launch(url);
  }

  Future<void> launchEmail(String contactEmail, String body) async {
    final params = Uri(
      scheme: 'mailto',
      path: contactEmail,
      query: 'body=$body',
    );
    final url = params.toString();

    await _launch(url);
  }

  Future<void> _launch(String url) async {
    await UrlLauncher().launch(url, mode: LaunchMode.externalApplication);
  }
}
