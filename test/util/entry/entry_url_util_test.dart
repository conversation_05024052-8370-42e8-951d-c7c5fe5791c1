import 'package:bitacora/analytics/analytics_events.dart';
import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/recover_session/recover_session_launcher.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/util/entry/entry_url_util.dart';
import 'package:bitacora/util/entry/entry_url_util_snapshot.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/share.dart';
import 'package:bitacora/util/web_app_launcher.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:provider/provider.dart';

import '../../analytics/mocks.dart';
import '../../application/api/mocks.dart';
import '../../application/cache/auth/mocks.dart';
import '../../mocks.dart';
import '../../mocktail_fallback_values.dart';
import '../mocks.dart';

void main() {
  BuildContext? context;

  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
  });

  Widget testWidget({
    AnalyticsLogger? analyticsLogger,
    ApiHelper? apiHelper,
    WebAppLauncher? webAppLauncher,
  }) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<ActiveSession>(
          create: (context) => mockActiveSession(session: mockSession()),
        ),
        Provider<AnalyticsLogger>(
          create: (_) => analyticsLogger ?? mockAnalyticsLogger(),
        ),
        Provider<ApiHelper>(
          create: (_) => apiHelper ??= _mockApiHelper(),
        ),
        Provider<RecoverSessionLauncher>(
            create: (_) => RecoverSessionLauncher()),
      ],
      child: MaterialApp(
        home: Localizations(
          locale: const Locale('es'),
          delegates: AppLocalizations.localizationsDelegates,
          child: Scaffold(
            body: Builder(builder: (c) {
              context = c;
              return Container();
            }),
          ),
        ),
      ),
    );
  }

  group('$EntryUrlUtil tests', () {
    test('Injects same', () {
      expect(EntryUrlUtil(), EntryUrlUtil());
    });

    testWidgets('Launches entry in web app', (tester) async {
      final entry = mockEntry(withRemoteId: true);
      final webAppLauncher = MockWebAppLauncher();
      when(() => webAppLauncher.maybeLaunchBitacoraWebApp(
          any(), any(), any(), any(), any())).thenAnswer((_) => Future.value());
      const hashId = 'ueoa';
      await tester.pumpWidget(testWidget(
        webAppLauncher: webAppLauncher,
        apiHelper: _mockApiHelper(hashId),
      ));
      final entryUrlUtil = EntryUrlUtil();

      await withInjected<WebAppLauncher>(
        webAppLauncher,
        () => entryUrlUtil.maybeLaunchEntryInWebApp(
            EntryUrlUtilContextSnapshot(context!), entry.remoteId!),
      );

      final path = verify(() => webAppLauncher.maybeLaunchBitacoraWebApp(
          any(), any(), any(), any(), captureAny())).captured.first;
      expect(path, 'entry/$hashId');
    });

    // FIXME: test show toast on Dio error (zone WebAppLauncher).

    testWidgets('Shares entry url', (tester) async {
      final entry = mockEntry(withRemoteId: true);
      const hashId = 'ueoa';
      final apiHelper = _mockApiHelper(hashId);
      await tester.pumpWidget(testWidget(
        apiHelper: apiHelper,
      ));
      final share = mockShare();
      final entryUrlUtil = EntryUrlUtil();

      await withInjected<Share>(
        share,
        () => entryUrlUtil.shareEntry(
          EntryUrlUtilContextSnapshot(context!),
          entry.remoteId!,
        ),
      );

      verify(() => share.share('${AppConfig().webAppUrl}/entry/$hashId'));
    });

    testWidgets('Logs share event', (tester) async {
      final analyticsLogger = mockAnalyticsLogger();
      await tester.pumpWidget(testWidget(analyticsLogger: analyticsLogger));
      final entryUrlUtil = EntryUrlUtil();

      await withInjected<Share>(
        mockShare(),
        () => entryUrlUtil.shareEntry(
            EntryUrlUtilContextSnapshot(context!), const RemoteId(123)),
      );

      final capturedEvent =
          verify(() => analyticsLogger.logEvent(captureAny())).captured.first;
      expect(capturedEvent, AnalyticsEvent.shareEntry);
    });
  });
}

ApiHelper _mockApiHelper([String hashId = 'aoeu']) {
  final response = MockResponse<Map<String, dynamic>>();
  when(() => response.data).thenReturn({'hash_id': hashId});
  final apiHelper = MockApiHelper();
  when(() => apiHelper.get<Map<String, dynamic>>(any())).thenAnswer(
      (_) => Future<Response<Map<String, dynamic>>>.value(response));
  return apiHelper;
}
