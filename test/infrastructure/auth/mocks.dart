import 'package:bitacora/infrastructure/auth/auth_mixed_db_repository.dart';
import 'package:bitacora/infrastructure/auth/current_api_token.dart';
import 'package:mocktail/mocktail.dart';

class MockAuthMixedDbRepository extends Mock implements AuthMixedDbRepository {}

class MockAuthSharedPrefsRepositoryInjector extends Mock
    implements AuthRepositoryInjector {}

class MockCurrentApiToken extends Mock implements CurrentApiToken {}

AuthRepositoryInjector mockAuthSharedPrefsRepositoryProvider() {
  final mock = MockAuthSharedPrefsRepositoryInjector();
  when(() => mock.get(any())).thenReturn(MockAuthMixedDbRepository());
  return mock;
}

CurrentApiToken mockCurrentApiToken() {
  final mock = MockCurrentApiToken();
  when(() => mock.saveToken(any())).thenAnswer((_) => Future.value());
  when(() => mock.getToken()).thenAnswer((_) => Future.value());
  when(() => mock.nuke()).thenAnswer((_) => Future.value());
  return mock;
}
