import 'package:bitacora/application/sync/machine/steps/download/collection/custom_field/custom_field_by_remote_id_repository_query.dart';
import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/common/value_object/remote_id.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/sync_metadata/value/sync_metadata_collection_type.dart';
import 'package:bitacora/util/logger/logger.dart';

class SyncCollectionCustomFieldDownloader extends SyncCollectionDownloader {
  final List<_PendingRelation> _pendingRelations = [];

  SyncCollectionCustomFieldDownloader(super.params);

  @override
  SyncMetadataCollectionType get collectionType =>
      SyncMetadataCollectionType.customField;

  @override
  Future<LocalId?> translateAndSave(Map<String, dynamic> map) async {
    final remoteId = RemoteId(map['id']);
    final parentRemoteId = RemoteId(map['parent_id']);

    if (remoteId.value == null) {
      return null;
    }

    return db.transaction<LocalId?>((dbContext) async {
      LocalId? parentLocalId;

      if (parentRemoteId.value != null) {
        final parentField = await db.query(
          CustomFieldByRemoteIdRepositoryQuery(parentRemoteId),
          context: dbContext,
        );
        parentLocalId = parentField?.id;
      }

      map['organization_id'] = params.organization.id!.dbValue;

      if (parentRemoteId.value == null) {
        return _saveCompleteCustomField(dbContext, map);
      }

      if (parentRemoteId.value != null && parentLocalId != null) {
        return _saveCompleteCustomField(dbContext, map);
      }

      final customFieldWithoutParent =
          apiTranslator.customField.fromMap(map..remove('parent_id'));
      final customFieldId =
          await db.customField.save(dbContext, customFieldWithoutParent);

      if (customFieldId == null) {
        return null;
      }

      _pendingRelations.add(_PendingRelation(
        parentRemoteId: parentRemoteId,
        childLocalId: customFieldId,
        pendingAllowedValues: map['allowed_values'],
      ));
      logger.w(
          'sync:download:${collectionType.value.apiKey}${params.durationTracker} Saved field without parent (id: $customFieldId), pending relation: $remoteId -> $parentRemoteId');

      return customFieldId;
    });
  }

  Future<LocalId?> _saveCompleteCustomField(
      RepositoryQueryContext dbContext, Map<String, dynamic> map) async {
    final customField = apiTranslator.customField.fromMap(map);
    final customFieldId = await db.customField.save(dbContext, customField);

    await _saveAllowedValues(dbContext, map['allowed_values'], customFieldId);

    return customFieldId;
  }

  Future<void> _saveAllowedValues(
    RepositoryQueryContext dbContext,
    List<dynamic> allowedValuesData,
    LocalId? customFieldId,
  ) async {
    await Future.wait(allowedValuesData.map((e) {
      e['custom_field_id'] = customFieldId!.dbValue;
      final allowedValue = apiTranslator.customFieldAllowedValue.fromMap(e);
      return db.customFieldAllowedValue.save(dbContext, allowedValue);
    }));
  }

  @override
  Future<void> deleteArchived(RemoteId remoteId) async {
    await db.transaction((context) async {
      final customField = await db.query(
          CustomFieldByRemoteIdRepositoryQuery(remoteId),
          context: context);
      if (customField != null) {
        await db.customField.delete(context, customField.id!);
      }
    });
  }

  @override
  Future<void> resolvePendingRelations() async {
    final resolvedRelations = <_PendingRelation>[];

    await db.transaction((dbContext) async {
      for (final relation in _pendingRelations) {
        final parent = await db.query(
          CustomFieldByRemoteIdRepositoryQuery(relation.parentRemoteId),
          context: dbContext,
        );
        if (parent?.id != null) {
          final childToUpdate = await db.customField.find(
            dbContext.copyWith(
              fields: dbContext.db.customField.fieldsBuilder.build(),
            ),
            relation.childLocalId,
          );
          if (childToUpdate != null) {
            await db.customField.save(
              dbContext,
              childToUpdate.copyWith(parent: CustomField(id: parent!.id!)),
            );
            if (relation.pendingAllowedValues != null) {
              await _saveAllowedValues(
                dbContext,
                relation.pendingAllowedValues!,
                relation.childLocalId,
              );
              logger.i(
                  'sync:download:${collectionType.value.apiKey}${params.durationTracker} Synced allowed_values for ${relation.childLocalId.value} after parent resolution');
            }
            resolvedRelations.add(relation);
            logger.i(
                'sync:download:${collectionType.value.apiKey}${params.durationTracker} Resolved relation: ${relation.childLocalId.value} -> ${relation.parentRemoteId.value}');
          }
        }
      }
    });

    _pendingRelations
        .removeWhere((element) => resolvedRelations.contains(element));
  }
}

class _PendingRelation {
  final RemoteId parentRemoteId;
  final LocalId childLocalId;
  final List<dynamic>? pendingAllowedValues;

  _PendingRelation({
    required this.parentRemoteId,
    required this.childLocalId,
    this.pendingAllowedValues,
  });

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is _PendingRelation &&
          runtimeType == other.runtimeType &&
          parentRemoteId == other.parentRemoteId &&
          childLocalId == other.childLocalId;

  @override
  int get hashCode => parentRemoteId.hashCode ^ childLocalId.hashCode;
}
