import 'package:bitacora/domain/common/value_object/value_object.dart';

enum ResourceAggregationEntityTypeValue {
  qrCode;

  static ResourceAggregationEntityTypeValue fromApiValue(String value) {
    switch (value) {
      case 'QR_CODE':
        return ResourceAggregationEntityTypeValue.qrCode;
      default:
        throw 'ResourceAggregationType apiValue [$value] not supported';
    }
  }
}

class ResourceAggregationEntityType
    extends ValueObject<ResourceAggregationEntityTypeValue> {
  ResourceAggregationEntityType(super.value);

  static ResourceAggregationEntityType qrCode =
      ResourceAggregationEntityType(ResourceAggregationEntityTypeValue.qrCode);

  factory ResourceAggregationEntityType.fromApiValue(String value) =>
      ResourceAggregationEntityType(
          ResourceAggregationEntityTypeValue.fromApiValue(value));

  factory ResourceAggregationEntityType.fromDbValue(int value) =>
      ResourceAggregationEntityType(
          ResourceAggregationEntityTypeValue.values[value - 1]);

  @override
  bool isSameType(Object other) {
    return other is ResourceAggregationEntityType;
  }

  @override
  get dbValue => value.index + 1;
}
