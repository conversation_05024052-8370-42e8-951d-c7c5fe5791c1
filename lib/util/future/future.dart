import 'dart:async';

import 'package:bitacora/util/inject/inject.dart';

class FutureInjector {
  factory FutureInjector() => inject(() => const FutureInjector._());

  const FutureInjector._();

  FutureOr delayed<T>(
    Duration duration, [
    FutureOr<T> Function()? computation,
  ]) async {
    return computation == null
        ? Future.delayed(duration)
        : Future.delayed(duration, computation);
  }
}
