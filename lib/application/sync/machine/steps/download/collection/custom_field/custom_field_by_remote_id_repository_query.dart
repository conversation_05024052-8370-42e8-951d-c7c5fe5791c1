import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';

class CustomFieldByRemoteIdRepositoryQuery
    extends RepositoryQuery<CustomField?> {
  final RemoteId remoteId;

  const CustomFieldByRemoteIdRepositoryQuery(this.remoteId);

  @override
  Future<CustomField?> run(RepositoryQueryContext context) =>
      context.db.customField.findByRemoteId(context, remoteId);

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.customField.fieldsBuilder.build();
}
