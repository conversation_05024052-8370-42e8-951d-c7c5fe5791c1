import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class TemplateConditionDbContract extends DbContract {
  static const String _ = 'tc_';
  static const String _tableName = 'templateCondition';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String value = '${_}value';
  final String operator = '${_}operator';
  final String whenBlockId = '${_}whenBlockId';
  final String templateBlockId = '${_}templateBlockId';
  final String templateGroupId = '${_}templateGroupId';

  const TemplateConditionDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithTemplateCondition;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $value NOT NULL,
    $operator INTEGER NOT NULL,
    $whenBlockId INTEGER NOT NULL,
    $templateBlockId INTEGER,
    $templateGroupId INTEGER
  )
  ''';

  @override
  FutureOr<void> upgrade(Database db, int oldVersion, int newVersion) {
    if (oldVersion < kDbVersionWithTemplateCondition) {
      return db.execute(create);
    }
  }
}
