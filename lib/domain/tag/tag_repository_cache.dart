import 'package:bitacora/domain/common/repository_cache.dart';
import 'package:bitacora/domain/common/repository_table_cache.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:flutter/foundation.dart';

class TagRepositoryCache extends RepositoryTableCache<Tag> {
  @override
  @protected
  Tag? completeCachedModel(RepositoryCache cache, Tag model) {
    final orgId = model.organization?.id;
    if (orgId != null) {
      final organization =
          cache.table<Organization>()!.cachedModel(cache, orgId);
      return merge(model, Tag(organization: organization));
    }
    return model;
  }

  @override
  @protected
  bool isContainedIn(RepositoryCache cache, Tag a, Tag b) {
    return isNullOrEqual(a.id, b.id) &&
        isNullOrEqual(a.remoteId, b.remoteId) &&
        isNullOrEqual(a.name, b.name) &&
        isNullOrEqual(a.color, b.color) &&
        isNestedContainedIn(cache, a.organization, b.organization);
  }

  @override
  @protected
  Tag merge(Tag a, Tag b) {
    if (a.id != null && b.id != null && a.id != b.id) {
      throw 'Merging models with different id';
    }

    return Tag(
      id: b.id ?? a.id,
      remoteId: b.remoteId ?? a.remoteId,
      name: b.name ?? a.name,
      color: b.color ?? a.color,
      organization: b.organization ?? a.organization,
    );
  }

  @override
  @protected
  Tag prepareToCache(RepositoryCache cache, LocalId id, Tag model) {
    final orgId = cache.table<Organization>()?.resolveId(model.organization);
    return merge(
      model,
      Tag(
        id: id,
        organization: orgId == null ? null : Organization(id: orgId),
      ),
    );
  }
}
