import 'package:bitacora/application/restart.dart';
import 'package:bitacora/util/restart_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_test/flutter_test.dart';

import '../base/base_robot.dart';

void main() {
  group('$Restart tests', () {
    test('Injects same', () {
      expect(Restart(), Restart());
    });

    testWidgets('Restarts widget', (tester) async {
      final robot = _RestartRobot(tester);
      await robot.pumpWidget();

      await robot.restart();

      robot.verifyRestart();
    });
  });
}

class _RestartRobot extends BaseRobot {
  int _testWidgetCreateStateCount = 0;

  _RestartRobot(super.tester);

  @override
  Future<void> pumpWidget() {
    return pumpTestApp(
      parent: (child) => RestartWidget(child: child),
      child: _TestWidget(onStateCreated: () => _testWidgetCreateStateCount++),
    );
  }

  Future<void> restart() async {
    Restart().restart(context);
    await tester.pumpAndSettle();
  }

  void verifyRestart() {
    expect(_testWidgetCreateStateCount, 2);
  }
}

class _TestWidget extends StatefulWidget {
  final VoidCallback onStateCreated;

  const _TestWidget({required this.onStateCreated});

  @override
  State<_TestWidget> createState() => _TestWidgetState();
}

class _TestWidgetState extends State<_TestWidget> {
  @override
  void initState() {
    widget.onStateCreated();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container();
  }
}
