import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart' as intl;

class FancyDate extends StatelessWidget {
  final LogDay logDay;
  final LogTime? logTime;
  final VoidCallback? onLongPress;
  final VoidCallback? onDateTap;
  final VoidCallback? onTimeTap;

  const FancyDate({
    super.key,
    required this.logDay,
    this.logTime,
    this.onLongPress,
    this.onDateTap,
    this.onTimeTap,
  });

  bool get _isCurrentDay => logDay.value == getLogDayForToday();

  @override
  Widget build(BuildContext context) {
    final baseStyle = _getBaseStyle(context);
    final date = getDateTimeFromLogDay(logDay);
    final now = DateTime.now();
    final locale = Localizations.localeOf(context).languageCode;

    final left = date.day.toString();
    final top = intl.DateFormat.MMM(locale).format(date);
    final bottom = now.year == date.year
        ? intl.DateFormat.E(locale).format(date)
        : date.year.toString();
    final right = _getRight(context);

    final letterSpacings = _calcLetterSpacings(baseStyle, top, bottom);

    return FittedBox(
      fit: BoxFit.fitWidth,
      alignment: Alignment.centerLeft,
      child: InkWell(
        borderRadius: kBorderRadius,
        onLongPress: onLongPress,
        child: AnimatedContainer(
          padding: const EdgeInsets.symmetric(horizontal: 2.0),
          decoration: BoxDecoration(
            borderRadius: kBorderRadius,
            color: _isCurrentDay
                ? Theme.of(context).colorScheme.primary
                : Colors.transparent,
          ),
          duration: const Duration(milliseconds: 200),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWell(
                borderRadius: kBorderRadius,
                onTap: onDateTap,
                child: Padding(
                  padding: const EdgeInsets.all(4.0),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        left,
                        textScaler: const TextScaler.linear(1),
                        style: _leftStyle(baseStyle),
                      ),
                      Center(
                        child: Stack(
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 7.0),
                              child: Text(
                                top,
                                textScaler: const TextScaler.linear(1),
                                style: _topStyle(baseStyle, letterSpacings[0]),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(top: 15),
                              child: Text(
                                bottom,
                                textScaler: const TextScaler.linear(1),
                                style:
                                    _bottomStyle(baseStyle, letterSpacings[1]),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              if (right != null) ...[
                SizedBox(
                  width: 5,
                  height: 28,
                  child: VerticalDivider(
                    thickness: 2.5,
                    color: _getColor(context),
                  ),
                ),
                InkWell(
                  borderRadius: kBorderRadius,
                  onTap: onTimeTap,
                  child: Padding(
                    padding: const EdgeInsets.all(4.0),
                    child: Text(
                      right,
                      textScaler: const TextScaler.linear(1),
                      style: _rightStyle(baseStyle),
                    ),
                  ),
                )
              ]
            ],
          ),
        ),
      ),
    );
  }

  TextStyle _getBaseStyle(BuildContext context) {
    return Theme.of(context)
        .textTheme
        .titleLarge!
        .copyWith(color: _getColor(context));
  }

  Color _getColor(BuildContext context) {
    return _isCurrentDay
        ? Theme.of(context).colorScheme.surface
        : Theme.of(context).colorScheme.onSurface;
  }

  TextStyle _leftStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(fontWeight: FontWeight.w900, fontSize: 28);
  }

  TextStyle _topStyle(TextStyle baseStyle, double letterSpacing) {
    return baseStyle.copyWith(
      fontWeight: FontWeight.w900,
      fontSize: 15,
      letterSpacing: letterSpacing,
    );
  }

  TextStyle _bottomStyle(TextStyle baseStyle, double letterSpacing) {
    return baseStyle.copyWith(
      fontWeight: FontWeight.w600,
      fontSize: 8,
      letterSpacing: letterSpacing,
    );
  }

  TextStyle _rightStyle(TextStyle baseStyle) {
    return baseStyle.copyWith(fontWeight: FontWeight.w700, fontSize: 21);
  }

  List<double> _calcLetterSpacings(
      TextStyle baseStyle, String top, String bottom) {
    final spacings = [0.0, 0.0];
    var topMeasure = _textSize(top, _topStyle(baseStyle, spacings[0]));
    var bottomMeasure = _textSize(bottom, _bottomStyle(baseStyle, spacings[1]));

    if (topMeasure.width > bottomMeasure.width) {
      spacings[1] = (topMeasure.width - bottomMeasure.width) / bottom.length;
    } else {
      spacings[0] = (bottomMeasure.width - topMeasure.width) / top.length;
    }

    return spacings;
  }

  Size _textSize(String text, TextStyle style) {
    final textPainter = TextPainter(
        text: TextSpan(text: text, style: style),
        maxLines: 1,
        textDirection: TextDirection.ltr)
      ..layout(minWidth: 0, maxWidth: double.infinity);
    return textPainter.size;
  }

  String? _getRight(BuildContext context) {
    if (logTime == null) {
      return null;
    }

    return logTime!.displayValue;
  }
}
