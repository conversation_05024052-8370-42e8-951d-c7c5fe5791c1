import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/person/person.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/person_detail/person_detail_db_contract.dart';

class PersonDetailDbTranslator implements DbTranslator<PersonDetail> {
  const PersonDetailDbTranslator();

  @override
  Set<Field> get nestedModelFields => personDetailNestedModelFields;

  @override
  Future<PersonDetail> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return PersonDetail(
      id: fields[PersonDetailField.id]?.value(map),
      remoteId: fields[PersonDetailField.remoteId]?.value(map),
      area: fields[PersonDetailField.area]?.value(map),
      company: fields[PersonDetailField.company]?.value(map),
      description: fields[PersonDetailField.description]?.value(map),
      avatar: await fields[PersonDetailField.avatar]?.nested(context, map),
      organization:
          await fields[PersonDetailField.organization]?.nested(context, map),
      phones: await fields[PersonDetailField.phones]?.nested(context, map),
      emails: await fields[PersonDetailField.emails]?.nested(context, map),
      addresses:
          await fields[PersonDetailField.addresses]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, PersonDetail model) async {
    final map = <String, dynamic>{};
    const contract = PersonDetailDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.area, model.area);
    addField(map, contract.company, model.company);
    addField(map, contract.description, model.description);

    await saveNestedModel<Person>(
        context, map, contract.personId, context.db.person, model.person);
    await saveNestedModel<Organization>(context, map, contract.organizationId,
        context.db.organization, model.organization);
    return map;
  }
}
