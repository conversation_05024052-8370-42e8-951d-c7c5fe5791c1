import 'dart:math';

import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/infrastructure/project/project_db_table.dart';
import 'package:mocktail/mocktail.dart';

import '../../domain/common/mocks.dart';

class MockProjectDbTable extends Mock implements ProjectDbTable {}

class MockProjectModelTranslator extends Mock
    implements ModelTranslator<Project> {}

ProjectDbTable mockProjectDbTable({
  LocalId? savedId,
}) {
  final mock = MockProjectDbTable();
  final random = Random(0);
  when(() => mock.save(any(), any())).thenAnswer((invocation) {
    final project = invocation.positionalArguments[1] as Project;
    if (project.id?.value != null) {
      return Future.value(project.id);
    }
    return Future.value(savedId ?? LocalId(random.nextInt(10000)));
  });
  when(() => mock.delete(any(), any())).thenAnswer((_) => Future.value(1));
  when(() => mock.hasEntries(any(), any()))
      .thenAnswer((_) => Future.value(false));
  return mock;
}

ModelTranslator<Project> mockProjectModelTranslator({
  bool prepareToMap = true,
  bool prepareFromMap = true,
}) {
  final mock = MockProjectModelTranslator();
  prepareMockModelTranslator(
    mock,
    prepareToMap: prepareToMap,
    fromMapBuilder:
        prepareFromMap ? (map) => Project(remoteId: RemoteId(map['id'])) : null,
  );
  return mock;
}
