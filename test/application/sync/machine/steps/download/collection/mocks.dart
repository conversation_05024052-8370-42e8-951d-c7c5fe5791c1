import 'package:bitacora/application/sync/machine/steps/download/collection/sync_collection_downloader.dart';
import 'package:bitacora/domain/sync_metadata/sync_metadata.dart';
import 'package:mocktail/mocktail.dart';

class MockSyncCollectionDownloader extends <PERSON><PERSON>
    implements SyncCollectionDownloader {}

SyncCollectionDownloader mockSyncCollectionDownloader(
    List<SyncMetadata> metadata) {
  final mock = MockSyncCollectionDownloader();
  final collectionType = metadata.first.collectionType!;
  when(() => mock.collectionType).thenReturn(collectionType);
  when(() => mock.getSyncMetadata()).thenAnswer((invocation) => Future.value(
      metadata.isEmpty ? const SyncMetadata() : metadata.removeAt(0)));
  when(() => mock.download(any(), any()))
      .thenAnswer((invocation) => Future.value());
  when(() => mock.resolvePendingRelations())
      .thenAnswer((invocation) => Future.value());
  return mock;
}
