import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class SignatureDbContract extends DbContract {
  static const String _ = 's_';
  static const String _tableName = 'signature';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String doodle = '${_}doodle';
  final String s3Key = '${_}s3Key';
  final String comments = '${_}comments';
  final String status = '${_}status';
  final String ownerEmail = '${_}ownerEmail';
  final String ownerName = '${_}ownerName';
  final String latitude = '${_}latitude';
  final String longitude = '${_}longitude';
  final String entryId = '${_}entryId';
  final String userId = '${_}userId';

  const SignatureDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithSignatureTable;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $doodle TEXT,
    $s3Key TEXT,
    $comments TEXT,
    $status INTEGER NOT NULL,
    $ownerEmail TEXT NOT NULL,
    $ownerName TEXT NOT NULL,
    $latitude REAL,
    $longitude REAL,
    $entryId INTEGER NOT NULL,
    $userId INTEGER
  )
  ''';
}
