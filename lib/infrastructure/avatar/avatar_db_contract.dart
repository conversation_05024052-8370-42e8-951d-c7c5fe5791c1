import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class AvatarDbContract extends DbContract {
  static const String _ = 'av_';
  static const String _tableName = 'avatar';

  final String id = '${_}id';
  final String s3Key = '${_}s3Key';
  final String path = '${_}path';
  final String isDownloaded = '${_}isDownloaded';
  final String transferState = '${_}transferState';
  final String personDetailId = '${_}personDetailId';

  const AvatarDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithPersonTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $s3Key TEXT NOT NULL,
    $path TEXT,
    $isDownloaded INTEGER,
    $transferState INTEGER,
    $personDetailId INTEGER NOT NULL UNIQUE
  )
  ''';
}
