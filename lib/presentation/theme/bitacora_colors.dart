import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';

const MaterialColor bitacoraBlue = MaterialColor(
  _bitacoraBluePrimaryValue,
  <int, Color>{
    50: Color(0xffdeeff4),
    100: Color(0xffadd7e3),
    200: Color(_bitacoraBluePrimaryValue),
    300: Color(0xff48a4c1),
    400: Color(0xff0a94bb),
    500: Color(0xff0084b5),
    600: Color(0xff0078a9),
    700: Color(0xff006798),
    800: Color(0xff005786),
    900: Color(0xff003a68),
  },
);
const int _bitacoraBluePrimaryValue = 0xff7bbdd1;

const MaterialColor bitacoraRed = MaterialColor(
  _bitacoraRedPrimaryValue,
  <int, Color>{
    50: Color(0xffffebee),
    100: Color(0xffffcdd2),
    200: Color(0xfff3999a),
    300: Color(0xffea7172),
    400: Color(0xfff5504f),
    500: Color(0xfffa3d33),
    600: Color(_bitacoraRedPrimaryValue),
    700: Color(0xffd9282d),
    800: Color(0xffcc2026),
    900: Color(0xffbd0e19),
  },
);
const int _bitacoraRedPrimaryValue = 0xffeb3333;

const int _bitacoraAquaPrimaryValue = 0xff05bac7;

const MaterialColor bitacoraYellow = MaterialColor(
  _bitacoraYellowPrimaryValue,
  <int, Color>{
    50: Color(0xfffff7df),
    100: Color(0xffffeaaf),
    200: Color(0xffffdd7b),
    300: Color(0xfffed143),
    400: Color(_bitacoraYellowPrimaryValue),
    500: Color(0xfffebb00),
    600: Color(0xfffead00),
    700: Color(0xffff9a00),
    800: Color(0xffff8800),
    900: Color(0xffff6600),
  },
);
const int _bitacoraYellowPrimaryValue = 0xfffec50a;

const MaterialColor bitacoraGreen = MaterialColor(
  _bitacoraGreenPrimaryValue,
  <int, Color>{
    50: Color(0xffe6f5ec),
    100: Color(0xffc2e6d0),
    200: Color(0xff9ad5b2),
    300: Color(0xff70c694),
    400: Color(0xff4fb97e),
    500: Color(_bitacoraGreenPrimaryValue),
    600: Color(0xff219e5e),
    700: Color(0xff188c52),
    800: Color(0xff117b46),
    900: Color(0xff065b32),
  },
);
const int _bitacoraGreenPrimaryValue = 0xff28ad69;

const MaterialColor bitacoraPurple = MaterialColor(
  _bitacoraPurplePrimaryValue,
  <int, Color>{
    50: Color(0xfff1e7f4),
    100: Color(0xffddc3e5),
    200: Color(0xffc79cd4),
    300: Color(0xffb177c2),
    400: Color(0xffa15cb4),
    500: Color(_bitacoraPurplePrimaryValue),
    600: Color(0xff8441a0),
    700: Color(0xff733a96),
    800: Color(0xff63358c),
    900: Color(0xff482c79),
  },
);
const int _bitacoraPurplePrimaryValue = 0xff9146a7;

Color getNextOrgColor(int numOrgs) {
  final options = <Color>[
    bitacoraGreen[500]!,
    const Color(_bitacoraAquaPrimaryValue),
    bitacoraBlue[500]!,
    bitacoraPurple[400]!,
  ];
  return options[numOrgs % options.length];
}

Color getTagColor(String name) {
  final options = <MaterialColor>[
    bitacoraGreen,
    bitacoraYellow,
    bitacoraBlue,
    bitacoraRed,
    bitacoraPurple,
  ];
  final base = name.hashCode % options.length;
  final shade = 400 + ('salty$name'.hashCode % 5) * 100;
  return options[base][shade]!;
}

MaterialColor createMaterialColor(Color color) {
  List strengths = <double>[.05];
  final swatch = <int, Color>{};
  final r = (color.r * 255).toInt(),
      g = (color.g * 255).toInt(),
      b = (color.b * 255).toInt();

  for (var i = 1; i < 10; i++) {
    strengths.add(0.1 * i);
  }
  for (final strength in strengths) {
    final ds = 0.5 - strength;
    swatch[(strength * 1000).round()] = Color.fromRGBO(
      r + ((ds < 0 ? r : (255 - r)) * ds).round(),
      g + ((ds < 0 ? g : (255 - g)) * ds).round(),
      b + ((ds < 0 ? b : (255 - b)) * ds).round(),
      1,
    );
  }
  return MaterialColor(color.toARGB32(), swatch);
}

Color getFieldMarkedAsNewColor(BuildContext context) {
  return colorWithOffset(context, bitacoraPurple, 200)!;
}
