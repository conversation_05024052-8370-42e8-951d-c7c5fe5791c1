import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/sync/machine/sync_machine_params.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/translator.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:flutter/foundation.dart';

abstract class SyncMachineStep {
  final SyncMachineParams params;

  bool _isCanceled = false;
  bool _isBusy = false;
  bool _isSynced = false;
  bool _isDirty = false;

  SyncMachineStep(this.params);

  void maybeReset(SyncTriggerEvent event) {
    if (_isCanceled || !_shouldTriggerReset(event)) {
      return;
    }

    _reset();
  }

  void _reset() {
    _isSynced = false;
    _isDirty = _isBusy;
  }

  Future<void> sync() async {
    if (_isSynced || _isBusy || _isCanceled) {
      throw 'Unexpected call to sync [$_isSynced, $_isBusy, $_isCanceled]';
    }

    _isBusy = true;
    _isDirty = false;

    try {
      logger.i(wrapSyncLog(''));
      await performSync();
      logger.i(wrapSyncLog('done'));
    } catch (e, s) {
      _isBusy = false;
      logger.e(wrapSyncLog('fail $e \n$s'));
      if (isCritical) {
        rethrow;
      }
    }

    _isBusy = false;
    _isSynced = !_isDirty;
  }

  bool get isSynced => _isSynced;

  Set<SyncTriggerSource> get triggerResetSources => <SyncTriggerSource>{};

  bool _shouldTriggerReset(SyncTriggerEvent event) {
    return event.mode == SyncTriggerMode.fullSync ||
        event.mode == SyncTriggerMode.superSync ||
        triggerResetSources.contains(event.source);
  }

  @protected
  Future<void> performSync();

  void cancel() {
    _isCanceled = true;
  }

  bool get isCanceled => _isCanceled;

  Session get session => params.session;

  Organization get organization => params.organization;

  Repository get db => params.db;

  Translator get apiTranslator => params.apiTranslator;

  ApiHelper get apiHelper => params.apiHelper;

  AnalyticsLogger get analyticsLogger => params.analyticsLogger;

  // Determines if failure to this step should stop the whole sync process.
  bool get isCritical => true;

  String get debugName;

  @protected
  String wrapSyncLog(String s) => 'sync:$debugName${params.durationTracker} $s';
}
