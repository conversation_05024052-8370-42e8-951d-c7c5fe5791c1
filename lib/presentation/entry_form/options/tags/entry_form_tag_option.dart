import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/presentation/entry_form/options/tags/entry_form_tag.dart';
import 'package:bitacora/presentation/entry_form/options/tags/entry_form_tag_option_actions.dart';
import 'package:bitacora/presentation/entry_form/options/tags/entry_form_tag_option_controller.dart';
import 'package:bitacora/presentation/entry_form/options/tags/tag_suggestion_repository_query.dart';
import 'package:bitacora/presentation/entry_form/util/entry_forms_util.dart';
import 'package:bitacora/presentation/entry_form/util/typeahead/typeahead_text_field_configuration.dart';
import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/widgets/typeahead_suggestion_highlighted.dart';
import 'package:bitacora/util/action_on_focus_widget.dart';
import 'package:bitacora/util/collection_selection/collection_selection.dart';
import 'package:bitacora/util/suggestion_typeahead/search_multi_pattern_repository_query.dart';
import 'package:bitacora/util/suggestion_typeahead/suggestion_typeahead_utils.dart';
import 'package:bitacora/util/trim_text_on_focus_lost.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:provider/provider.dart';

class EntryFormTagOption extends StatefulWidget {
  final EntryFormTagOptionController controller;

  const EntryFormTagOption({
    super.key,
    required this.controller,
  });

  @override
  State<EntryFormTagOption> createState() => _EntryFormTagOptionState();
}

class _EntryFormTagOptionState extends State<EntryFormTagOption> {
  final FocusNode _textFieldFocus = FocusNode();
  final TextEditingController _textFieldController = TextEditingController();
  bool _isShowingTextField = true;
  bool _shouldAutoFocus = true;

  @override
  void initState() {
    super.initState();
    _isShowingTextField = widget.controller.tags.value.isEmpty;
    _shouldAutoFocus = _isShowingTextField;
  }

  @override
  void dispose() {
    _textFieldFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final activeOrganization = context.watch<ActiveOrganization>();
    if (!activeOrganization.hasLoaded) {
      return const SizedBox();
    }

    final orgDbQueryScope = QueryScope(orgId: activeOrganization.value!.id);

    if (_shouldAutoFocus) {
      _shouldAutoFocus = false;
      Future.microtask(() => _textFieldFocus.requestFocus());
    }

    final actions = EntryFormTagOptionActions(
      controller: widget.controller,
      onAddTagFromTextField: _onAddTagFromTextField,
    );

    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: kFormVerticalSpacing / 2,
        horizontal: kFormHorizontalSpacing / 2,
      ),
      child: ChangeNotifierProvider<CollectionSelection<Tag>>.value(
        value: widget.controller.selection,
        builder: (context, _) => Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            ValueListenableBuilder<List<Tag>>(
              valueListenable: widget.controller.tags,
              builder: (context, tags, child) => Wrap(
                alignment: WrapAlignment.end,
                crossAxisAlignment: WrapCrossAlignment.center,
                children: [..._getTags(tags), if (child != null) child],
              ),
              child: _isShowingTextField ? null : actions,
            ),
            if (_isShowingTextField)
              Padding(
                padding: const EdgeInsets.only(left: 4, top: 2, bottom: 2),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    SizedBox(
                      width: 160,
                      child: ActionOnFocusWidget(
                        focusNode: _textFieldFocus,
                        onFocusChanged: (hasFocus) {
                          if (!hasFocus) {
                            setState(() {
                              _maybeAddTagFromTextField();
                              if (widget.controller.tags.value.isEmpty) {
                                widget.controller.isShowing.value = false;
                              }
                              _isShowingTextField = false;
                            });
                          }
                        },
                        child: Material(
                          color: Colors.black.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(4.0),
                          child: Padding(
                            padding: const EdgeInsets.all(6.0),
                            child: ValueListenableBuilder<List<Tag>>(
                              valueListenable: widget.controller.tags,
                              builder: (_, tags, ___) {
                                final textFieldConfiguration =
                                    TypeaheadTextFieldConfiguration(
                                  focusNode: _textFieldFocus,
                                  controller: _textFieldController,
                                  textInputAction: TextInputAction.done,
                                  textCapitalization: TextCapitalization.words,
                                  decoration: InputDecoration(
                                    border: InputBorder.none,
                                    enabledBorder: InputBorder.none,
                                    contentPadding: EdgeInsets.zero,
                                    hintText: AppLocalizations.of(context)!.tag,
                                  ),
                                );

                                return _EntryFormTagTypeahead(
                                  configuration: textFieldConfiguration,
                                  onSelected: _maybeAddTagFromTextField,
                                  tags: tags,
                                  queryScope: orgDbQueryScope,
                                );
                              },
                            ),
                          ),
                        ),
                      ),
                    ),
                    actions,
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  List<Widget> _getTags(List<Tag> tags) {
    return tags
        .map<Widget>((e) => EntryFormTag(
              tag: e,
              onTap: () {
                setState(() {
                  _maybeAddTagFromTextField();
                  widget.controller.removeTag(e);

                  _textFieldController.text = e.name!.value;
                  _isShowingTextField = true;
                  _textFieldFocus.requestFocus();
                });
              },
            ))
        .toList(growable: false);
  }

  void _onAddTagFromTextField() {
    setState(() {
      _isShowingTextField = true;
      _textFieldFocus.requestFocus();
    });
    _maybeAddTagFromTextField();
  }

  void _maybeAddTagFromTextField() {
    final text = _textFieldController.text.trim();
    if (text.isNotEmpty) {
      widget.controller.addTag(Tag(
        name: TagName(text),
        color: TagColor(getTagColor(text)),
      ));
      _textFieldController.text = '';
    }
  }
}

class _EntryFormTagTypeahead extends StatefulWidget {
  final TypeaheadTextFieldConfiguration configuration;
  final VoidCallback onSelected;
  final List<Tag> tags;
  final QueryScope queryScope;

  const _EntryFormTagTypeahead({
    required this.configuration,
    required this.onSelected,
    required this.tags,
    required this.queryScope,
  });

  @override
  State<_EntryFormTagTypeahead> createState() => _EntryFormTagTypeaheadState();
}

class _EntryFormTagTypeaheadState extends State<_EntryFormTagTypeahead> {
  String? currentPattern;

  @override
  Widget build(BuildContext context) {
    final db = context.read<Repository>();

    return TrimTextOnFocusLost(
      focusNode: widget.configuration.focusNode,
      controller: widget.configuration.controller,
      child: TypeAheadField<String>(
        controller: widget.configuration.controller,
        focusNode: widget.configuration.focusNode,
        onSelected: (value) {
          widget.configuration.controller.text = value;
          widget.onSelected();
        },
        builder: widget.configuration
            .copyWith(
                scrollPadding: const EdgeInsets.all(20)
                    .copyWith(bottom: MediaQuery.sizeOf(context).height * 0.3))
            .builder,
        itemBuilder: (c, suggestion) {
          return TypeaheadSuggestionHighlighted(
            pattern: currentPattern,
            suggestion: suggestion,
            highlightColor: Theme.of(context).colorScheme.primary,
          );
        },
        suggestionsCallback: (input) async {
          currentPattern = input;
          final patterns = defaultSearchPatternResolvers
              .map((e) => e(input))
              .toList(growable: false);

          return db.query(
            SearchMultiPatternRepositoryQuery(
              subQuery: TagSuggestionRepositoryQuery(widget.tags
                  .map<String>((e) => e.name!.dbValue)
                  .toList(growable: false)),
              patterns: patterns,
            ),
            context: db.context(queryScope: widget.queryScope),
          );
        },
        transitionBuilder: (_, __, box) => box,
        hideOnLoading: true,
        hideOnEmpty: true,
        hideOnError: true,
        retainOnLoading: true,
        hideOnSelect: true,
      ),
    );
  }
}
