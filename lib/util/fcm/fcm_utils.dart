import 'dart:async';
import 'dart:convert';

import 'package:bitacora/analytics/analytics_logger.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/bug_report/bug_report_service.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/application/error/error_logger.dart';
import 'package:bitacora/application/sync/background/background_sync_future_scheduler.dart';
import 'package:bitacora/application/sync/sync_trigger.dart';
import 'package:bitacora/domain/common/query/entry_common_db_queries.dart';
import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/translator.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:bitacora/infrastructure/db_lock.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/background_work/background_work_utils.dart';
import 'package:bitacora/util/fcm/fcm_organization_topic_repository_query.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';
import 'package:bitacora/util/workmanager/workmanager_utils.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/widgets.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';

class FcmUtils {
  factory FcmUtils() => inject(() => const FcmUtils._());

  const FcmUtils._();

  /// Returning [Future<void>] to make sure we await reading from
  /// SharedPreferences, but we don't actually wait for all the unsubscriptions.
  /// We don't clear the SharedPreferences key because the whole file is
  /// deleted elsewhere.
  Future<void> nuke() async {
    final prefs = await SharedPreferences.getInstance();
    final topics =
        prefs.getStringList(SharedPreferencesKeys.fcmTopicSubscriptions) ??
            <String>[];
    logger.d('fcm:topics nuke $topics');
    unawaited(Future.wait(
      topics.map((topic) async {
        await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
      }),
    ));
    // FIXME: Unsubscription not guaranteed. If user gets message from
    // unexpected topic, we should unsubscribe immediately.
  }

  Future<void> requestPermissions() async {
    final messaging = FirebaseMessaging.instance;

    final settings = await messaging.requestPermission(
      alert: true,
      announcement: false,
      badge: false,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: false,
    );

    logger.t('fcm:requestPermissions done ${settings.authorizationStatus}');
  }

  Future<void> foregroundHandler(
    BuildContext context,
    RemoteMessage payload,
  ) async {
    final activeSession = context.read<ActiveSession>();

    if (!activeSession.hasLoaded) {
      f() {
        if (activeSession.value != null) {
          _maybeHandleForegroundMessage(context, payload, activeSession);
          activeSession.removeListener(f);
        }
      }

      activeSession.addListener(f);
    } else {
      await _maybeHandleForegroundMessage(context, payload, activeSession);
    }
  }

  Future<void> _maybeHandleForegroundMessage(
    BuildContext context,
    RemoteMessage payload,
    ActiveSession activeSession,
  ) async {
    logger.i('fcm:foreground Handle message');
    await runZonedGuarded(() async {
      if (activeSession.value == null) {
        logger.i('fcm:foreground Ignoring notification due to null session.');
        return;
      }

      final session = activeSession.value!;
      final db = context.read<Repository>();
      final analyticsLogger = context.read<AnalyticsLogger>();
      final syncTrigger = context.read<SyncTrigger>();
      final apiTranslator = context.read<ApiTranslator>();
      final activeOrg = context.read<ActiveOrganization>();

      final organization = await _getLocalOrganizationFromTopic(payload, db);
      if (organization == null && payload.from != null) {
        logger.i(
          'fcm:foreground Ignoring notification from unexpected topic '
          '<${payload.from}>.',
        );
        return;
      }

      final message = json.decode(payload.data['message']);

      if (await _maybeSendBugReport(db, analyticsLogger, session, message)) {
        logger.i('fcm:foreground Bug report was maybe sent.');
        return;
      }

      if (!session.isValid) {
        logger
            .i('fcm:foreground Ignoring notification due to invalid session.');
        return;
      }

      if (await _maybeSuperSync(syncTrigger, session, message)) {
        logger.i('fcm:foreground Super Sync was maybe triggered.');
        return;
      }

      if (message['session_id'] == session.id) {
        logger.i('fcm:foreground Ignoring notification due to same session.');
        return;
      }

      if (await _maybeDeleteEntry(db, message) ||
          await _maybeSaveEntry(
              syncTrigger, db, organization, apiTranslator, message)) {
        logger.i('fcm:foreground Notification deleted or saved entry.');
        return;
      }

      if (await _maybePerformSync(
          syncTrigger, message, organization, () => activeOrg.value)) {
        logger.i('fcm:foreground perform sync.');
        return;
      }

      logger.i('fcm:foreground unknown message: $message.');
    }, (e, stack) => ErrorLogger().recordError(e, stack));
  }

  @pragma('vm:entry-point')
  static Future<void> backgroundHandler(RemoteMessage payload) async {
    await BackgroundWorkUtils().wrapWork<Null>(DbLockKey.fcm, (context) async {
      logger.i('fcm:background Start');
      final analyticsLogger = context.read<AnalyticsLogger>();

      final message = json.decode(payload.data['message']);
      final db = context.read<Repository>();
      final session = await context.read<Future<Session?>>();

      final organization = await _getLocalOrganizationFromTopic(payload, db);
      if (organization == null && payload.from != null) {
        logger.i(
          'fcm:background Ignoring notification from unexpected topic '
          '<${payload.from}>.',
        );
        return;
      }

      if (session == null) {
        logger.i('fcm:background Ignoring notification due to null session.');
        return;
      }

      if (await _maybeSendBugReport(db, analyticsLogger, session, message)) {
        logger.i('fcm:background Bug report was maybe sent.');
        return;
      }

      if (!session.isValid) {
        logger
            .i('fcm:background Ignoring notification due to invalid session.');
        return;
      }

      if (await _maybeSuperSync(null, session, message)) {
        logger.i('fcm:background Super Sync was maybe triggered.');
        return;
      }

      if (message['session_id'] == session.id) {
        logger.i('fcm:background Ignoring notification due to same session.');
        return;
      }

      final translator = ApiTranslator();
      final prefs = await SharedPreferences.getInstance();
      if (await _maybeDeleteEntry(db, message) ||
          await _maybeSaveEntry(null, db, organization, translator, message)) {
        logger.i('fcm:background Marking entry repository as dirty.');
        await prefs.setBool(
            SharedPreferencesKeys.hasDirtyEntryRepository, true);
        return;
      }

      if (await _maybePerformSync(
          null, message, organization, context.read<Future<Organization?>>)) {
        logger
            .i('fcm:background Perform Sync Registering background sync task.');
        return;
      }

      logger.i('fcm:background unknown message: $message.');
    });
  }

  static Future<bool> _maybeSendBugReport(
    Repository db,
    AnalyticsLogger analyticsLogger,
    Session session,
    Map<String, dynamic> message,
  ) async {
    if (message['send_bug_report'] ?? false) {
      if (message['user_id'] == session.user.remoteId!.apiValue) {
        logger.i(
          'fcm Sending bug report'
          ' notify:${message['show_notification']}',
        );
        await BugReportService().createAndSend(
          db,
          session,
          analyticsLogger,
          description: 'FCM bug report $message',
          showNotification: message['show_notification'] ?? false,
        );
      }
      return true;
    }
    return false;
  }

  static Future<bool> _maybeSuperSync(
    SyncTrigger? syncTrigger,
    Session session,
    Map<String, dynamic> message,
  ) async {
    if (!(message['super_sync'] ?? false)) {
      return false;
    }

    if (message['user_id'] != null &&
        message['user_id'] != session.user.remoteId!.apiValue) {
      return false;
    }

    if (syncTrigger != null) {
      syncTrigger.trigger(const SyncTriggerEvent(
        SyncTriggerSource.notification,
        SyncTriggerMode.superSync,
      ));
    } else {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(SharedPreferencesKeys.hasPendingSuperSync, true);
      await WorkmanagerUtils().registerOneOffTask(
        WorkmanagerTask.backgroundSync,
        initialDelay: kBackgroundSyncDelay,
      );
    }

    return true;
  }

  static Future<bool> _maybeDeleteEntry(
      Repository db, Map<String, dynamic> message) async {
    final entryMap = message['entry'];
    final isDeleted = entryMap?['deleted'] ?? false;
    if (!isDeleted) {
      return false;
    }

    final remoteId = RemoteId(entryMap['id']);
    final entryToDelete =
        await db.query(EntryIdRepositoryQuery(remoteId: remoteId));
    if (entryToDelete == null) {
      // Pretend we deleted the entry to avoid sync
      return true;
    }

    await db.entry.delete(db.context(), entryToDelete.id!, requestSync: false);
    return true;
  }

  static Future<bool> _maybeSaveEntry(
    SyncTrigger? syncTrigger,
    Repository db,
    Organization? organization,
    Translator translator,
    Map<String, dynamic> message,
  ) async {
    if (message['entry'] == null || organization == null) {
      return false;
    }

    final entry = translator.entry.fromMap(
        {...message['entry'], 'organization_id': message['organization_id']});

    if (await _shouldSaveEntry(db, entry)) {
      await db.entry.save(
        db.context(queryScope: QueryScope(orgId: organization.id)),
        entry,
      );
      return true;
    }

    return false;
  }

  static Future<bool> _shouldSaveEntry(Repository db, Entry entry) async {
    if (entry.extension!.extensionType == ExtensionType.progresslog) {
      // Check if parent entry exists in db
      final parentEntry = await db.query(
        EntryIdRepositoryQuery(remoteId: entry.progresslog!.entry!.remoteId!),
      );
      return parentEntry != null;
    }

    // FIXME: try to save syncable project
    final projects = await db.query(
      ProjectIdsRepositoryQuery(
        remoteIds: entry.projects.map<RemoteId>((e) => e.remoteId!).toList(),
      ),
    );

    for (final project in projects) {
      if (project == null) {
        return false;
      }
    }

    return true;
  }

  static Future<bool> _maybePerformSync(
    SyncTrigger? syncTrigger,
    Map<String, dynamic> message,
    Organization? topicOrganization,
    FutureOr<Organization?> Function() activeOrg,
  ) async {
    final activeOrgRemoteId = (await activeOrg())?.remoteId!;
    if (activeOrgRemoteId != topicOrganization?.remoteId) {
      logger.i('fcm Will not perform sync as topic org != active org');
      return false;
    }

    if (!(message['perform_sync'] ?? message['entry'] != null)) {
      return false;
    }

    await _performSync(syncTrigger);
    return true;
  }

  static Future<void> _performSync(SyncTrigger? syncTrigger) async {
    if (syncTrigger != null) {
      syncTrigger.trigger(const SyncTriggerEvent(
        SyncTriggerSource.notification,
        SyncTriggerMode.fullSync,
      ));
    } else {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(SharedPreferencesKeys.hasPendingSync, true);
      await WorkmanagerUtils().registerOneOffTask(
        WorkmanagerTask.backgroundSync,
        initialDelay: kBackgroundSyncDelay,
      );
    }
  }

  // FIXME: Improve Query with topic param.
  static Future<Organization?> _getLocalOrganizationFromTopic(
      RemoteMessage payload, Repository db) async {
    final orgList = await db.query(const FcmOrganizationTopicRepositoryQuery());
    final tierPrefix = AppConfig().apiMode == ApiMode.prod ? '' : 's_';
    for (final org in orgList) {
      if ('/topics/$tierPrefix${org.remoteId!.value}' == payload.from) {
        return org;
      }
    }
    return null;
  }

  Future<void> syncSubscriptions(List<String> topics) async {
    logger.d('fcm:topics syncing subscriptions... $topics');
    // FIXME: subscribing multiple times to <NAME_EMAIL>/123456789
    final prefs = await SharedPreferences.getInstance();
    final currentTopics =
        prefs.getStringList(SharedPreferencesKeys.fcmTopicSubscriptions) ??
            <String>[];
    final updatedTopics = [...currentTopics];

    // Subscribe to new topics...
    await Future.wait(
      topics.map((topic) async {
        if (currentTopics.contains(topic)) {
          return;
        }

        logger.d('fcm:topics subscribing to $topic');
        await FirebaseMessaging.instance.subscribeToTopic(topic);
        updatedTopics.add(topic);
      }),
    );
    await prefs.setStringList(
      SharedPreferencesKeys.fcmTopicSubscriptions,
      updatedTopics,
    );

    // Unsubscribe from old topics...
    await Future.wait(
      currentTopics.map((topic) async {
        if (topics.contains(topic)) {
          return;
        }

        logger.d('fcm:topics unsubscribing from $topic');
        await FirebaseMessaging.instance.unsubscribeFromTopic(topic);
        updatedTopics.remove(topic);
      }),
    );
    await prefs.setStringList(
      SharedPreferencesKeys.fcmTopicSubscriptions,
      updatedTopics,
    );
    logger.d('fcm:topics subscriptions synced');
  }
}
