import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class EntryFilterSearchField extends StatefulWidget {
  final void Function(String) onSearch;

  const EntryFilterSearchField({super.key, required this.onSearch});

  @override
  State<EntryFilterSearchField> createState() => _EntryFilterSearchFieldState();
}

class _EntryFilterSearchFieldState extends State<EntryFilterSearchField> {
  final ValueNotifier<bool> hasText = ValueNotifier(false);
  final TextEditingController controller = TextEditingController();

  @override
  void initState() {
    super.initState();
    controller.addListener(_onTextChanged);
  }

  void _onTextChanged() {
    hasText.value = controller.text.isNotEmpty;
  }

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      controller: controller,
      keyboardType: TextInputType.text,
      textAlignVertical: TextAlignVertical.center,
      autofocus: true,
      decoration: InputDecoration(
        hintText: AppLocalizations.of(context)!.search,
        suffixIconConstraints: const BoxConstraints(),
        suffixIcon: ValueListenableBuilder<bool>(
          valueListenable: hasText,
          builder: (_, value, child) => value ? child! : const SizedBox(),
          child: IconButton(
            constraints: const BoxConstraints(minWidth: 36, minHeight: 36),
            onPressed: () {
              controller.text = '';
              widget.onSearch('');
            },
            icon: const Icon(Icons.clear),
          ),
        ),
      ),
      onChanged: widget.onSearch,
    );
  }
}
