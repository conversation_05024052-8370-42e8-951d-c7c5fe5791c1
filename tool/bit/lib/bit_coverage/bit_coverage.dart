import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';
const kCoverageOutputDirectory = 'coverage/html/';

const _help = '''
A tool for analyzing bitacora_flutter/lib test coverage.

Runs `flutter test --coverage`.
Filters unnecessary sources.
Generates report html.
Opens html.

Usage:
 
  bit coverage

''';

Future<int> bitCoverage([List<String> args = const <String>[]]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  writeln('Preparing coverage report...', TextStyle.highlighted);

  final tests = await _runTests();
  if (!tests) {
    writeln('Failed to run tests with coverage.', TextStyle.error);
    return 1;
  }

  final filter = await _filterCoverage();
  if (!filter) {
    writeln('Failed to remove code from coverage analysis.', TextStyle.error);
    return 1;
  }

  final genHtml = await _genHtml();
  if (!genHtml) {
    writeln('Failed to generate html with coverage data.', TextStyle.error);
    return 1;
  }

  await _openHtml();
  return 0;
}

Future<bool> _runTests() async {
  final coverage = await startProcess(
    'flutter',
    [
      'test',
      '--coverage',
    ],
    ProcessStartMode.inheritStdio,
  );
  return await coverage.exitCode == 0;
}

Future<bool> _filterCoverage() async {
  writeln('Filtering sources...');
  final lcov = await startProcess(
    'lcov',
    [
      '--remove',
      'coverage/lcov.info',
      'dev/*',
      'lib/l10n/*',
      'lib/domain/*',
      'lib/amplifyConfiguration.dart',
      'lib/generated_plugin_registrant.dart',
      '--ignore-errors',
      'unused',
      '-o',
      'coverage/new_lcov.info',
    ],
    ProcessStartMode.inheritStdio,
  );
  return await lcov.exitCode == 0;
}

Future<bool> _genHtml() async {
  writeln('Generating html...');
  final genhtml = await startProcess(
    'genhtml',
    ['coverage/new_lcov.info', '-o', kCoverageOutputDirectory],
    ProcessStartMode.inheritStdio,
  );
  return await genhtml.exitCode == 0;
}

Future<void> _openHtml() async {
  writeln('Opening html...');
  await startProcess(
    'open',
    ['${kCoverageOutputDirectory}index.html'],
  );
}
