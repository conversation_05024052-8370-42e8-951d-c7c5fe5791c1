import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field/custom_field_fields_builder.dart';
import 'package:bitacora/infrastructure/custom_field/custom_field_db_contract.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';

class CustomFieldDbFieldsBuilder extends DbFieldsBuilder
    implements CustomFieldFieldsBuilder {
  CustomFieldDbFieldsBuilder() {
    _id();
  }

  CustomFieldDbContract get contract => const CustomFieldDbContract();

  CustomFieldDbFieldsBuilder _id() {
    addField(
      CustomFieldField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldDbFieldsBuilder remoteId() {
    addField(
      CustomFieldField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldFieldsBuilder name() {
    addField(
      CustomFieldField.name,
      DbField(
        column: contract.name,
        valueBuilder: (v) => CustomFieldName(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldFieldsBuilder type() {
    addField(
      CustomFieldField.type,
      DbField(
        column: contract.type,
        valueBuilder: (v) => CustomFieldType.fromDbValue(v),
      ),
    );
    return this;
  }

  @override
  CustomFieldFieldsBuilder deletedAt() {
    addField(
      CustomFieldField.deletedAt,
      DbField(
        column: contract.deletedAt,
        valueBuilder: (v) => CustomFieldDeletedAt(
            v == null ? null : DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  CustomFieldFieldsBuilder updatedAt() {
    addField(
      CustomFieldField.updatedAt,
      DbField(
        column: contract.updatedAt,
        valueBuilder: (v) =>
            CustomFieldUpdatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  CustomFieldFieldsBuilder createdAt() {
    addField(
      CustomFieldField.createdAt,
      DbField(
        column: contract.createdAt,
        valueBuilder: (v) =>
            CustomFieldCreatedAt(DateTime.fromMicrosecondsSinceEpoch(v)),
      ),
    );
    return this;
  }

  @override
  CustomFieldFieldsBuilder allowedValues(Fields fields) {
    addField(
      CustomFieldField.allowedValues,
      DbField(
          key: CustomFieldField.allowedValues,
          column: contract.id,
          nestedFields: fields as DbFields,
          nestedBuilder: (nestedContext, value) => nestedContext
              .db.customFieldAllowedValue
              .findAll(nestedContext, LocalId(value))),
    );
    return this;
  }

  @override
  CustomFieldFieldsBuilder parent(Fields fields) {
    addField(
      CustomFieldField.parent,
      DbField(
          key: CustomFieldField.parent,
          column: contract.parentId,
          nestedFields: fields as DbFields,
          nestedBuilder: (nestedContext, value) =>
              nestedContext.db.customField.find(nestedContext, LocalId(value))),
    );
    return this;
  }

  @override
  CustomFieldFieldsBuilder organization(Fields fields) {
    addField(
      CustomFieldField.organization,
      DbField(
        key: CustomFieldField.organization,
        column: contract.organizationId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.organization.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
