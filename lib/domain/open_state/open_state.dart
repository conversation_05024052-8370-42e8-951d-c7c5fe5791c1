import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/open_state/value/open_state_end_day.dart';
import 'package:bitacora/domain/open_state/value/open_state_progress.dart';
import 'package:bitacora/domain/open_state/value/open_state_progressive.dart';
import 'package:bitacora/domain/open_state/value/open_state_start_day.dart';

export 'package:bitacora/domain/open_state/value/open_state_end_day.dart';
export 'package:bitacora/domain/open_state/value/open_state_progress.dart';
export 'package:bitacora/domain/open_state/value/open_state_progressive.dart';
export 'package:bitacora/domain/open_state/value/open_state_start_day.dart';

class OpenState extends Model {
  final OpenStateProgress? progress;
  final OpenStateProgressive? progressive;
  final OpenStateStartDay? startDay;
  final OpenStateEndDay? endDay;

  const OpenState({
    super.id,
    this.progress,
    this.progressive,
    this.startDay,
    this.endDay,
  });

  OpenState copyWith({
    LocalId? id,
    OpenStateStartDay? startDay,
  }) {
    return OpenState(
      id: id ?? this.id,
      progress: progress,
      progressive: progressive,
      startDay: startDay ?? this.startDay,
      endDay: endDay,
    );
  }

  @override
  Map<Field, dynamic> get fields => {
        OpenStateField.id: id,
        OpenStateField.progress: progress,
        OpenStateField.progressive: progressive,
        OpenStateField.startDay: startDay,
        OpenStateField.endDay: endDay,
      };
}

enum OpenStateField with Field {
  id,
  progress,
  progressive,
  startDay,
  endDay,
}

const openStateNestedModelFields = <Field>{};
