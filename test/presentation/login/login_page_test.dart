import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/cache/auth/active_session.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/presentation/login/login_group.dart';
import 'package:bitacora/presentation/login/login_page.dart';
import 'package:bitacora/presentation/login/reset_password_group.dart';
import 'package:bitacora/presentation/login/signup_group.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:provider/provider.dart';

import '../../application/cache/auth/mocks.dart';
import '../../application/mocks.dart';

void main() {
  Widget testApp(Widget child) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider<ActiveSession>(
          create: (context) => mockActiveSession(session: mockSession()),
        ),
      ],
      child: MaterialApp(
        home: Localizations(
          locale: const Locale('en'),
          delegates: AppLocalizations.localizationsDelegates,
          child: child,
        ),
      ),
    );
  }

  group('$LoginPage widget test', () {
    testWidgets('Display login', (tester) async {
      await tester.pumpWidget(testApp(const LoginPage()));

      expect(find.byType(LoginGroup), findsOneWidget);
      expect(find.byType(SignupGroup), findsNothing);
      expect(find.byType(ResetPasswordGroup), findsNothing);
    });

    testWidgets('Tap Reset', (tester) async {
      await tester.pumpWidget(testApp(const LoginPage()));

      final resetPasswordTextButton = find.text('Reset');
      await tester.tap(resetPasswordTextButton);
      await tester.pumpAndSettle();

      expect(find.byType(ResetPasswordGroup), findsOneWidget);
      expect(find.byType(LoginGroup), findsNothing);
      expect(find.byType(SignupGroup), findsNothing);
    });

    testWidgets('Tap Signup', (tester) async {
      await tester.pumpWidget(testApp(const LoginPage()));

      final signupTextButton = find.text('Signup');
      await tester.tap(signupTextButton);
      await tester.pumpAndSettle();

      expect(find.byType(SignupGroup), findsOneWidget);
      expect(find.byType(LoginGroup), findsNothing);
      expect(find.byType(ResetPasswordGroup), findsNothing);
    });

    testWidgets('Display package name', (tester) async {
      const packageName = 'com.bitacora.test';
      const version = '9.9.9';
      const buildNumber = '8';
      final appConfig = mockAppConfig(shouldShowPackageInfo: true);
      PackageInfo.setMockInitialValues(
        appName: appConfig.appName,
        packageName: packageName,
        version: version,
        buildNumber: buildNumber,
        buildSignature: '',
      );

      await withInjected<AppConfig>(
        appConfig,
        () => tester.pumpWidget(testApp(const LoginPage())),
      );
      await tester.pump();

      expect(find.text('($packageName v$version)'), findsOneWidget);
    });

    testWidgets('Don\'t display package name', (tester) async {
      const packageName = 'com.bitacora.test';
      const version = '9.9.9';
      const buildNumber = '8';
      final appConfig = mockAppConfig(shouldShowPackageInfo: false);
      PackageInfo.setMockInitialValues(
        appName: appConfig.appName,
        packageName: packageName,
        version: version,
        buildNumber: buildNumber,
        buildSignature: '',
      );

      await withInjected<AppConfig>(
        appConfig,
        () => tester.pumpWidget(testApp(const LoginPage())),
      );
      await tester.pump();

      expect(find.text('($packageName v$version)'), findsNothing);
    });
  });
}
