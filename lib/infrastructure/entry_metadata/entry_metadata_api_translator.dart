import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';

class EntryMetadataApiTranslator implements ModelTranslator<EntryMetadata> {
  const EntryMetadataApiTranslator();

  @override
  EntryMetadata fromMap(Map<String, dynamic> data) {
    return EntryMetadata(
      remoteId: data['id'] != null ? RemoteId(data['id']) : null,
      value: data['value'] != null ? EntryMetadataValue(data['value']) : null,
      type: data['type'] != null
          ? EntryMetadataType.fromApiValue(data['type'])
          : null,
      entry: data['entry_id'] != null
          ? Entry(remoteId: RemoteId(data['entry_id']))
          : null,
    );
  }

  @override
  Map<String, dynamic> toMap(EntryMetadata model) {
    return {
      'type': model.type?.apiValue,
      'value': model.value?.apiValue,
    };
  }
}
