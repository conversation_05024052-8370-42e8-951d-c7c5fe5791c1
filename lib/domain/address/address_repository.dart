import 'package:bitacora/domain/address/address.dart';
import 'package:bitacora/domain/address/address_post_fields_builder.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/repository_table.dart';
import 'package:bitacora/domain/common/value_object/local_id.dart';

abstract class AddressRepository<C extends RepositoryQueryContext,
    F extends AddressFieldsBuilder> extends RepositoryTable<Address, C, F> {
  Future<List<Address>> findAll(C context, LocalId personDetailId);
}
