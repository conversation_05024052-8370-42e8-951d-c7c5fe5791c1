import 'package:bitacora/domain/user_invite/user_invite.dart';
import 'package:bitacora/domain/user_invite/user_invite_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/user_invite/user_invite_db_contract.dart';
import 'package:bitacora/infrastructure/user_invite/user_invite_db_fields_builder.dart';
import 'package:bitacora/infrastructure/user_invite/user_invite_db_translator.dart';
import 'package:collection/collection.dart';

class UserInviteDbTable extends DbTable<UserInvite, UserInviteDbFieldsBuilder>
    implements UserInviteRepository<DbContext, UserInviteDbFieldsBuilder> {
  UserInviteDbTable() : super(isSyncable: false);

  @override
  UserInviteDbContract get contract => const UserInviteDbContract();

  @override
  DbTranslator<UserInvite> get translator => const UserInviteDbTranslator();

  @override
  UserInviteDbFieldsBuilder get fieldsBuilder => UserInviteDbFieldsBuilder();

  @override
  Future<List<UserInvite>> findAll(DbContext context) async {
    return query(
      context,
      where: '${contract.orgId} = ?',
      whereArgs: [context.queryScope!.orgId!.dbValue],
      orderBy: '${contract.createdAt} DESC',
    );
  }

  @override
  Future<void> deleteUserInvite(
    DbContext context,
    String email,
  ) async {
    assert(context.queryScope!.byOrg);

    final userInvites = await context.db.userInvite.findAll(
      context.copyWith(
        fields: context.db.userInvite.fieldsBuilder.email().build(),
      ),
    );

    final userInvite = userInvites.firstWhereOrNull(
        (e) => e.email?.dbValue.toLowerCase() == email.toLowerCase());

    if (userInvite != null) {
      await context.db.userInvite.delete(
        context,
        userInvite.id!,
      );
    }
  }
}
