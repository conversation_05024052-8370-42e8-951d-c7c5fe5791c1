import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/signature/signature.dart';
import 'package:bitacora/domain/user/user.dart';

class SignatureApiTranslator implements ModelTranslator<Signature> {
  const SignatureApiTranslator();

  @override
  Signature fromMap(Map<String, dynamic> data) {
    return Signature(
      remoteId: RemoteId(data['id']),
      s3Key: SignatureS3Key(data['signature_s3_key']),
      ownerName: SignatureOwnerName(data['person_name']),
      ownerEmail: SignatureOwnerEmail(data['email']),
      comments: SignatureComments(data['comments']),
      status: SignatureStatus.fromApi(data['status']),
      location: SignatureLocation(data['location']),
      entry: Entry(remoteId: RemoteId(data['project_entry_id'])),
      user: data['user_id'] == null
          ? null
          : User(remoteId: RemoteId(data['user_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(Signature model) => {
        'person_name': model.ownerName!.apiValue,
        'email': model.ownerEmail!.apiValue,
        'comments': model.comments!.apiValue,
        'status': model.status!.apiValue,
        'project_entry_id': model.entry!.remoteId!.apiValue,
      };
}
