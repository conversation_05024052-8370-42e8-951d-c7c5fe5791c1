import 'dart:math';

import 'package:flutter/material.dart';

const Duration kCircleCheckAnimationDuration = Duration(milliseconds: 300);

class CircleCheck extends StatefulWidget {
  final bool isSelected;
  final Color? color;

  const CircleCheck({
    super.key,
    this.isSelected = false,
    this.color,
  });

  @override
  State<CircleCheck> createState() => _CircleCheckState();
}

class _CircleCheckState extends State<CircleCheck>
    with SingleTickerProviderStateMixin {
  late final AnimationController _controller = AnimationController(
    vsync: this,
    duration: kCircleCheckAnimationDuration,
  );

  @override
  void initState() {
    super.initState();
    _controller.animateTo(widget.isSelected ? 1 : 0);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(covariant CircleCheck oldWidget) {
    if (oldWidget.isSelected != widget.isSelected) {
      _controller.animateTo(widget.isSelected ? 1 : 0);
    }
    super.didUpdateWidget(oldWidget);
  }

  @override
  Widget build(BuildContext context) {
    return CustomPaint(
      painter: _CustomPainter(
        _controller,
        widget.color ?? Theme.of(context).colorScheme.primary,
        Theme.of(context).colorScheme.onSurface,
      ),
      size: Size.infinite,
    );
  }
}

class _CustomPainter extends CustomPainter {
  final Animation<double> listenable;
  final Paint _paint = Paint();
  final Color color;
  final Color fadeColor;

  _CustomPainter(this.listenable, this.color, this.fadeColor)
      : super(repaint: listenable);

  @override
  void paint(Canvas canvas, Size size) {
    final center = _center(size);
    final baseRadio = _radio(size);
    _paint.color = Color.lerp(fadeColor, color, _pulse())!;
    canvas.drawCircle(
      center,
      baseRadio * _pulse(),
      _paint,
    );
  }

  @override
  bool shouldRepaint(_CustomPainter oldDelegate) {
    return oldDelegate.listenable.value != listenable.value ||
        oldDelegate.color != color;
  }

  double _radio(Size size) {
    return min(size.width, size.height) * 0.5;
  }

  Offset _center(Size size) {
    return Offset(size.width * 0.5, size.height * 0.5);
  }

  double _pulse() {
    return listenable.value;
  }
}
