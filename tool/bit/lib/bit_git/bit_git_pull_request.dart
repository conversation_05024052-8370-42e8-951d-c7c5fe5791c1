import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/ascii/box_decorator.dart';
import 'package:bit/command_line.dart';
import 'package:bit/git_utils.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';

const _help = '''
A tool for managing pull requests in github.

If a pull request already exists for branch, provides options,
otherwise prompts to create a new pull request.

Usage: 

  bit git pull-request
   
Options:

  refill  - - ---  Reset title, body and base branch to pull request.
  merge  -- --  -  Refill, merge and delete branch. Checkout and pull master.

''';

Future<int> bitGitPullRequest([
  List<String> args = const <String>[],
]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  if (args.isNotEmpty) {
    writeln(_help, TextStyle.help);
    return 1;
  }

  final status = await _assertStatus();
  if (status == 1) {
    return 1;
  }

  final current = await _getCurrentPullRequest();
  if (current != null) {
    writeln('\nPull Request #$current ', TextStyle.highlighted);
    writeln('https://github.com/Bitacora-io/bitacora_flutter/pull/$current');
    return _promptPullRequestOptions(current);
  }

  return _createPullRequest();
}

Future<int> _assertStatus() async {
  final baseBranch = await gitGetPreviousBranch();
  if (baseBranch == null || baseBranch.isEmpty) {
    final isLastCommitInMaster = await gitIsPrevCommitInBranch();
    if (isLastCommitInMaster) {
      writeln('Please rebase on to master.', TextStyle.error);
    } else {
      writeln('No base branch found.', TextStyle.error);
      return 1;
    }
  }

  final isClean = await gitIsWorkingTreeClean();
  if (isClean != 0) {
    writeln('Interrupting. Working tree is not clean.', TextStyle.error);
    return 1;
  }

  final isPushed = await gitAssertBranchPushed();
  if (!isPushed) {
    writeln('Interrupting. Local branch differs from remote.', TextStyle.error);
    return 1;
  }

  final isBasePushed = await gitAssertBranchPushed(baseBranch);
  if (!isBasePushed) {
    writeln(
      'Interrupting. Local base branch differs from remote.',
      TextStyle.error,
    );
    return 1;
  }
  return 0;
}

Future<String?> _getBaseBranch() async {
  final previous = await gitGetPreviousBranch();
  if (previous != null) {
    return previous;
  }

  final isLastCommitInMaster = await gitIsPrevCommitInBranch();
  if (isLastCommitInMaster) {
    return 'master';
  }

  return null;
}

Future<int?> _getCurrentPullRequest() async {
  final view = await startProcess('gh', ['pr', 'view']);
  final result = await view.exitCode;
  if (result != 0) {
    return null;
  }
  final output = await getProcessOutput(view);
  final regexp = RegExp(
    r'https://github.com/Bitacora-io/bitacora_flutter/pull/(.*)',
  );
  final match = regexp.firstMatch(output);
  return int.parse(match!.group(1)!);
}

Future<int> _createPullRequest() async {
  writeln('Creating a pull request in GitHub...');

  final pullRequest = await _PullRequest.load();
  if (pullRequest == null) {
    return 1;
  }

  pullRequest.present();

  final request = await CommandLine.confirm(
    () => runProcess(
      'gh',
      [
        'pr',
        'create',
        '--fill',
        if (pullRequest.reviewer != null) '-r',
        if (pullRequest.reviewer != null) pullRequest.reviewer!,
        '-B',
        pullRequest.baseBranch,
      ],
      ProcessStartMode.inheritStdio,
    ),
  ).present();
  return request ?? 1;
}

Future<String?> _determineReviewer() async {
  final api = await startProcess(
    'gh',
    [
      'api',
      '-H',
      'Accept: application/vnd.github+json',
      '-H',
      'X-GitHub-Api-Version: 2022-11-28',
      '/orgs/Bitacora-io/members'
    ],
  );
  await api.exitCode;
  final response = await api.stdout.transform(utf8.decoder).join();
  final members = (jsonDecode(response) as List).map<String>((m) => m['login']);
  final result =
      await CommandLine.select(members, title: 'Select reviewer:').present();
  if (result == null) {
    writeln('No reviewer selected.', TextStyle.error);
  }
  return result;
}

Future<int> _promptPullRequestOptions(int pullRequestId) async {
  final pullRequest = await _PullRequest.load(pullRequestId);
  if (pullRequest == null) {
    return 1;
  }
  pullRequest.present();

  return await CommandLine({
        'refill': () => _refill(pullRequest),
        if (pullRequest.baseBranch == 'master')
          'merge': () => _merge(pullRequest),
      }).present() ??
      1;
}

Future<int> _refill(_PullRequest pullRequest) async {
  return runProcess(
    'gh',
    [
      'pr',
      'edit',
      '-t',
      pullRequest.title,
      '-b',
      pullRequest.body,
      '-B',
      pullRequest.baseBranch,
    ],
  );
}

Future<int> _merge(_PullRequest pullRequest) async {
  final refill = await _refill(pullRequest);
  if (refill != 0) {
    writeln('Failed to refill.', TextStyle.error);
    return 1;
  }

  write('\nThis will merge and delete ', TextStyle.highlighted);
  write(pullRequest.currentBranch, TextStyle.focus);
  writeln('.', TextStyle.highlighted);

  // FIXME determine if reviewed.
  final request = await CommandLine.confirm(
    () => runProcess(
      'gh',
      [
        'pr',
        'merge',
        '-d',
        '-r',
      ],
      ProcessStartMode.inheritStdio,
    ),
  ).present();
  return request ?? 1;
}

class _PullRequest {
  final String baseBranch;
  final String currentBranch;
  final String title;
  final String body;
  final String? reviewer;
  final Iterable<_Review>? reviews;

  _PullRequest(
    this.baseBranch,
    this.currentBranch,
    this.title,
    this.body,
    this.reviewer,
    this.reviews,
  );

  static Future<_PullRequest?> load([int? id]) async {
    final baseBranch = await _getBaseBranch();
    final currentBranch = await gitGetCurrentBranch();

    if (baseBranch == null) {
      writeln('Unable to determine base branch.', TextStyle.error);
      return null;
    }

    final reviewer = id != null ? null : await _determineReviewer();
    final lastCommitMessage = await gitGetPreviousCommitMessage();
    final split = lastCommitMessage.split('\n');
    final title = split.first;
    split.removeAt(0);
    final body = split.join('\n').trim();
    final reviews = id != null ? await _Review.load(id) : null;

    return _PullRequest(
      baseBranch,
      currentBranch,
      title,
      body,
      reviewer,
      reviews,
    );
  }

  void present() {
    final decorator = BoxDecorator();
    decorator.write('$currentBranch > $baseBranch', TextStyle.focus);
    decorator.divide();
    decorator.writeln(title);
    decorator.write('\n$body');
    if (reviewer != null) {
      decorator.divide();
      decorator.write('Reviewer: ');
      decorator.write(reviewer!, TextStyle.highlighted);
    }
    if (reviews?.isNotEmpty ?? false) {
      decorator.divide();
      decorator.writeln('Reviews: \n');
      var i = 0;
      for (final review in reviews!) {
        if (i++ > 0) {
          decorator.writeln('');
        }
        decorator.write('${review.author}: ', TextStyle.highlighted);
        decorator.write(review.state, review.textStyle);
        if (review.body?.isNotEmpty ?? false) {
          decorator.write('\n\n${review.body}');
        }
      }
    }
    decorator.print();
  }
}

class _Review {
  final String author;
  final String state;
  final String? body;

  _Review(this.author, this.state, this.body);

  static Future<Iterable<_Review>> load(int id) async {
    final api = await startProcess(
      'gh',
      [
        'pr',
        'view',
        '$id',
        '--json',
        'reviews',
      ],
    );
    await api.exitCode;
    final response = await api.stdout.transform(utf8.decoder).join();
    final reviews = (jsonDecode(response)['reviews'] as List).map<_Review>(
      (m) => _Review(
        m['author']['login'],
        m['state'],
        m['body'],
      ),
    );
    return reviews;
  }

  TextStyle get textStyle {
    if (state == 'APPROVED') {
      return TextStyle.focus;
    } else if (state == 'CHANGES_REQUESTED') {
      return TextStyle.error;
    }
    return TextStyle.help;
  }
}
