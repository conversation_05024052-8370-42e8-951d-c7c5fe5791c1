import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/custom_field/custom_field.dart';
import 'package:bitacora/domain/custom_field_allowed_value/custom_field_allowed_value.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_label.dart';
import 'package:bitacora/domain/custom_field_allowed_value/value/custom_field_allowed_value_value.dart';
import 'package:bitacora/domain/location_tracking/location_tracking.dart';

class CustomFieldAllowedValueApiTranslator
    implements ModelTranslator<CustomFieldAllowedValue> {
  const CustomFieldAllowedValueApiTranslator();

  @override
  CustomFieldAllowedValue fromMap(Map<String, dynamic> data) {
    return CustomFieldAllowedValue(
      remoteId: RemoteId(data['id']),
      label: CustomFieldAllowedValueLabel(data['label']),
      value: CustomFieldAllowedValueValue(data['value']),
      customField: CustomField(id: LocalId(data['custom_field_id'])),
      parent: data['parent_id'] == null
          ? null
          : CustomFieldAllowedValue(remoteId: RemoteId(data['parent_id'])),
    );
  }

  @override
  Map<String, dynamic> toMap(CustomFieldAllowedValue model) {
    throw UnimplementedError();
  }
}
