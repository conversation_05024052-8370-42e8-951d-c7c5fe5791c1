import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/bit_version_bump/version.dart';
import 'package:bit/environment.dart';
import 'package:bit/process.dart';

Future<int> uploadSymbols(Version version) async {
  writeln('Uploading symbols...', TextStyle.highlighted);

  final crashlyticsBit4a = await _uploadBit4aToCrashlytics(version);
  if (crashlyticsBit4a != 0) {
    writeln('Failed to upload to crashlytics.', TextStyle.error);
  }

  final symbolsToSentry = await _uploadSymbolsToSentry();
  if (symbolsToSentry != 0) {
    writeln('Failed to upload symbols to Sentry.', TextStyle.error);
  }

  final dataRepoPush = await wrapWorkingDirectory(
    kDataRepositoryDirectory,
    () async {
      await runProcess('git', ['add', '.']);
      final result = await runProcess('git', ['commit', '-m', '$version']);
      if (result == 0) {
        return runProcess('git', ['push']);
      }
      return result;
    },
  );
  if (dataRepoPush != 0) {
    writeln('Failed to upload to data repository.', TextStyle.error);
  }

  return crashlyticsBit4a != 0 ? crashlyticsBit4a : dataRepoPush;
}

Future<int> _uploadBit4aToCrashlytics(Version version) {
  return runProcess('firebase', [
    'crashlytics:symbols:upload',
    '--app=1:690664140824:android:00b01748cb7b97cd3cd36d',
    '../bitacora_flutter_data/symbols/v$version/bit4a',
  ], ProcessStartMode.inheritStdio);
}

Future<int> _uploadSymbolsToSentry() {
  return runProcess('dart', [
    'run',
    'sentry_dart_plugin',
  ], ProcessStartMode.inheritStdio);
}
