import 'package:bitacora/util/loader/loader.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$Loader tests', () {
    test('Holds initial value', () async {
      final loader = Loader<int>(() async {}, initialValue: 123);

      expect(loader.value, 123);
    });

    test("Doesn't load in constructor", () async {
      var didLoad = false;

      final loader = Loader(() async => didLoad = true);
      await Future.microtask(() => null);

      expect(didLoad, false);
      expect(loader.isLoading, false);
      expect(loader.hasLoaded, false);
    });

    test('Loads on request', () async {
      var didLoad = false;
      final loader = Loader<bool>(() async {
        await Future.microtask(() => null);
        return didLoad = true;
      });

      await loader.load();

      expect(didLoad, true);
      expect(loader.isLoading, false);
      expect(loader.hasLoaded, true);
      expect(loader.value, true);
    });

    test('Loads on subscription', () async {
      var didLoad = false;
      bool? listenerResult;
      final loader = Loader<bool>(() async {
        await Future.microtask(() => null);
        return didLoad = true;
      });

      loader.addListener(() {
        listenerResult = loader.value;
      });

      await awaitUntil(() => listenerResult == true);
      expect(didLoad, true);
      expect(loader.isLoading, false);
      expect(loader.hasLoaded, true);
      expect(loader.value, true);
    });

    test('IsLoading state', () {
      var didLoad = false;
      final loader = Loader(() async {
        await Future.microtask(() {});
        didLoad = true;
      });

      loader.load();

      expect(didLoad, false);
      expect(loader.isLoading, true);
      expect(loader.hasLoaded, false);
    });
  });
}
