import 'package:bitacora/domain/custom_field_options/custom_field_options.dart';
import 'package:bitacora/domain/custom_field_options/custom_field_options_repository.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_db_contract.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_db_fields_builder.dart';
import 'package:bitacora/infrastructure/custom_field_options/custom_field_options_db_translator.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';

class CustomFieldOptionsDbTable
    extends DbTable<CustomFieldOptions, CustomFieldOptionsDbFieldsBuilder>
    implements
        CustomFieldOptionsRepository<DbContext,
            CustomFieldOptionsDbFieldsBuilder> {
  @override
  CustomFieldOptionsDbContract get contract =>
      const CustomFieldOptionsDbContract();

  @override
  DbTranslator<CustomFieldOptions> get translator =>
      const CustomFieldOptionsDbTranslator();

  @override
  CustomFieldOptionsDbFieldsBuilder get fieldsBuilder =>
      CustomFieldOptionsDbFieldsBuilder();
}
