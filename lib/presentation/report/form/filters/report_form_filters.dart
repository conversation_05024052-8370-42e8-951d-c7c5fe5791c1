import 'package:bitacora/presentation/entry/filter/form/entry_filter_form.dart';
import 'package:bitacora/presentation/report/form/filters/report_form_filters_controller.dart';
import 'package:bitacora/presentation/report/form/report_form_page_section.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';

class ReportFormFilters extends StatelessWidget {
  final ReportFormFiltersController controller;

  const ReportFormFilters({super.key, required this.controller});

  @override
  Widget build(BuildContext context) {
    return ReportFormPageSection(
      controller: controller.sectionController,
      hasData: controller.entryFilterController.hasData,
      header: ListTile(
        title: Text(AppLocalizations.of(context)!.filters),
        subtitle: Text(AppLocalizations.of(context)!.reportFiltersCaption),
        visualDensity: VisualDensity.compact,
      ),
      body: ValueListenableBuilder(
        valueListenable: controller.entryType,
        builder: (context, entryType, _) {
          return EntryFilterForm(
            controller: controller.entryFilterController,
            entryType: entryType,
          );
        },
      ),
    );
  }
}
