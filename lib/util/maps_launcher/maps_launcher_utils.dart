import 'package:bitacora/util/bottom_sheet/bottom_sheet_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:latlong2/latlong.dart';
import 'package:map_launcher/map_launcher.dart';

class MapsLauncherUtils {
  void launch(BuildContext context, LatLng location) async {
    await const BottomSheetUtils().show(
      context,
      FutureBuilder<List<AvailableMap>>(
        future: MapLauncher.installedMaps,
        builder: (context, snapshot) {
          if (snapshot.connectionState != ConnectionState.done ||
              !snapshot.hasData) {
            return const SizedBox();
          }

          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.all(16.0),
                child: Text(
                  AppLocalizations.of(context)!.openWith,
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ),
              ...snapshot.data!.map((map) {
                return ListTile(
                  dense: true,
                  title: Text(map.mapName),
                  onTap: () {
                    MapLauncher.showMarker(
                      mapType: map.mapType,
                      coords: Coords(location.latitude, location.longitude),
                      title: '',
                    );
                    Navigator.of(context).pop();
                  },
                );
              }),
              SizedBox(height: MediaQuery.paddingOf(context).bottom),
            ],
          );
        },
      ),
    );
  }
}
