import 'package:bitacora/application/cache/organization/active_organization.dart';
import 'package:bitacora/domain/common/query_scope.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/report/field/report_entry_type.dart';
import 'package:bitacora/domain/report/field/report_format.dart';
import 'package:bitacora/domain/report/report.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/presentation/entry/filter/entry_filter_controller.dart';
import 'package:bitacora/presentation/entry/filter/project/entry_filter_project_repository_query.dart';
import 'package:bitacora/presentation/entry/filter/signee/entry_filter_signee_repository_query.dart';
import 'package:bitacora/presentation/entry/filter/tag/entry_filter_tag_repository_query.dart';
import 'package:bitacora/presentation/entry/filter/user/entry_filter_user_repository_query.dart';
import 'package:bitacora/presentation/report/form/filters/report_form_filter_collection_controller.dart';
import 'package:bitacora/presentation/report/form/report_form_page_section_controller.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:flutter/material.dart';

class ReportFormFiltersController {
  final ValueNotifier<ReportFormat> reportFormat;
  final ValueNotifier<ReportEntryType> entryType;
  final EntryFilterController entryFilterController;

  late final ReportFormPageSectionController sectionController;

  final TextEditingController _titleQueryController;
  final TextEditingController _commentsQueryController;
  final ReportFilterCollectionController<Project> _projectController;
  final ReportFilterCollectionController<Tag> _tagController;
  final ReportFilterCollectionController _sublocationController;
  final ReportFilterCollectionController _providerController;
  final ReportFilterCollectionController<User> _authorController;
  final ReportFilterCollectionController _assigneeController;
  final ReportFilterCollectionController<User> _signeeController;

  ReportFormFiltersController(
    BuildContext context,
    this.reportFormat,
    this.entryType,
    this.entryFilterController,
  )   : _titleQueryController = entryFilterController.titleQueryController,
        _commentsQueryController =
            entryFilterController.commentsQueryController,
        _projectController = ReportFilterCollectionController<Project>(
          apiName: 'project_id',
          apiStringResolver: (project) => '${project.remoteId!.apiValue}',
          typeaheadController: entryFilterController.projectController,
        ),
        _tagController = ReportFilterCollectionController<Tag>(
            apiName: 'tags',
            apiStringResolver: (tag) => '${tag.remoteId!.apiValue}',
            typeaheadController: entryFilterController.tagController),
        _sublocationController = ReportFilterCollectionController(
            apiName: 'sublocation',
            typeaheadController: entryFilterController.sublocationController),
        _providerController = ReportFilterCollectionController(
          apiName: 'provider',
          typeaheadController: entryFilterController.providerController,
        ),
        _authorController = ReportFilterCollectionController<User>(
          apiName: 'author_id',
          apiStringResolver: (user) => '${user.remoteId!.apiValue}',
          typeaheadController: entryFilterController.authorController,
        ),
        _assigneeController = ReportFilterCollectionController(
          apiName: 'assignee',
          typeaheadController: entryFilterController.assigneeController,
        ),
        _signeeController = ReportFilterCollectionController<User>(
          apiName: 'signature',
          apiStringResolver: (user) => '${user.email!.apiValue}',
          typeaheadController: entryFilterController.signeeController,
        ) {
    sectionController = ReportFormPageSectionController(
      hasData: entryFilterController.hasData,
      color: Theme.of(context).colorScheme.primary,
      borderRadius: kBorderRadius,
    );

    reportFormat.addListener(() {
      if (reportFormat.value == ReportFormat.excel) {
        sectionController.expand();
      } else if (!entryFilterController.hasData.value) {
        sectionController.collapse();
      }
    });
  }

  void setDefaultValues(
    Repository db,
    ActiveOrganization activeOrganization,
    Report defaultReport,
  ) {
    if (defaultReport.getParams == null) {
      return;
    }

    _titleQueryController.text = defaultReport.getParams!.filterTitle ?? '';
    _commentsQueryController.text =
        defaultReport.getParams!.filterComments ?? '';

    _projectController.maybeSetDefaultValues(
      defaultReport.getParams!.projectId,
      (remoteId) => db.query(
        EntryFilterProjectRepositoryQuery(projectRemoteId: remoteId),
      ),
    );
    _sublocationController
        .maybeSetDefaultValues(defaultReport.getParams!.sublocation);
    _tagController.maybeSetDefaultValues(
      defaultReport.getParams!.tags,
      (remoteId) => db.query(
        EntryFilterTagRepositoryQuery(tagRemoteId: remoteId),
      ),
    );
    _providerController
        .maybeSetDefaultValues(defaultReport.getParams!.provider);
    _authorController.maybeSetDefaultValues(
      defaultReport.getParams!.authorId,
      (remoteId) => db.query(
        EntryFilterUserRepositoryQuery(userRemoteId: remoteId),
      ),
    );
    _assigneeController
        .maybeSetDefaultValues(defaultReport.getParams!.assignee);
    _signeeController.maybeSetDefaultValues(
      defaultReport.getParams!.signature,
      (email) => db.query(
        EntryFilterSigneeRepositoryQuery(email: email),
        context: db.context(
          queryScope: QueryScope(orgId: activeOrganization.value!.id),
        ),
      ),
    );

    if (defaultReport.getParams!.signatureStatus.isNotEmpty) {
      entryFilterController.signatureStatusSelection
          .toggleList(defaultReport.getParams!.signatureStatus);
    }
  }

  Map<String, dynamic> get apiGetParams {
    return <String, dynamic>{
      ..._projectController.apiGetParams,
      ..._sublocationController.apiGetParams,
      ..._tagController.apiGetParams,
      ..._providerController.apiGetParams,
      ..._authorController.apiGetParams,
      ..._assigneeController.apiGetParams,
      ..._signeeController.apiGetParams,
      ..._getSignatureStatusApiGetParams(),
      if (_titleQueryController.text.isNotEmpty)
        'title': _titleQueryController.text,
      if (_commentsQueryController.text.isNotEmpty)
        'comments': _commentsQueryController.text,
    };
  }

  Map<String, dynamic> _getSignatureStatusApiGetParams() {
    return <String, dynamic>{
      if (entryFilterController.signatureStatusSelection.selections.isNotEmpty)
        'signature_status': entryFilterController
            .signatureStatusSelection.selections
            .map((e) => e.apiValue)
            .toList(),
    };
  }
}
