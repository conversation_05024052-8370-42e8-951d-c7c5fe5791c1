import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/shared_preferences_keys.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/loader/loader.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

class ActiveLogDay extends Loader<LogDay> implements ValueNotifier<LogDay?> {
  ActiveLogDay()
      : super(() async {
          final prefs = await SharedPreferences.getInstance();
          final prefsLogDay = prefs.getInt(SharedPreferencesKeys.activeLogDay);

          if (prefsLogDay != null) {
            return LogDay(prefsLogDay);
          }
          return LogDay(getLogDayForToday());
        });

  Future<void> set(LogDay value) async {
    await _persistActiveLogDay(value);
    await load();
  }

  Future<void> _persistActiveLogDay(LogDay logDay) async {
    final prefs = await SharedPreferences.getInstance();
    if (logDay.value == getLogDayForToday()) {
      await prefs.remove(SharedPreferencesKeys.activeLogDay);
    } else {
      await prefs.setInt(SharedPreferencesKeys.activeLogDay, logDay.value);
    }
  }

  // FIXME. This is only needed due to FormHasChanges. Don't use directly.
  @override
  set value(LogDay? newValue) {
    throw 'Unsupported';
  }
}
