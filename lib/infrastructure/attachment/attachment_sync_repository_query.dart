import 'package:bitacora/domain/attachment/attachment.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';

class AttachmentSyncRepositoryQuery extends RepositoryQuery<Attachment?> {
  final LocalId id;

  AttachmentSyncRepositoryQuery({required this.id});

  @override
  Future<Attachment?> run(RepositoryQueryContext context) =>
      context.db.attachment.find(context, id);

  @override
  Fields fields(Repository db) => db.attachment.fieldsBuilder
      .s3Key()
      .name()
      .isUploaded()
      .isDownloaded()
      .transferState()
      .path()
      .transferAttempts()
      .entry(db.entry.fieldsBuilder.build())
      .build();
}
