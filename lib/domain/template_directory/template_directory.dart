import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/template/template.dart';
import 'package:bitacora/domain/template_directory/value/template_directory_name.dart';
import 'package:bitacora/domain/template_directory/value/template_directory_path.dart';

export 'package:bitacora/domain/template_directory/value/template_directory_name.dart';
export 'package:bitacora/domain/template_directory/value/template_directory_path.dart';

class TemplateDirectory extends Model {
  final TemplateDirectoryName? name;
  final TemplateDirectoryPath? path;
  final TemplateDirectory? parent;

  final List<Template>? templates;
  final Organization? organization;

  TemplateDirectory({
    super.id,
    super.remoteId,
    this.name,
    this.path,
    this.parent,
    this.templates,
    this.organization,
  });

  @override
  Map<Field, dynamic> get fields => {
        TemplateField.id: id,
        TemplateField.remoteId: remoteId,
        TemplateField.name: name,
        TemplateField.path: path,
        TemplateField.parent: parent,
        TemplateField.templates: templates,
        TemplateField.organization: organization,
      };
}

enum TemplateField with Field {
  id,
  remoteId,
  name,
  path,
  parent,
  templates,
  organization,
}

const templateNestedModelFields = {
  TemplateField.templates,
  TemplateField.organization,
  TemplateField.parent,
};
