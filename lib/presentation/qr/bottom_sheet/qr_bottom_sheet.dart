import 'package:bitacora/application/resource/resource_s3_downloader_repository_query.dart';
import 'package:bitacora/domain/common/live_model.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/qr_code/value/qr_code_action.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/presentation/qr/bottom_sheet/qr_bottom_sheet_controller.dart';
import 'package:bitacora/presentation/qr/bottom_sheet/qr_bottom_sheet_error.dart';
import 'package:bitacora/presentation/qr/bottom_sheet/qr_bottom_sheet_header.dart';
import 'package:bitacora/presentation/qr/bottom_sheet/qr_bottom_sheet_resources.dart';
import 'package:bitacora/presentation/qr/bottom_sheet/save_entry/qr_bottom_sheet_save_entry.dart';
import 'package:bitacora/presentation/qr/identifier/qr_identifier_panel.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/util/bitacora_icons.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:flutter_platform_widgets/flutter_platform_widgets.dart';
import 'package:provider/provider.dart';

class QrBottomSheet extends StatelessWidget {
  final QrBottomSheetController controller;

  const QrBottomSheet({
    super.key,
    required this.controller,
  });

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<QrCodeDecodingState>(
      valueListenable: controller.state,
      builder: (context, state, _) {
        return Column(
          children: [
            QrBottomSheetHeader(controller: controller),
            if (controller.resources.isNotEmpty)
              QrBottomSheetResources(
                resources: controller.resources
                    .map((e) => LiveModel<Resource>(
                        e,
                        context.read<Repository>().resource.getMutations(),
                        () => context.read<Repository>().query(
                            ResourceS3DownloaderRepositoryQuery(id: e.id!))))
                    .toList(),
              ),
            Padding(
              padding: kPageInsets.copyWith(top: 0),
              child: _buildBody(context, state),
            )
          ],
        );
      },
    );
  }

  Widget _buildBody(BuildContext context, QrCodeDecodingState state) {
    if (state == QrCodeDecodingState.started) {
      return Center(
        child: PlatformCircularProgressIndicator(),
      );
    }

    if (!controller.belongsToActiveOrganization) {
      return QrBottomSheetError(
        onClosePanel: controller.onClosePanel,
        icon: BitacoraIcons.organization,
        text: AppLocalizations.of(context)!.qrDoesNotBelongOrg,
      );
    }

    if (!controller.locationIsAllowed) {
      return QrBottomSheetError(
        onClosePanel: controller.onClosePanel,
        icon: Icons.wrong_location_outlined,
        text: AppLocalizations.of(context)!.farFromQr,
      );
    }

    if (controller.qrCode?.payload!.action == QrCodeAction.identifier) {
      if (state == QrCodeDecodingState.failed ||
          controller.identifier == null) {
        return QrBottomSheetError(
          onClosePanel: controller.onClosePanel,
          icon: Icons.qr_code,
          text: AppLocalizations.of(context)!.somethingWentWrong,
        );
      }

      return QrIdentifierPanel(controller: controller);
    }

    if (state == QrCodeDecodingState.failed ||
        controller.entryFormController == null) {
      return QrBottomSheetError(
        onClosePanel: controller.onClosePanel,
        icon: Icons.qr_code,
        text: AppLocalizations.of(context)!.somethingWentWrong,
      );
    }

    return QrBottomSheetSaveEntry(
      entryFormController: controller.entryFormController!,
    );
  }
}
