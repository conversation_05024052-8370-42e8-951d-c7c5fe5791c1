import 'package:bitacora/domain/gpt/value/gpt_chat_message_content.dart';

enum GptChatMessageType {
  system('system'),
  user('user'),
  assistant('assistant');

  final String apiValue;

  const GptChatMessageType(this.apiValue);

  bool get isAssistant => this == GptChatMessageType.assistant;
}

class GptChatMessage {
  GptChatMessageContent content;
  GptChatMessageType type;

  GptChatMessage({required this.content, this.type = GptChatMessageType.user});

  Map<String, String> get apiValue =>
      {'role': type.apiValue, 'content': content.value};
}
