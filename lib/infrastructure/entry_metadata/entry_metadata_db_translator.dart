import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_contract.dart';

class EntryMetadataDbTranslator extends DbTranslator<EntryMetadata> {
  EntryMetadataDbTranslator();

  @override
  Set<Field> get nestedModelFields => entryMetadataNestedModelFields;

  @override
  Future<EntryMetadata> fromDb(
      DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return EntryMetadata(
      id: fields[EntryMetadataField.id]?.value(map),
      remoteId: fields[EntryMetadataField.remoteId]?.value(map),
      value: fields[EntryMetadataField.value]?.value(map),
      type: fields[EntryMetadataField.type]?.value(map),
      entry: await fields[EntryMetadataField.entry]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(
      DbContext context, EntryMetadata model) async {
    final map = <String, dynamic>{};
    const contract = EntryMetadataDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.value, model.value);
    addField(map, contract.type, model.type);

    await saveNestedModel<Entry>(
        context, map, contract.entryId, context.db.entry, model.entry);

    return map;
  }
}
