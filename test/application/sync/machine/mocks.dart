import 'package:bitacora/application/api/api_helper.dart';
import 'package:bitacora/application/app_config.dart';
import 'package:bitacora/application/sync/machine/sync_machine.dart';
import 'package:bitacora/application/sync/machine/sync_machine_params.dart';
import 'package:bitacora/application/sync/machine/sync_machine_step.dart';
import 'package:bitacora/application/sync/pending_attachments_upload_repository_query.dart';
import 'package:bitacora/application/sync/pending_mutations_upload_repository_query.dart';
import 'package:bitacora/application/sync/sync_state.dart';
import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/organization/organization.dart';
import 'package:bitacora/domain/session/session.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/infrastructure/api_translator.dart';
import 'package:mocktail/mocktail.dart';

import '../../../analytics/mocks.dart';
import '../../../domain/auth/mocks.dart';
import '../../../domain/common/mocks.dart';
import '../../../infrastructure/mocks.dart';
import '../../../presentation/entry_form/entry_form_page_test.dart';
import '../../api/mocks.dart';

class MockSyncMachine extends Mock implements SyncMachine {}

class MockSyncMachineInjector extends Mock implements SyncMachineInjector {}

class MockSyncMachineParams extends Mock implements SyncMachineParams {}

SyncMachineInjector mockSyncMachineInjector({
  SyncMachine? syncMachine,
  List<SyncMachine>? syncMachines,
}) {
  final mockMachine =
      syncMachine == null && syncMachines == null ? mockSyncMachine() : null;

  final mock = MockSyncMachineInjector();
  when(() => mock.get(
            steps: any(named: 'steps'),
            params: any(named: 'params'),
            syncTrigger: any(named: 'syncTrigger'),
          ))
      .thenAnswer(
          (_) => mockMachine ?? syncMachine ?? syncMachines!.removeAt(0));
  return mock;
}

SyncMachine mockSyncMachine({SyncMachineParams? syncMachineParams}) {
  final mock = MockSyncMachine();
  when(() => mock.cancel()).thenReturn(null);
  when(() => mock.params)
      .thenReturn(syncMachineParams ?? MockSyncMachineParams());
  return mock;
}

SyncMachineParams mockSyncMachineParams({Organization? organization}) {
  final mock = MockSyncMachineParams();
  when(() => mock.organization).thenReturn(organization ?? mockOrganization());
  return mock;
}

class TestSyncMachineStep extends SyncMachineStep {
  int crashCountdown;
  int syncCount = 0;
  Function(TestSyncMachineStep)? onSync;

  TestSyncMachineStep(
    super.params, {
    this.crashCountdown = 0,
    this.onSync,
  });

  @override
  Future<void> performSync() async {
    syncCount++;
    if (onSync != null) {
      onSync!(this);
    }
    if (crashCountdown-- == 1) {
      throw 'Crash';
    }
  }

  @override
  String get debugName => 'test';
}

const kSyncTestUserRemoteId = RemoteId(3);

SyncMachineParams syncMachineParams({
  Organization? organization,
  Repository? db,
  Session? session,
  ApiHelper? apiHelper,
  ApiTranslator? apiTranslator,
  SyncState? syncState,
}) {
  organization ??= const Organization(id: LocalId(1), remoteId: RemoteId(2));
  final repository = db ?? mockRepository();
  return SyncMachineParams(
    appConfig: AppConfig(),
    session: session ??
        mockSession(user: const User(remoteId: kSyncTestUserRemoteId)),
    authRepository: mockAuthRepository(),
    organization: organization,
    db: repository,
    apiTranslator: apiTranslator ?? MockApiTranslator(),
    apiHelper: apiHelper ?? MockApiHelper(),
    syncState: syncState ?? SyncState(repository, mockActiveOrganization()),
    analyticsLogger: mockAnalyticsLogger(),
  );
}

Repository mockRepository() {
  final mock = MockRepository();
  when(() => mock.context(queryScope: any(named: 'queryScope')))
      .thenReturn(MockDbContext());
  when(() => mock.query(const PendingAttachmentsUploadRepositoryQuery(),
      context: any(named: 'context'))).thenAnswer((_) => Future.value([]));
  when(() => mock.query(const PendingMutationsUploadRepositoryQuery(),
      context: any(named: 'context'))).thenAnswer((_) => Future.value([]));

  return mock;
}
