import 'package:bitacora/domain/common/value_object/local_id.dart';
import 'package:bitacora/domain/resource_category/resource_category.dart';
import 'package:bitacora/domain/resource_category/resource_category_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_db_contract.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_db_fields_builder.dart';
import 'package:bitacora/infrastructure/resource_category/resource_category_db_translator.dart';
import 'package:bitacora/infrastructure/resource_category/resource_catergory_resource_db_contract.dart';

class ResourceCategoryDbTable
    extends DbTable<ResourceCategory, ResourceCategoryDbFieldsBuilder>
    implements
        ResourceCategoryRepository<DbContext, ResourceCategoryDbFieldsBuilder> {
  @override
  ResourceCategoryDbContract get contract => const ResourceCategoryDbContract();

  ResourceCategoryResourceDbContract get resourceCategoryResourceDbContract =>
      const ResourceCategoryResourceDbContract();

  @override
  DbTranslator<ResourceCategory> get translator =>
      const ResourceCategoryDbTranslator();

  @override
  ResourceCategoryDbFieldsBuilder get fieldsBuilder =>
      ResourceCategoryDbFieldsBuilder();

  @override
  Future<void> saveResourceRelation(
    DbContext context,
    LocalId resourceId,
    LocalId resourceCategoryId,
  ) async {
    final executor = await context.executor;
    final previousRelations = await executor.query(
      resourceCategoryResourceDbContract.tableName,
      columns: [resourceCategoryResourceDbContract.resourceCategoryId],
      where: '${resourceCategoryResourceDbContract.resourceId} = ? AND '
          '${resourceCategoryResourceDbContract.resourceCategoryId} = ?',
      whereArgs: [resourceId.dbValue, resourceCategoryId.dbValue],
    );

    if (previousRelations.isNotEmpty) {
      return;
    }

    await executor.insert(
      resourceCategoryResourceDbContract.tableName,
      {
        resourceCategoryResourceDbContract.resourceId: resourceId.dbValue,
        resourceCategoryResourceDbContract.resourceCategoryId:
            resourceCategoryId.dbValue,
      },
    );
  }

  @override
  Future<void> deleteLostResourceRelations(
    DbContext context,
    LocalId resourceCategoryId,
    List<LocalId> currentResourceIds,
  ) async {
    final executor = await context.executor;
    await executor.delete(
      resourceCategoryResourceDbContract.tableName,
      where: '''
        ${resourceCategoryResourceDbContract.resourceCategoryId} = ? 
        AND ${resourceCategoryResourceDbContract.resourceId} 
          NOT IN (${currentResourceIds.map((e) => '?').join(',')})
      ''',
      whereArgs: [
        resourceCategoryId.dbValue,
        ...currentResourceIds.map((e) => e.dbValue),
      ],
    );
  }

  @override
  Future<void> onPreDelete(DbContext context, LocalId id) async {
    final executor = await context.executor;
    await executor.delete(
      resourceCategoryResourceDbContract.tableName,
      where: '${resourceCategoryResourceDbContract.resourceCategoryId} = ?',
      whereArgs: [id.dbValue],
    );
  }
}
