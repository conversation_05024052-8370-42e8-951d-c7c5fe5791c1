import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/presentation/entry_form/options/date_time/entry_form_date_time_option_controller.dart';
import 'package:flutter/material.dart';
import 'package:mocktail/mocktail.dart';

import '../../../../../domain/common/mocks.dart';

class MockEntryFormDateTimeOptionController extends Mock
    implements EntryFormDateTimeOptionController {}

EntryFormDateTimeOptionController mockEntryFormDateTimeOptionController() {
  final mock = MockEntryFormDateTimeOptionController();

  final entry = mockEntry(withRemoteId: true);
  final liveEntry = mockLiveModel<Entry>(entry);
  when(() => mock.liveEntry).thenReturn(liveEntry);
  when(() => mock.openStart).thenReturn(TextEditingController());
  when(() => mock.openEnd).thenReturn(TextEditingController());
  when(() => mock.startDate).thenReturn(TextEditingController());
  when(() => mock.endDate).thenReturn(TextEditingController());
  when(() => mock.startTime).thenReturn(TextEditingController());
  when(() => mock.endTime).thenReturn(TextEditingController());
  when(() => mock.isProgressive).thenReturn(ValueNotifier(false));
  when(() => mock.isScheduleEntry).thenReturn(ValueNotifier(false));
  return mock;
}
