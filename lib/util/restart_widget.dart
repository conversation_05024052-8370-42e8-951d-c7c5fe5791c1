import 'package:bitacora/util/context_snapshot/context_snapshot.dart';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';

class RestartWidget extends StatefulWidget {
  final Widget child;

  const RestartWidget({super.key, required this.child});

  static void restartApp(RestartWidgetContextSnapshot contextSnapshot) {
    (contextSnapshot.read<RestartWidgetState>()).restartApp(null);
  }

  @override
  State<RestartWidget> createState() => RestartWidgetState();
}

class RestartWidgetState extends State<RestartWidget> {
  Key _key = UniqueKey();
  RestartData? data;

  void restartApp(RestartData? data) {
    setState(() {
      this.data = data;
      _key = UniqueKey();
    });
  }

  @override
  Widget build(BuildContext context) {
    return KeyedSubtree(
      key: _key,
      child: Provider<RestartData?>.value(
        value: data,
        child: widget.child,
      ),
    );
  }
}

class RestartData {
  final Map<String, dynamic> props;

  RestartData(this.props);
}

class RestartWidgetContextSnapshot extends ContextSnapshot {
  RestartWidgetContextSnapshot(super.context);

  @override
  List<ValueShot> get valueShots => [ValueShot.state<RestartWidgetState>()];
}
