import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:intl/intl.dart';

class CurrencyValueObject extends IntValueObject {
  static final _numberFormat = NumberFormat.simpleCurrency(decimalDigits: 2);

  const CurrencyValueObject(super.value);

  @override
  bool isSameType(Object other) {
    return other is CurrencyValueObject;
  }

  @override
  String get displayValue =>
      value == null ? '' : _numberFormat.format(value! / 100);

  static int? parse(String text) {
    if (text.isEmpty) {
      return null;
    }

    // FIXME: Europe... test other currencies
    if (text.endsWith('.')) {
      return (_numberFormat.parse('${text}0') * 100).round();
    }
    return (_numberFormat.parse(text) * 100).round();
  }
}
