import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_group/entry_group.dart';
import 'package:bitacora/domain/entry_group_entry/entry_group_entry.dart';

class EntryGroupEntryApiTranslator implements ModelTranslator<EntryGroupEntry> {
  const EntryGroupEntryApiTranslator();

  @override
  EntryGroupEntry fromMap(Map<String, dynamic> data) {
    return EntryGroupEntry(
      remoteId: RemoteId(data['id']),
      entry: Entry(remoteId: RemoteId(data['project_entry_id'])),
      entryGroup: EntryGroup(remoteId: RemoteId(data['entry_collection_id'])),
      createFrom: data['create_from'] == null
          ? null
          : Entry(remoteId: RemoteId(data['create_from'])),
    );
  }

  @override
  Map<String, dynamic> toMap(EntryGroupEntry model) {
    return <String, dynamic>{
      if (model.remoteId?.value != null) 'id': model.remoteId!.apiValue,
      'entry_id': model.entry!.remoteId!.apiValue,
      'create_from': model.createFrom?.remoteId!.apiValue,
    };
  }
}
