import 'dart:async';

import 'package:bitacora/application/sync/machine/dedup/sync_entry_dedup_by_created_at_repository_query.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/util/inject/inject.dart';
import 'package:bitacora/util/logger/logger.dart';

class SyncEntryDedupMonitorInjector {
  factory SyncEntryDedupMonitorInjector() =>
      inject(() => const SyncEntryDedupMonitorInjector._());

  const SyncEntryDedupMonitorInjector._();

  SyncEntryDedupMonitor get(Repository db) {
    return inject(() => SyncEntryDedupMonitor._(db));
  }
}

class SyncEntryDedupMonitor {
  final StreamSubscription _subscription;

  SyncEntryDedupMonitor._(Repository db)
      : _subscription =
            db.entry.getMutations().listen((event) => _maybeDedup(db, event));

  void cancel() {
    _subscription.cancel();
  }

  static void _maybeDedup(Repository db, Mutation<Entry> event) {
    final entry = event.model;

    // Make sure it's an insert from server
    if (event.type != MutationType.insert ||
        entry!.id != null ||
        entry.remoteId == null) {
      return;
    }

    db.transaction<void>((context) async {
      final maybeDuplicates = await db.query(
        SyncEntryDedupByCreatedAtRepositoryQuery(createdAt: entry.createdAt!),
        context: context,
      );
      for (final maybeDuplicate in maybeDuplicates) {
        if (maybeDuplicate.remoteId!.value == null &&
            maybeDuplicate.extension!.extensionType ==
                entry.extension!.extensionType &&
            maybeDuplicate.author!.remoteId == entry.author!.remoteId) {
          // FIXME: Which entry to delete?
          // (1) Has failed/pending outgoing updates? Maybe keep local
          // (2) No updates? Probably keep remote, could have changes.
          // Maybe check updatedAt?

          logger
              .i('sync:dedup Deduplicating entry:${maybeDuplicate.id!.value}');
          await db.entry.delete(context, maybeDuplicate.id!);
          return;
        }
      }
    });
  }
}
