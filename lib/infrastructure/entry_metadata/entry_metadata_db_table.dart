import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/common/mutation_type.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata.dart';
import 'package:bitacora/domain/entry_metadata/entry_metadata_repository.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_table.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_contract.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_fields_builder.dart';
import 'package:bitacora/infrastructure/entry_metadata/entry_metadata_db_translator.dart';

class EntryMetadataDbTable
    extends DbTable<EntryMetadata, EntryMetadataDbFieldsBuilder>
    implements
        EntryMetadataRepository<DbContext, EntryMetadataDbFieldsBuilder> {
  @override
  EntryMetadataDbContract get contract => const EntryMetadataDbContract();

  @override
  DbTranslator<EntryMetadata> get translator => EntryMetadataDbTranslator();

  @override
  EntryMetadataDbFieldsBuilder get fieldsBuilder =>
      EntryMetadataDbFieldsBuilder();

  @override
  Future<List<EntryMetadata>> findAll(
    DbContext context,
    LocalId entryId,
  ) {
    return query(
      context,
      where: '${contract.entryId} = ?',
      whereArgs: [entryId.dbValue],
    );
  }

  @override
  Future<void> saveAll(DbContext context, Mutation<Entry> entryMutation) async {
    assert(context.txn != null);
    if (entryMutation.type != MutationType.insert) {
      return;
    }

    final metadataList = entryMutation.model!.metadata!;
    final entryId = entryMutation.id!;

    final savedAttachmentIds = <LocalId>{};
    for (final metadata in metadataList) {
      assert(metadata.entry?.id == null || metadata.entry?.id == entryId);
      final savedId = await save(
        context,
        metadata.entry?.id == null
            ? metadata.copyWith(entry: Entry(id: entryId))
            : metadata,
      );
      savedAttachmentIds.add(savedId ?? metadata.id!);
    }
  }

  @override
  Future<void> deleteAll(DbContext context, LocalId entryId) async {
    final metadataList = await findAll(
        context.copyWith(fields: context.db.entryMetadata.fieldsBuilder.build()),
        entryId);

    for (final metadata in metadataList) {
      await delete(context, metadata.id!);
    }
  }
}
