import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/domain/person_detail/person_detail.dart';
import 'package:bitacora/domain/phone/phone.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';
import 'package:bitacora/infrastructure/phone/phone_db_contract.dart';

class PhoneDbTranslator implements DbTranslator<Phone> {
  const PhoneDbTranslator();

  @override
  Set<Field> get nestedModelFields => phoneNestedModelFields;

  @override
  Future<Phone> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Phone(
      id: fields[PhoneField.id]?.value(map),
      remoteId: fields[PhoneField.remoteId]?.value(map),
      number: fields[PhoneField.number]?.value(map),
      type: fields[PhoneField.type]?.value(map),
      updatedAt: fields[PhoneField.updatedAt]?.value(map),
      createdAt: fields[PhoneField.createdAt]?.value(map),
      personDetail: await fields[PhoneField.personDetail]?.nested(context, map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Phone model) async {
    final map = <String, dynamic>{};
    const contract = PhoneDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.remoteId, model.remoteId);
    addField(map, contract.number, model.number);
    addField(map, contract.type, model.type);
    addField(map, contract.updatedAt, model.updatedAt);
    addField(map, contract.createdAt, model.createdAt);

    await saveNestedModel<PersonDetail>(context, map, contract.personDetailId,
        context.db.personDetail, model.personDetail);
    return map;
  }
}
