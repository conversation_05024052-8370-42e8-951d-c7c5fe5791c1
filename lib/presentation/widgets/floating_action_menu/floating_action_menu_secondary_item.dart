import 'package:bitacora/presentation/widgets/floating_action_menu/floating_action_menu.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:percent_indicator/percent_indicator.dart';

class FloatingActionMenuSecondaryItem {
  final ValueKey<String> key;
  final IconData icon;
  final VoidCallback action;

  FloatingActionMenuSecondaryItem({
    required this.key,
    required this.icon,
    required this.action,
  });

  SingleChildRenderObjectWidget render(
    final ValueNotifier<Key?> secondarySelectedKey, {
    bool showCircularProgressBar = false,
  }) {
    return _FloatingActionMenuSecondaryItemRender(
      key: key,
      child: _FloatingActionMenuSecondaryItemWidget(
        key: key,
        showCircularProgressBar: showCircularProgressBar,
        selectedIndex: secondarySelectedKey,
        icon: icon,
      ),
    );
  }
}

class _FloatingActionMenuSecondaryItemRender
    extends SingleChildRenderObjectWidget {
  const _FloatingActionMenuSecondaryItemRender({
    super.key,
    required super.child,
  });

  @override
  FloatingActionMenuSecondaryItemProxyBox createRenderObject(
    BuildContext context,
  ) {
    return FloatingActionMenuSecondaryItemProxyBox(key);
  }

  @override
  void updateRenderObject(BuildContext context,
      FloatingActionMenuSecondaryItemProxyBox renderObject) {
    renderObject.key = key;
  }
}

class _FloatingActionMenuSecondaryItemWidget extends StatefulWidget {
  final IconData icon;
  final ValueNotifier<Key?> selectedIndex;
  final bool showCircularProgressBar;

  const _FloatingActionMenuSecondaryItemWidget({
    super.key,
    required this.selectedIndex,
    required this.icon,
    this.showCircularProgressBar = false,
  });

  @override
  State<_FloatingActionMenuSecondaryItemWidget> createState() =>
      _FloatingActionMenuSecondaryItemWidgetState();
}

class _FloatingActionMenuSecondaryItemWidgetState
    extends State<_FloatingActionMenuSecondaryItemWidget>
    with SingleTickerProviderStateMixin {
  late final AnimationController _iconScaleAnimationController;
  late final Animation<double> _iconScaleAnimation;
  late bool _isHovered = false;

  @override
  void initState() {
    super.initState();
    final zoomTween = Tween<double>(begin: 0.6, end: 1.0);
    _iconScaleAnimationController = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 200));
    _iconScaleAnimation = zoomTween
        .chain(CurveTween(curve: Curves.easeInOut))
        .animate(_iconScaleAnimationController);

    widget.selectedIndex.addListener(() {
      if (!mounted) {
        return;
      }

      if (widget.selectedIndex.value == widget.key && !_isHovered) {
        setState(() {
          _isHovered = true;
        });
        _iconScaleAnimationController.animateTo(target);
      }
      if (widget.selectedIndex.value != widget.key && _isHovered) {
        setState(() {
          _isHovered = false;
        });
        _iconScaleAnimationController.reverse(from: target);
      }
    });

    _isHovered = widget.selectedIndex.value == widget.key;
    if (_isHovered) {
      _iconScaleAnimationController.animateTo(target);
    }
  }

  @override
  void dispose() {
    _iconScaleAnimationController.dispose();
    super.dispose();
  }

  double get target => widget.showCircularProgressBar ? 0.7 : 1.0;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0),
      child: SizeTransition(
        axis: Axis.horizontal,
        sizeFactor: _iconScaleAnimation,
        child: ScaleTransition(
          scale: _iconScaleAnimation,
          child: Builder(builder: (context) {
            if (!widget.showCircularProgressBar) {
              return Icon(
                widget.icon,
                color: _isHovered
                    ? Theme.of(context).colorScheme.primary
                    : Theme.of(context).colorScheme.onSurface,
                size: 42,
              );
            }

            return Stack(
              children: [
                CircularPercentIndicator(
                  backgroundColor: Theme.of(context).colorScheme.surface,
                  progressColor: Theme.of(context).colorScheme.primary,
                  animation: true,
                  animationDuration:
                      kAutoSelectDefaultSecondaryOptionDuration.inMilliseconds,
                  percent: 1.0,
                  lineWidth: 4.0,
                  radius: 24.0,
                  center: Padding(
                    padding: const EdgeInsets.all(0.0),
                    child: Icon(
                      widget.icon,
                      color: _isHovered
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.onSurface,
                      size: 36,
                    ),
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }
}

class FloatingActionMenuSecondaryItemProxyBox extends RenderProxyBox {
  Key? key;

  FloatingActionMenuSecondaryItemProxyBox(this.key);
}
