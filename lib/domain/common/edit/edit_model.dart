import 'package:bitacora/domain/common/model.dart';
import 'package:flutter/foundation.dart';

abstract class EditModel<T extends Model> extends ValueNotifier<T?> {
  final ValueNotifier<T?> liveModel;

  EditModel(this.liveModel) : super(liveModel.value) {
    liveModel.addListener(_update);
  }

  @override
  void dispose() {
    liveModel.removeListener(_update);
    super.dispose();
  }

  void _update() {
    value = edit;
  }

  @protected
  void onEdit() {
    _update();
  }

  @protected
  T? get edit;

  bool hasEdits<X>();
}
