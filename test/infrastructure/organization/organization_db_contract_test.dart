import 'package:bitacora/infrastructure/organization/organization_db_contract.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../test_util.dart';

void main() {
  group('$OrganizationDbContract tests', () {
    test('Create $OrganizationDbContract', () {
      expectRemovingSpaces(
        const OrganizationDbContract().create,
        '''
        CREATE TABLE organization (
          o_id INTEGER PRIMARY KEY AUTOINCREMENT,
          o_remoteId INTEGER UNIQUE,
          o_createdAt INTEGER NOT NULL,
          o_updatedAt INTEGER NOT NULL,
          o_name TEXT NOT NULL,
          o_activePlan INTEGER NOT NULL,
          o_color INTEGER NOT NULL,
          o_userHasSeen INTEGER NOT NULL DEFAULT 0,
          o_ownerId INTEGER NOT NULL
        )
        ''',
      );
    });
  });
}
