import 'dart:async';

import 'package:bitacora/domain/common/model_translator.dart';
import 'package:bitacora/domain/common/mutation.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/entry_repository.dart';
import 'package:bitacora/domain/feed_post/feed_post.dart';
import 'package:bitacora/domain/feed_post/feed_post_repository.dart';
import 'package:bitacora/domain/product/product.dart';
import 'package:bitacora/domain/product/product_repository.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/domain/resource/resource_repository.dart';
import 'package:bitacora/infrastructure/entry/entry_api_translator.dart';
import 'package:mocktail/mocktail.dart';

import '../common/mocks.dart';

class MockEntryRepository extends Mock implements EntryRepository {}

class MockFeedPostRepository extends Mock implements FeedPostRepository {}

class MockResourceRepository extends Mock implements ResourceRepository {}

class MockProductRepository extends Mock implements ProductRepository {}

class MockEntryApiTranslator extends Mock implements EntryApiTranslator {}

EntryRepository mockEntryRepository(
    {StreamController<Mutation<Entry>>? mutationsController,
    List<String> assignees = const <String>[]}) {
  final mock = MockEntryRepository();
  when(() => mock.getMutations()).thenAnswer((_) =>
      (mutationsController ?? StreamController<Mutation<Entry>>.broadcast())
          .stream);
  when(() => mock.assignees(any())).thenAnswer((_) => Future.value(assignees));
  return mock;
}

FeedPostRepository mockFeedPostRepository(
    {StreamController<Mutation<FeedPost>>? mutationsController,
    List<String> assignees = const <String>[]}) {
  final mock = MockFeedPostRepository();
  when(() => mock.getMutations()).thenAnswer((_) =>
      (mutationsController ?? StreamController<Mutation<FeedPost>>.broadcast())
          .stream);
  return mock;
}

ResourceRepository mockResourceRepository(
    {StreamController<Mutation<Resource>>? mutationsController,
    List<String> assignees = const <String>[]}) {
  final mock = MockResourceRepository();
  when(() => mock.getMutations()).thenAnswer((_) =>
      (mutationsController ?? StreamController<Mutation<Resource>>.broadcast())
          .stream);
  return mock;
}

ProductRepository mockProductRepository(
    {StreamController<Mutation<Product>>? mutationsController,
    List<String> assignees = const <String>[]}) {
  final mock = MockProductRepository();
  when(() => mock.getMutations()).thenAnswer((_) =>
      (mutationsController ?? StreamController<Mutation<Product>>.broadcast())
          .stream);
  return mock;
}

ModelTranslator<Entry> mockEntryModelTranslator({
  bool prepareToMap = true,
  bool prepareFromMap = true,
}) {
  final mock = MockEntryApiTranslator();
  prepareMockModelTranslator(
    mock,
    prepareToMap: prepareToMap,
    fromMapBuilder:
        prepareFromMap ? (map) => Entry(remoteId: RemoteId(map['id'])) : null,
  );
  return mock;
}
