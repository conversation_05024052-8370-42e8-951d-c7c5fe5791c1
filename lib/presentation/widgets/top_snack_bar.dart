import 'package:bitacora/presentation/theme/bitacora_colors.dart';
import 'package:bitacora/presentation/theme/theme_util.dart';
import 'package:bitacora/presentation/widgets/loading_indicator.dart';
import 'package:flutter/material.dart';

class TopSnackBar extends StatelessWidget {
  final Widget title;
  final Widget? action;
  final MaterialColor? color;
  final bool isError;
  final VoidCallback? onTap;

  const TopSnackBar({
    super.key,
    required this.title,
    this.action,
    this.isError = false,
    this.color,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final backgroundColor = color != null
        ? color!.withValues(alpha: 0.15)
        : isError
            ? Theme.of(context).colorScheme.error.withValues(alpha: 0.15)
            : Theme.of(context).colorScheme.primary.withValues(alpha: 0.15);

    final actionOrLoading = action ?? LoadingIndicator.small();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0),
      child: Card(
        color: backgroundColor,
        elevation: 0,
        child: ListTile(
          onTap: onTap,
          contentPadding: const EdgeInsets.only(
            right: kInputDecorationPadding,
            left: kInputDecorationPadding * 2,
          ),
          title: DefaultTextStyle.merge(
            style: Theme.of(context).textTheme.bodyMedium,
            child: title,
          ),
          trailing: Theme(
            data: ThemeData(primarySwatch: isError ? bitacoraRed : color),
            child: actionOrLoading,
          ),
        ),
      ),
    );
  }
}
