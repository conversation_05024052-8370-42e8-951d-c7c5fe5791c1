import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';

class EntryGroupDbContract extends DbContract {
  static const String _ = 'eg_';
  static const String _tableName = 'entryGroup';

  final String id = '${_}id';
  final String remoteId = '${_}remoteId';
  final String name = '${_}name';
  final String organizationId = '${_}organizationId';
  final String createdAt = '${_}createdAt';

  const EntryGroupDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithEntryGroupTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $remoteId INTEGER UNIQUE,
    $name TEXT NOT NULL,
    $organizationId INTEGER NOT NULL,
    $createdAt INTEGER NOT NULL
  )
  ''';
}
