import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:bit/ascii/ansi_stdout.dart';
import 'package:bit/bit_build/build_for_version_bump.dart';
import 'package:bit/bit_coverage/test_coverage_analysis.dart';
import 'package:bit/bit_version_bump/dart_analysis.dart';
import 'package:bit/bit_version_bump/gen_l10n.dart';
import 'package:bit/bit_version_bump/upload_symbols.dart';
import 'package:bit/bit_version_bump/version.dart';
import 'package:bit/command_line.dart';
import 'package:bit/environment.dart';
import 'package:bit/git_utils.dart';
import 'package:bit/process.dart';

const _kFlagHelp = '-h';
const _kFlagPatch = '--patch';
const _kFlagMinor = '--minor';
const _kFlagMajor = '--major';
const _kFlagTestRun = '--test-run';
const _kFlagList = '--list';
const _kFlagInfo = '--info';

final _kVersionRegex = RegExp(r'version: (\d+)\.(\d+)\.(\d+)\+(\d+)');
final _kFlutterVersionRegex = RegExp(r'Flutter (\d+).(\d+).(\d+)');
final _kCommitCoverageRegex = RegExp(
  r'`bit coverage` -> (\d+).(\d+)%, (\d+) tests',
);
final _kCommitIosBuildRegex = RegExp(r'`bit build bit4ios` -> (\d+) bytes');
final _kCommitAndroidBuildRegex = RegExp(r'`bit build bit4a` -> (\d+) bytes');

const _help = '''
A tool for pushing a version bump.

1. Verifies git status
2. Runs `dart analyze`
3. Runs `bit coverage`
4. Updates version in pubspec.yaml
5. Runs `bit build bit4ios`
6. Runs `bit build bit4a`
7. Uploads dSYMs
8. Commits changes

Available arguments:

 -h               Prints help.
 --patch          Increases patch version i.e. 0.0.1 -> 0.0.2 [Default]
 --minor          Increases minor version i.e. 0.1.1 -> 0.2.0
 --major          Increases major version i.e. 1.1.1 -> 2.0.0
 --test-run       Skips checks and upload steps.
 --list           Prints versions list with commit hash.
 --info v0.5.1    Provides version details.

Usage: 

  bit version-bump
  bit version-bump --patch
  bit version-bump --test-run
  bit version-bump --list
  bit version-bump --info v0.5
''';

Future<int> bitVersionBump([List<String> args = const <String>[]]) async {
  if (args.contains(_kFlagHelp)) {
    writeln(_help, TextStyle.help);
    return 0;
  }

  return _runCommand(args);
}

Future<int> _runCommand(List<String> args) {
  if (args.contains(_kFlagList)) {
    return _listVersions();
  } else if (args.contains(_kFlagTestRun)) {
    return _buildTestRun();
  } else if (args.contains(_kFlagInfo)) {
    return _printVersionInfo(args);
  }
  return _maybeBumpVersion(args);
}

Future<VersionBumpLevel> _getVersionBumpLevel(List<String> args) async {
  if (args.length != 1) {
    return VersionBumpLevel.patch;
  }

  switch (args.first) {
    case _kFlagMajor:
      return VersionBumpLevel.major;
    case _kFlagMinor:
      return VersionBumpLevel.minor;
    case _kFlagPatch:
    default:
      return VersionBumpLevel.patch;
  }
}

Future<int> _listVersions() async {
  writeln('Listing versions...', TextStyle.highlighted);
  return runProcess('git', [
    'log',
    '--pretty=format:%C(yellow)%H %Creset%ch -> %Cgreen%s',
    '--all',
    '--grep=^v[0-9]\\+.[0-9]\\+.[0-9]\\++[0-9]\\+',
  ], ProcessStartMode.inheritStdio);
}

Future<int> _maybeBumpVersion(List<String> args) async {
  final level = await _getVersionBumpLevel(args);
  writeln('Bumping version (${level.name})...\n', TextStyle.highlighted);

  final gitStatus = await _assertGitStatus();
  if (gitStatus != 0) {
    return gitStatus;
  }

  final currentVersion = _getCurrentVersion();
  final nextVersion = currentVersion.bump(level);
  write('\nThis will bump Bitacora.io app from ');
  write('v$currentVersion', TextStyle.error);
  write(' to ');
  write('v$nextVersion', TextStyle.highlighted);
  writeln('.\n');
  final bump =
      await CommandLine.confirm(
        () => _bumpVersion(currentVersion, nextVersion),
      ).present();
  return bump ?? 1;
}

Future<int> _assertGitStatus() async {
  writeln('Analyzing git status...', TextStyle.highlighted);

  writeln('Checking main repository.');
  final main = await gitAssertBranchCleanAndPull();
  if (main != 0) {
    return main;
  }

  writeln('Checking data repository.');
  return wrapWorkingDirectory(
    kDataRepositoryDirectory,
    gitAssertBranchCleanAndPull,
  );
}

Version _getCurrentVersion([bool isTestRun = false]) {
  final pubspec = File('pubspec.yaml').readAsStringSync();
  final v = _kVersionRegex.firstMatch(pubspec)!;
  return Version(
    int.parse(v.group(1)!),
    int.parse(v.group(2)!),
    int.parse(v.group(3)!),
    int.parse(v.group(4)!),
    isTestRun,
  );
}

Future<int> _bumpVersion(Version previousVersion, Version version) async {
  final l10nResult = await generateL10n();
  if (l10nResult != 0) {
    writeln('Error generating l10n files $l10nResult.', TextStyle.error);
  }

  final pubGetResult = await _flutterPubGet();
  if (pubGetResult != 0) {
    writeln('Error getting flutter dependencies.', TextStyle.error);
  }

  final dartAnalysis = await analyzeDart();
  if (dartAnalysis != 0) {
    writeln(
      'Dart analysis found ${dartAnalysis == 1 ? 'warnings' : 'errors'}.',
      TextStyle.error,
    );
    return dartAnalysis;
  }

  writeln();
  final testCoverage = await analyzeTestCoverage();
  if (testCoverage.exitCode != 0) {
    writeln('Test analysis failed.', TextStyle.error);
    return testCoverage.exitCode;
  }

  writeln();
  await _bumpVersionInPubspec(version);

  writeln();
  final build4ios = await buildForVersionBump(version, 'bit4ios');
  if (build4ios.exitCode != 0) {
    writeln('Build failed.', TextStyle.error);
    return build4ios.exitCode;
  }

  writeln();
  final build4a = await buildForVersionBump(version, 'bit4a');
  if (build4a.exitCode != 0) {
    writeln('Build failed.', TextStyle.error);
    return build4a.exitCode;
  }

  writeln('\nOpening output directory...', TextStyle.highlighted);
  await runProcess('open', [version.outputPath]);

  writeln('\nPreparing commit...', TextStyle.highlighted);
  final commitMessage = await _getCommitMessage(
    previousVersion,
    version,
    testCoverage,
    build4ios,
    build4a,
  );
  writeln('\nCommit message:');
  writeln(commitMessage);

  await CommandLine.confirm(() async {
    await runProcess('git', ['add', '.']);
    final result = await runProcess('git', ['commit', '-m', commitMessage]);
    if (result == 0) {
      return runProcess('git', ['push']);
    }
    return result;
  }).present();

  writeln();
  final symbols = await uploadSymbols(version);
  if (symbols != 0) {
    writeln(
      'Symbols failed to upload, please upload manually.',
      TextStyle.error,
    );
  }
  await _showVersionLog(previousVersion);
  return 0;
}

Future<int> _buildTestRun() async {
  final previousVersion = _getCurrentVersion();
  final version = _getCurrentVersion(true);
  writeln('Test-running a version bump $version...\n', TextStyle.highlighted);

  final build4ios = await buildForVersionBump(version, 'bit4ios');
  if (build4ios.exitCode != 0) {
    writeln('Build failed.', TextStyle.error);
    return build4ios.exitCode;
  }

  writeln();
  final build4a = await buildForVersionBump(version, 'bit4a');
  if (build4a.exitCode != 0) {
    writeln('Build failed.', TextStyle.error);
    return build4a.exitCode;
  }

  writeln('\nOpening output directory...', TextStyle.highlighted);
  await runProcess('open', [version.outputPath]);

  final commitMessage = await _getCommitMessage(
    previousVersion,
    version,
    TestCoverageResult(0),
    build4ios,
    build4a,
  );
  writeln('\nResult:');
  writeln(commitMessage);
  return 0;
}

Future<int> _flutterPubGet() async {
  writeln('Updating flutter dependencies...', TextStyle.highlighted);
  return runProcess('flutter', ['pub', 'get'], ProcessStartMode.inheritStdio);
}

Future<void> _bumpVersionInPubspec(Version version) async {
  writeln('Bumping version in pubspec...', TextStyle.highlighted);
  final pubspecFile = File('pubspec.yaml');
  final pubspec = pubspecFile.readAsStringSync();
  final replaced = pubspec.replaceFirst(_kVersionRegex, 'version: $version');
  pubspecFile.writeAsStringSync(replaced);

  final gitDiff = await startProcess('git', ['diff']);
  gitDiff.stdout.listen(stdout.add);
  await gitDiff.exitCode;
}

Future<String> _getCommitMessage(
  Version previousVersion,
  Version version,
  TestCoverageResult testCoverage,
  BuildVersionBumpResult build4ios,
  BuildVersionBumpResult build4a,
) async {
  final previousVersionInfo = await _getVersionInfo(previousVersion);

  final coverageDelta =
      testCoverage.coverage - previousVersionInfo.testCoverage;
  final numTestsDelta = testCoverage.numTests - previousVersionInfo.numTests;
  final testsReport =
      '${testCoverage.coverage}%, ${testCoverage.numTests} tests '
      '[${coverageDelta >= 0 ? '+' : ''}${coverageDelta.toStringAsFixed(1)}%, '
      '${numTestsDelta >= 0 ? '+' : ''}$numTestsDelta tests]';

  final iosAppSizeDelta = build4ios.appSize - previousVersionInfo.iosAppSize;
  final iosReport =
      '${build4ios.appSize} bytes '
      '[${iosAppSizeDelta >= 0 ? '+' : ''}$iosAppSizeDelta bytes]';

  final androidAppSizeDelta =
      build4a.appSize - previousVersionInfo.androidAppSize;
  final androidReport =
      '${build4a.appSize} bytes '
      '[${androidAppSizeDelta >= 0 ? '+' : ''}$androidAppSizeDelta bytes]';

  final flutterVersion = await _getFlutterVersion();
  return '''
v$version

`dart analyze` -> 0 warnings, 0 errors
`bit coverage` -> $testsReport
`bit build bit4ios` -> $iosReport
`bit build bit4a` -> $androidReport

flutter version: $flutterVersion
''';
}

Future<int> _showVersionLog(Version previousVersion) async {
  final process = await startProcess('git', ['log', '--pretty=format:%s']);
  var hasMatch = false;

  writeln('\nVersion Log:', TextStyle.highlighted);
  writeln();
  process.stdout.listen((event) {
    if (hasMatch) {
      return;
    }

    for (final line in utf8.decoder.convert(event).split('\n')) {
      if (line.isNotEmpty) {
        stdout.writeln(line);
      }
      if (line == 'v$previousVersion') {
        hasMatch = true;
        stdout.writeln();
        return;
      }
    }
  });
  return process.exitCode;
}

Future<int> _printVersionInfo(List<String> args) async {
  final infoIndex = args.indexOf(_kFlagInfo);
  final version = args[infoIndex + 1];
  writeln('Printing $version info...', TextStyle.highlighted);
  await runProcess('git', [
    'log',
    '--grep=^$version',
  ], ProcessStartMode.inheritStdio);
  return 0;
}

Future<VersionInfo> _getVersionInfo(Version version) async {
  var testCoverage = 0.0;
  var numTests = 0;
  var iosByteSize = 0;
  var androidByteSize = 0;

  final gitLog = await startProcess('git', ['log', '--grep=^v$version']);
  gitLog.stdout.listen((event) {
    final split = utf8.decoder.convert(event).split('\n');
    for (final element in split) {
      final coverageMatch = _kCommitCoverageRegex.firstMatch(element);
      if (coverageMatch != null && coverageMatch.groupCount == 3) {
        testCoverage = double.parse(
          '${coverageMatch.group(1)!}.${coverageMatch.group(2)!}',
        );
        numTests = int.parse(coverageMatch.group(3)!);
      }

      final iosBuildMatch = _kCommitIosBuildRegex.firstMatch(element);
      if (iosBuildMatch != null && iosBuildMatch.groupCount == 1) {
        iosByteSize = int.parse(iosBuildMatch.group(1)!);
      }

      final androidBuildMatch = _kCommitAndroidBuildRegex.firstMatch(element);
      if (androidBuildMatch != null && androidBuildMatch.groupCount == 1) {
        androidByteSize = int.parse(androidBuildMatch.group(1)!);
      }
    }
  });
  await gitLog.exitCode;

  return VersionInfo(
    testCoverage: testCoverage,
    numTests: numTests,
    iosAppSize: iosByteSize,
    androidAppSize: androidByteSize,
  );
}

Future<String> _getFlutterVersion() async {
  String flutterVersion = '';
  final flutterVersionProcess = await startProcess('flutter', ['--version']);
  flutterVersionProcess.stdout.listen((event) {
    final split = utf8.decoder.convert(event).split('\n');
    for (final element in split) {
      final match = _kFlutterVersionRegex.firstMatch(element);
      if (match != null && match.groupCount == 3) {
        flutterVersion =
            '${match.group(1)}.${match.group(2)}.${match.group(3)}';
      }
    }
  });
  await flutterVersionProcess.exitCode;
  return flutterVersion;
}
