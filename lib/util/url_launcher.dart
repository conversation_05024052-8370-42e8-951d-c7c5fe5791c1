import 'package:bitacora/util/inject/inject.dart';
import 'package:url_launcher/url_launcher.dart' as launcher;
import 'package:url_launcher/url_launcher.dart';

class UrlLauncher {
  factory UrlLauncher() => inject(() => const UrlLauncher._());

  const UrlLauncher._();

  Future<bool> canLaunch(String url) => launcher.canLaunchUrl(Uri.parse(url));

  Future<void> launch(
    String url, {
    LaunchMode mode = LaunchMode.inAppWebView,
  }) =>
      launcher.launchUrl(Uri.parse(url), mode: mode);
}
