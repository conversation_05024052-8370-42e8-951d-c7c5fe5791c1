import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/qr_code/qr_code.dart';

class QrCodeByRemoteIdRepositoryQuery extends RepositoryQuery<QrCode?> {
  final RemoteId remoteId;

  const QrCodeByRemoteIdRepositoryQuery(this.remoteId);

  @override
  Future<QrCode?> run(RepositoryQueryContext context) =>
      context.db.qrCode.findByRemoteId(context, remoteId);

  @override
  Fields? fields(Repository<RepositoryQueryContext> db) =>
      db.qrCode.fieldsBuilder.build();
}
