import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/resource/resource.dart';
import 'package:bitacora/domain/resource/value/resource_aggregation_entity_id.dart';
import 'package:bitacora/domain/resource/value/resource_aggregation_entity_type.dart';

class QrResourcesRepositoryQuery extends RepositoryQuery<List<Resource>> {
  final RemoteId qrRemoteId;
  final ResourceAggregationEntityType entityType;

  QrResourcesRepositoryQuery(
      {required this.qrRemoteId, required this.entityType});

  @override
  Future<List<Resource>> run(RepositoryQueryContext context) {
    final entityId = ResourceAggregationEntityId('${qrRemoteId.dbValue}');
    return context.db.resource.findAllByEntity(context, entityId, entityType);
  }

  @override
  Fields fields(Repository db) {
    return db.resource.fieldsBuilder
        .s3Key()
        .name()
        .isDownloaded()
        .transferState()
        .path()
        .type()
        .metadata()
        .build();
  }
}
