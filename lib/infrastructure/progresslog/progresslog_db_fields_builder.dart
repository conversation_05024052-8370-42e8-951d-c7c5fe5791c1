import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/common/value_object/common.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/progresslog/progresslog_fields_builder.dart';
import 'package:bitacora/infrastructure/db_field.dart';
import 'package:bitacora/infrastructure/db_fields_builder.dart';
import 'package:bitacora/infrastructure/progresslog/progresslog_db_contract.dart';

class ProgresslogDbFieldsBuilder extends DbFieldsBuilder
    implements ProgresslogFieldsBuilder {
  ProgresslogDbFieldsBuilder() {
    _id();
  }

  ProgresslogDbContract get contract => const ProgresslogDbContract();

  ProgresslogDbFieldsBuilder _id() {
    addField(
      ProgresslogField.id,
      DbField(
        column: contract.id,
        valueBuilder: (v) => LocalId(v),
      ),
    );
    return this;
  }

  @override
  ProgresslogDbFieldsBuilder remoteId() {
    addField(
      ProgresslogField.remoteId,
      DbField(
        column: contract.remoteId,
        valueBuilder: (v) => RemoteId(v),
      ),
    );
    return this;
  }

  @override
  ProgresslogDbFieldsBuilder progress() {
    addField(
      ProgresslogField.progress,
      DbField(
        column: contract.progress,
        valueBuilder: (v) => ProgresslogProgress(v),
      ),
    );
    return this;
  }

  @override
  ProgresslogDbFieldsBuilder entry(Fields fields) {
    addField(
      ProgresslogField.entry,
      DbField(
        key: ProgresslogField.entry,
        column: contract.entryId,
        nestedFields: fields as DbFields,
        nestedBuilder: (nestedContext, value) =>
            nestedContext.db.entry.find(nestedContext, LocalId(value)),
      ),
    );
    return this;
  }
}
