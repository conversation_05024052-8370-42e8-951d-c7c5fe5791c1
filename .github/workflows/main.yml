name: Test

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

jobs:
  build:
    runs-on: macos-latest
    steps:
      # Checks-out your repository under $GITHUB_WORKSPACE, so your job can access it
      - uses: actions/checkout@v2
      
      # Install Flutter
      - uses: actions/setup-java@v2
        with:
          distribution: 'zulu'
          java-version: '11'
      - uses: subosito/flutter-action@v1
        with:
          flutter-version: '2.5.3'
      
      # Install dependencies
      - name: Install dependencies
        run: flutter pub get

      # Test
      - name: Test app
        run: flutter test --coverage
