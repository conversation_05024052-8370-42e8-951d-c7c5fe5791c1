import 'package:bitacora/domain/inventorylog/value/inventorylog_type.dart';
import 'package:bitacora/presentation/entry_form/inventorylog/inventorylog_form_controller.dart';
import 'package:bitacora/presentation/entry_form/util/header_option.dart';
import 'package:flutter/material.dart';

class InventorylogFormHeaderOption extends StatelessWidget {
  final InventorylogFormController controller;
  final bool isEditable;

  const InventorylogFormHeaderOption({
    super.key,
    required this.controller,
    this.isEditable = true,
  });

  @override
  Widget build(BuildContext context) {
    return HeaderOption<InventorylogType>(
      valueNotifier: controller.inventorylogType,
      name: (context, value) => value.localizedDisplayValue(context),
      options: InventorylogType.values,
      isEditable: isEditable,
    );
  }
}
