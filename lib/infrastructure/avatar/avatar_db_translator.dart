import 'package:bitacora/domain/avatar/avatar.dart';
import 'package:bitacora/domain/common/model.dart';
import 'package:bitacora/infrastructure/avatar/avatar_db_contract.dart';
import 'package:bitacora/infrastructure/db_context.dart';
import 'package:bitacora/infrastructure/db_translator.dart';

class AvatarDbTranslator implements DbTranslator<Avatar> {
  const AvatarDbTranslator();

  @override
  Set<Field> get nestedModelFields => avatarNestedModelFields;

  @override
  Future<Avatar> fromDb(DbContext context, Map<String, dynamic> map) async {
    final fields = context.fields!.map;
    return Avatar(
      id: fields[AvatarField.id]?.value(map),
      path: fields[AvatarField.path]?.value(map),
      s3Key: fields[AvatarField.s3Key]?.value(map),
      isDownloaded: fields[AvatarField.isDownloaded]?.value(map),
      transferState: fields[AvatarField.transferState]?.value(map),
      personDetailId: fields[AvatarField.personDetailId]?.value(map),
    );
  }

  @override
  Future<Map<String, dynamic>> toDb(DbContext context, Avatar model) async {
    final map = <String, dynamic>{};
    const contract = AvatarDbContract();
    addField(map, contract.id, model.id);
    addField(map, contract.path, model.path);
    addField(map, contract.s3Key, model.s3Key);
    addField(map, contract.isDownloaded, model.isDownloaded);
    addField(map, contract.transferState, model.transferState);
    addField(map, contract.personDetailId, model.personDetailId);

    return map;
  }
}
