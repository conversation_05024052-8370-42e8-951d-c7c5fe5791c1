import 'package:bitacora/domain/common/query/project_common_db_queries.dart';
import 'package:bitacora/domain/common/query/search_providers_repository_query.dart';
import 'package:bitacora/domain/entry/filter/entry_filter.dart';
import 'package:bitacora/domain/entry/value/entry_signature_status.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/domain/user/user.dart';
import 'package:bitacora/l10n/app_localizations_resolver.dart';
import 'package:bitacora/presentation/entry/filter/project/entry_filter_projects_repository_query.dart';
import 'package:bitacora/presentation/entry/filter/signee/entry_filter_signees_repository_query.dart';
import 'package:bitacora/presentation/entry/filter/tag/entry_filter_tags_repository_query.dart';
import 'package:bitacora/presentation/entry/filter/user/entry_filter_assignees_repository_query.dart';
import 'package:bitacora/presentation/entry/filter/user/entry_filter_staff_repository_query.dart';
import 'package:bitacora/presentation/entry_form/util/project_sublocation_form_row/sublocation_suggestion_respository_query.dart';
import 'package:bitacora/util/collection_selection/collection_selection.dart';
import 'package:bitacora/util/collection_selection/collection_selection_search_typeahead_controller.dart';
import 'package:flutter/material.dart';

class EntryFilterController {
  final ValueNotifier<EntryFilter?> entryFilter;
  final ValueNotifier<bool> hasData = ValueNotifier(false);

  final TextEditingController titleQueryController;
  final TextEditingController commentsQueryController;
  final CollectionSelectionSearchTypeaheadController<Project> projectController;
  final CollectionSelectionSearchTypeaheadController sublocationController;
  final CollectionSelectionSearchTypeaheadController<Tag> tagController;
  final CollectionSelectionSearchTypeaheadController providerController;
  final CollectionSelectionSearchTypeaheadController<User> authorController;
  final CollectionSelectionSearchTypeaheadController assigneeController;
  final CollectionSelectionSearchTypeaheadController<User> signeeController;
  final CollectionSelection<EntrySignatureStatus> signatureStatusSelection;

  EntryFilterController({
    EntryFilter? entryFilter,
  })  : entryFilter = ValueNotifier(entryFilter ?? EntryFilter()),
        titleQueryController = TextEditingController()
          ..text = entryFilter?.titleQuery ?? '',
        commentsQueryController = TextEditingController()
          ..text = entryFilter?.commentsQuery ?? '',
        projectController = CollectionSelectionSearchTypeaheadController(
          fieldName: AppLocalizationsResolver.get().formProject,
          suggestionsQuery: const EntryFilterProjectsRepositoryQuery(),
          displayStringResolver: (project) => project.name!.displayValue,
          filterOutResolver: (project) => project.id!.dbValue,
          filterOutDefaultQuery: const ProjectByNameRepositoryQuery(
              ProjectName(kDefaultProjectName)),
          initialItems: entryFilter?.projects,
        ),
        sublocationController = CollectionSelectionSearchTypeaheadController(
          fieldName: AppLocalizationsResolver.get().formSublocation,
          suggestionsQuery: const SublocationSuggestionRepositoryQuery(),
          initialItems: entryFilter?.sublocations,
        ),
        tagController = CollectionSelectionSearchTypeaheadController(
          fieldName: AppLocalizationsResolver.get().tag,
          suggestionsQuery: const EntryFilterTagsRepositoryQuery(),
          displayStringResolver: (tag) => tag.name!.displayValue,
          filterOutResolver: (tag) => tag.id!.dbValue,
          colorResolver: (context, tag) => tag.color!.opaqueValue(context),
          initialItems: entryFilter?.tags,
        ),
        providerController = CollectionSelectionSearchTypeaheadController(
          fieldName: AppLocalizationsResolver.get().provider,
          suggestionsQuery: const SearchProvidersRepositoryQuery(),
          initialItems: entryFilter?.providers,
        ),
        authorController = CollectionSelectionSearchTypeaheadController(
          fieldName: AppLocalizationsResolver.get().creator,
          suggestionsQuery: const EntryFilterStaffRepositoryQuery(),
          displayStringResolver: (user) => user.nameEmailDisplayValue,
          filterOutResolver: (user) => user.id!.dbValue,
          initialItems: entryFilter?.authors,
        ),
        assigneeController = CollectionSelectionSearchTypeaheadController(
          fieldName: AppLocalizationsResolver.get().assignee,
          suggestionsQuery: const EntryFilterAssigneesRepositoryQuery(),
          initialItems: entryFilter?.assignee,
        ),
        signeeController = CollectionSelectionSearchTypeaheadController(
          fieldName: AppLocalizationsResolver.get().signee,
          textEditingHint: '${AppLocalizationsResolver.get().signee}'
              ' [${AppLocalizationsResolver.get().email}]',
          suggestionsQuery: const EntryFilterSigneesRepositoryQuery(),
          displayStringResolver: (user) => user.nameEmailDisplayValue,
          filterOutResolver: (user) => user.id!.dbValue,
          submitResolver: (email) => User.externalSignee(email),
          textInputType: TextInputType.emailAddress,
          textCapitalization: TextCapitalization.none,
          allowSubmit: true,
          initialItems: entryFilter?.signee,
        ),
        signatureStatusSelection = CollectionSelection(
          isQuitSelectingOnEmptySelections: false,
          initialItems: entryFilter?.signatureStatus,
        ) {
    titleQueryController.addListener(_checkHasData);
    commentsQueryController.addListener(_checkHasData);
    projectController.collection.addListener(_checkHasData);
    tagController.collection.addListener(_checkHasData);
    sublocationController.collection.addListener(_checkHasData);
    providerController.collection.addListener(_checkHasData);
    authorController.collection.addListener(_checkHasData);
    assigneeController.collection.addListener(_checkHasData);
    signeeController.collection.addListener(_checkHasData);
    signatureStatusSelection.addListener(_checkHasData);

    signatureStatusSelection.isSelecting = true;
  }

  void _checkHasData() {
    hasData.value = titleQueryController.text.isNotEmpty ||
        commentsQueryController.text.isNotEmpty ||
        projectController.collection.value.isNotEmpty ||
        tagController.collection.value.isNotEmpty ||
        sublocationController.collection.value.isNotEmpty ||
        providerController.collection.value.isNotEmpty ||
        authorController.collection.value.isNotEmpty ||
        assigneeController.collection.value.isNotEmpty ||
        signeeController.collection.value.isNotEmpty ||
        signatureStatusSelection.selections.isNotEmpty;
  }

  void dispose() {
    titleQueryController.dispose();
    commentsQueryController.dispose();
    projectController.collection.dispose();
    tagController.collection.dispose();
    sublocationController.collection.dispose();
    providerController.collection.dispose();
    authorController.collection.dispose();
    assigneeController.collection.dispose();
    signeeController.collection.dispose();
    signatureStatusSelection.dispose();
  }
}
