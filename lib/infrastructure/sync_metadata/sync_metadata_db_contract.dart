import 'dart:async';

import 'package:bitacora/infrastructure/db_contract.dart';
import 'package:bitacora/infrastructure/db_repository.dart';
import 'package:sqflite/sqflite.dart';

class SyncMetadataDbContract extends DbContract {
  static const String _ = 'sm_';
  static const String _tableName = 'syncMetadata';

  final String id = '${_}id';
  final String organizationId = '${_}organizationId';
  final String collectionType = '${_}collectionType';
  final String lastSyncTime = '${_}lastSyncTime';
  final String nextPageToken = '${_}nextPageToken';
  final String syncVersion = '${_}syncVersion';

  const SyncMetadataDbContract() : super(_, _tableName);

  @override
  int get initialDbVersion => kDbVersionWithResourceTables;

  @override
  String get create => '''
  CREATE TABLE $_tableName (
    $id INTEGER PRIMARY KEY AUTOINCREMENT,
    $organizationId INTEGER NOT NULL,
    $collectionType INTEGER NOT NULL,
    $lastSyncTime INTEGER,
    $syncVersion INTEGER NOT NULL,
    $nextPageToken TEXT
  )
  ''';

  @override
  FutureOr<void> upgradeOrCreate(
      Database db, int oldVersion, int newVersion) async {
    if (oldVersion < kDbVersionWithSyncMetadataSyncVersionColumn) {
      await db.execute(
        'ALTER TABLE $tableName ADD $syncVersion INTEGER NOT NULL DEFAULT 1',
      );
    }
  }
}
