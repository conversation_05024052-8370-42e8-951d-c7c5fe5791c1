import 'package:bitacora/dev/mock/dev_mock.dart';
import 'package:bitacora/domain/access/access.dart';
import 'package:bitacora/domain/organization/value/organization_active_plan.dart';
import 'package:bitacora/presentation/organization/staff/staff_page.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../mocktail_fallback_values.dart';
import '../_robots/staff_page_test_robot.dart';

void main() {
  setUpAll(() {
    MocktailFallbackValues.ensureInitialized();
  });

  group('$StaffPage tests', () {
    testWidgets('Display Staff', (tester) async {
      final robot = StaffPageTestRobot(tester);

      await robot.pumpWidget();

      robot.verifyStaff();
    });

    testWidgets(
      'Display UserInviteFormIcon when activeUser is Admin',
      (tester) async {
        final organization =
            mockOrganization(activePlan: OrganizationActivePlan.pro);
        final access = Access(permission: AccessPermission(kAccessAdmin));

        final robot = StaffPageTestRobot(tester);

        await robot.pumpWidget(organization: organization, access: access);

        robot.verifyInviteFormIcon(organization, access);
      },
    );

    testWidgets(
      'Display UserInviteFormIcon when activeUser is not Admin',
      (tester) async {
        final organization =
            mockOrganization(activePlan: OrganizationActivePlan.pro);
        final access = Access(permission: AccessPermission(kAccessWrite));

        final robot = StaffPageTestRobot(tester);

        await robot.pumpWidget(organization: organization, access: access);

        robot.verifyInviteFormIcon(organization, access);
      },
    );
  });
}
