import 'package:bitacora/domain/entry/entry.dart';
import 'package:bitacora/domain/entry/value/entry_timer_status.dart';
import 'package:bitacora/domain/extension/extension.dart';
import 'package:bitacora/domain/extension/extension_type.dart';
import 'package:bitacora/domain/inventorylog/inventorylog.dart';
import 'package:bitacora/domain/personnellog/personnellog.dart';
import 'package:bitacora/domain/progresslog/progresslog.dart';
import 'package:bitacora/domain/project/project.dart';
import 'package:bitacora/domain/templatelog/templatelog.dart';
import 'package:bitacora/domain/worklog/worklog.dart';
import 'package:bitacora/presentation/daylog/log_day_list_item_header.dart';
import 'package:bitacora/presentation/project/project_detail_bottom_sheet.dart';
import 'package:bitacora/util/bottom_sheet/bottom_sheet_utils.dart';
import 'package:bitacora/util/date_utils.dart';
import 'package:bitacora/util/list_entry/templatelog_list_entry.dart';
import 'package:bitacora/util/time_utils.dart';
import 'package:flutter/material.dart';
import 'package:bitacora/l10n/app_localizations.dart';
import 'package:intl/intl.dart';

class ListEntry {
  final Entry entry;
  final Project? project;

  const ListEntry(this.entry, this.project);

  String topText(BuildContext context) {
    return _getTopText(context, entry);
  }

  Color? get topTextColor {
    return _getTopTextColor(entry);
  }

  String topRightText(BuildContext context) {
    return _getTopRightText(context, entry);
  }

  String leftText(BuildContext context) {
    final text = _getLeftText(context, entry);
    return text.isNotEmpty ? text : '-';
  }

  String get rightText {
    final text = _getRightText(entry);
    return text.isNotEmpty ? text : '-';
  }

  Widget rightWidget(BuildContext context) {
    final text = Text(
      rightText,
      maxLines: 3,
      softWrap: false,
      overflow: TextOverflow.ellipsis,
      textAlign: TextAlign.right,
      style: const TextStyle(fontSize: 13),
    );
    if (entry.extension!.extensionType == ExtensionType.templatelog) {
      return text;
    }

    return TextButton(
      onPressed: () {
        const BottomSheetUtils().show(
          context,
          ProjectDetailBottomSheet(project: entry.projects.first),
        );
      },
      style: TextButton.styleFrom(
        minimumSize: Size.zero,
        padding: const EdgeInsets.all(2.0),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
      ),
      child: text,
    );
  }

  String get mainText {
    return _getMainText(entry);
  }

  String bottomText(BuildContext context) {
    final comments = entry.comments!.displayValue;
    final fromExtension = _getBottomText(context, entry);
    return '$fromExtension'
        '${comments.isNotEmpty && fromExtension.isNotEmpty ? '\n' : ''}'
        '$comments';
  }

  String _getTopText(BuildContext context, Entry entry) {
    final forExtension = _getTopTextForExtension(context, entry.extension!);
    if (entry.openState != null && entry.openState!.progress!.value > 0) {
      return '${entry.openState!.progress!.displayValue} $forExtension';
    }
    return forExtension;
  }

  String _getTopTextForExtension(BuildContext context, Extension extension) {
    switch (extension.extensionType) {
      case ExtensionType.worklog:
      case ExtensionType.simplelog:
        return '';
      case ExtensionType.inventorylog:
        return _getTopTextForInventorylog(context, extension as Inventorylog);
      case ExtensionType.personnellog:
        return '';
      case ExtensionType.progresslog:
        final progresslog = extension as Progresslog;
        final openEntryTopText =
            _getTopTextForExtension(context, progresslog.entry!.extension!);
        if (progresslog.entry!.openState!.progressive!.value) {
          return '${progresslog.progress!.displayValue} $openEntryTopText';
        }
        return openEntryTopText;
      case ExtensionType.templatelog:
        return const TemplatelogListEntry()
            .getTopText(extension as Templatelog);
      }
  }

  String _getTopTextForInventorylog(
      BuildContext context, Inventorylog inventorylog) {
    if (inventorylog.type == InventorylogType.movement) {
      if (project != null) {
        if (inventorylog.sourceProject!.id == project!.id &&
            inventorylog.destProject!.id == project!.id) {
          return AppLocalizations.of(context)!.movement;
        } else if (inventorylog.sourceProject!.id == project!.id) {
          return '${AppLocalizations.of(context)!.movement}: '
              '${AppLocalizations.of(context)!.outgoing}';
        } else if (inventorylog.destProject!.id == project!.id) {
          return '${AppLocalizations.of(context)!.movement}: '
              '${AppLocalizations.of(context)!.incoming}';
        }
      }
      return AppLocalizations.of(context)!.movement;
    } else if (inventorylog.type == InventorylogType.incoming) {
      return AppLocalizations.of(context)!.incoming;
    } else if (inventorylog.type == InventorylogType.outgoing) {
      final reason = inventorylog.reason!.displayValue;
      if (reason.isNotEmpty) {
        return '${AppLocalizations.of(context)!.outgoing}: $reason';
      }
      return AppLocalizations.of(context)!.outgoing;
    }
    return '';
  }

  Color? _getTopTextColor(Entry entry) {
    if (entry.inventorylog != null) {
      return entry.inventorylog!.type!.color;
    } else if (entry.templatelog != null) {
      return null;
    } else if (entry.progresslog != null) {
      return _getTopTextColor(entry.progresslog!.entry!);
    }

    return null;
  }

  String _getTopRightText(BuildContext context, Entry entry) {
    if (entry.openState?.endDay?.value != entry.openState?.startDay?.value) {
      final date = LogDayListItemHeader.getFormattedDate(
          context, entry.openState!.endDay!);
      return '-> $date';
    }
    return '';
  }

  String _getLeftText(BuildContext context, Entry entry) {
    if (entry.worklog != null) {
      return entry.worklog!.quantity!.displayValue;
    } else if (entry.inventorylog != null) {
      return entry.inventorylog!.quantity!.displayValue;
    } else if (entry.personnellog != null) {
      return _getLeftTextForPersonnellog(context, entry.personnellog!);
    } else if (entry.progresslog != null) {
      return _getLeftText(context, entry.progresslog!.entry!);
    } else if (entry.templatelog != null) {
      return const TemplatelogListEntry().getLeftText(entry.templatelog!);
    }
    return '';
  }

  String _getLeftTextForPersonnellog(
      BuildContext context, Personnellog personnellog) {
    final minutes = personnellog.minutes!.inHours;
    if (minutes.isNotEmpty) {
      return minutes;
    }

    if (personnellog.entrance!.value != null &&
        personnellog.exit!.value != null) {
      final added = personnellog.exit!.value! < personnellog.entrance!.value!
          ? 24 * 60
          : 0;
      return getFormattedHours(
          added + personnellog.exit!.value! - personnellog.entrance!.value!);
    }
    return getFormattedTimeRange(
        personnellog.entrance!.value, personnellog.exit!.value);
  }

  String _getRightText(Entry entry) {
    switch (entry.extension!.extensionType) {
      case ExtensionType.worklog:
        return _getRightTextForWorklog(entry.worklog!);
      case ExtensionType.inventorylog:
        return _getRightTextForInventorylog(entry.inventorylog!);
      case ExtensionType.personnellog:
        return _getRightTextForPersonnellog(entry.personnellog!);
      case ExtensionType.progresslog:
        return _getRightText(entry.progresslog!.entry!);
      case ExtensionType.templatelog:
        return const TemplatelogListEntry().getRightText(entry.templatelog!);
      default:
        throw 'Unexpected entry extension type '
            '<${entry.extension!.extensionType}>';
    }
  }

  String _getRightTextForWorklog(Worklog worklog) {
    if (project != null) {
      return worklog.sublocation!.displayValue;
    }
    return worklog.project!.name!.displayValue;
  }

  String _getRightTextForInventorylog(Inventorylog inventorylog) {
    if (project != null) {
      if (inventorylog.destProject?.id == project!.id) {
        return inventorylog.destSublocation!.displayValue;
      }
      return inventorylog.sourceSublocation!.displayValue;
    }
    if (inventorylog.destProject != null) {
      return inventorylog.destProject!.name!.displayValue;
    }
    return inventorylog.sourceProject!.name!.displayValue;
  }

  String _getRightTextForPersonnellog(Personnellog personnellog) {
    if (project != null) {
      return personnellog.sublocation!.displayValue;
    }
    return personnellog.project!.name!.displayValue;
  }

  String _getMainText(Entry entry) {
    if (entry.worklog != null) {
      return entry.worklog!.title!.displayValue;
    } else if (entry.inventorylog != null) {
      return entry.inventorylog!.itemName!.displayValue;
    } else if (entry.personnellog != null) {
      return entry.personnellog!.name!.displayValue;
    } else if (entry.progresslog != null) {
      return _getMainText(entry.progresslog!.entry!);
    } else if (entry.templatelog != null) {
      return const TemplatelogListEntry().getMainText(entry.templatelog!);
    }
    throw Exception('Entry without title');
  }

  String _getBottomText(BuildContext context, Entry entry) {
    switch (entry.extension!.extensionType) {
      case ExtensionType.inventorylog:
        return _getBottomTextForInventorylog(entry.inventorylog!);
      case ExtensionType.personnellog:
        return _getBottomTextForPersonnellog(context, entry.personnellog!);
      case ExtensionType.progresslog:
        final comments = entry.progresslog!.entry!.comments!.displayValue;
        final proxy = _getBottomText(context, entry.progresslog!.entry!);
        return '$proxy'
            '${comments.isNotEmpty && proxy.isNotEmpty ? '\n' : ''}'
            '$comments';
      case ExtensionType.templatelog:
        return const TemplatelogListEntry().getBottomText(entry.templatelog!);
      default:
        return '';
    }
  }

  String _getBottomTextForInventorylog(Inventorylog inventorylog) {
    final left = _getInventoryLeft(inventorylog);
    final right = _getInventoryRight(inventorylog);
    return '$left->$right';
  }

  String _getInventoryLeft(Inventorylog inventorylog) {
    switch (inventorylog.type!.value) {
      case InventorylogTypeValue.incoming:
        return inventorylog.provider!.displayValue;
      case InventorylogTypeValue.movement:
        if (inventorylog.destProject!.id == inventorylog.sourceProject!.id ||
            project?.id == inventorylog.sourceProject!.id) {
          return inventorylog.sourceSublocation!.value == null
              ? inventorylog.sourceProject!.name!.displayValue
              : inventorylog.sourceSublocation!.displayValue;
        }
        return inventorylog.sourceProject!.name!.displayValue;
      case InventorylogTypeValue.outgoing:
        return inventorylog.sourceSublocation!.value == null
            ? inventorylog.sourceProject!.name!.displayValue
            : inventorylog.sourceSublocation!.displayValue;
      }
  }

  String _getInventoryRight(Inventorylog inventorylog) {
    switch (inventorylog.type!.value) {
      case InventorylogTypeValue.incoming:
        return inventorylog.destSublocation!.value == null
            ? inventorylog.destProject!.name!.displayValue
            : inventorylog.destSublocation!.displayValue;
      case InventorylogTypeValue.movement:
        if (inventorylog.destProject!.id == inventorylog.sourceProject!.id ||
            project?.id == inventorylog.destProject!.id) {
          return inventorylog.destSublocation!.value == null
              ? inventorylog.destProject!.name!.displayValue
              : inventorylog.destSublocation!.displayValue;
        }
        return inventorylog.destProject!.name!.displayValue;
      case InventorylogTypeValue.outgoing:
        return inventorylog.provider!.displayValue;
      }
  }

  String _getBottomTextForPersonnellog(
      BuildContext context, Personnellog personnellog) {
    if (personnellog.entrance!.value != null &&
        personnellog.exit!.value != null) {
      return getFormattedTimeRange(
          personnellog.entrance!.value, personnellog.exit!.value);
    }
    return '';
  }

  String? timerText(BuildContext context, Entry entry) {
    if (entry.timerStatus == null ||
        entry.startDate?.value == null ||
        entry.startTime?.value == null) {
      return null;
    }

    final startDate = getDateTimeFromLogDay(LogDay(entry.startDate!.value!));
    final startDateTime = DateTime(
      startDate.year,
      startDate.month,
      startDate.day,
      entry.startTime!.hour!,
      entry.startTime!.minute!,
      entry.startTime!.second!,
    );

    final languageCode = Localizations.localeOf(context).languageCode;
    if (entry.timerStatus == EntryTimerStatus.started) {
      return '${DateFormat.jms(languageCode).format(startDateTime)} ->';
    } else if (entry.timerStatus == EntryTimerStatus.finished) {
      if (entry.endDate == null || entry.endTime == null) {
        return null;
      }

      final endDate = getDateTimeFromLogDay(LogDay(entry.endDate!.value!));
      final endDateTime = DateTime(
        endDate.year,
        endDate.month,
        endDate.day,
        entry.endTime!.hour!,
        entry.endTime!.minute!,
        entry.endTime!.second!,
      );
      return '${DateFormat.jms(languageCode).format(startDateTime)} '
          '-> ${DateFormat.jms(languageCode).format(endDateTime)}';
    }
    return null;
  }
}
