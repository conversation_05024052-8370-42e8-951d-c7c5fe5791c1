import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/tag/tag.dart';
import 'package:bitacora/presentation/entry/filter/tag/entry_filter_tag_fields.dart';

class EntryFilterTagsRepositoryQuery extends RepositoryQuery<List<Tag>> {
  const EntryFilterTagsRepositoryQuery();

  @override
  Future<List<Tag>> run(RepositoryQueryContext context) =>
      context.db.tag.searchAll(context);

  @override
  Fields? fields(Repository db) => entryFilterTagFields(db);
}
