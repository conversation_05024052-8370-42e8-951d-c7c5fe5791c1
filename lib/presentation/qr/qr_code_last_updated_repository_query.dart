import 'package:bitacora/domain/common/repository.dart';
import 'package:bitacora/domain/common/repository_query.dart';
import 'package:bitacora/domain/common/repository_query_context.dart';
import 'package:bitacora/domain/qr_code/qr_code.dart';

class QrCodeLastUpdatedRepositoryQuery extends RepositoryQuery<QrCode?> {
  final QrCode qrCode;

  QrCodeLastUpdatedRepositoryQuery({required this.qrCode});

  @override
  Future<QrCode?> run(RepositoryQueryContext context) =>
      context.db.qrCode.getLastUpdated(context, qrCode);

  @override
  Fields fields(Repository db) => db.qrCode.fieldsBuilder
      .remoteId()
      .payload()
      .updatedAt()
      .organization(db.organization.fieldsBuilder.remoteId().build())
      .build();
}
