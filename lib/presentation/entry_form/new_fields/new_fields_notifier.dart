import 'package:flutter/widgets.dart';

class NewFieldsNotifier extends ChangeNotifier {
  final Set<Key> _newFields = <Key>{};

  NewFieldsNotifier();

  void markField(Key key, bool asNew) {
    final prev = hasNewFields();
    if (asNew) {
      _newFields.add(key);
    } else {
      _newFields.remove(key);
    }
    final curr = hasNewFields();
    if (curr != prev) {
      notifyListeners();
    }
  }

  void clear() {
    _newFields.clear();
    notifyListeners();
  }

  bool hasNewFields() {
    return _newFields.isNotEmpty;
  }
}
